const http = require('http');

/**
 * 健康检查脚本
 * 用于Docker容器健康检查
 */

const options = {
  hostname: 'localhost',
  port: process.env.PORT || 3000,
  path: '/api/health',
  method: 'GET',
  timeout: 3000
};

const request = http.request(options, (response) => {
  console.log(`Health check status: ${response.statusCode}`);
  
  if (response.statusCode === 200) {
    process.exit(0); // 健康
  } else {
    process.exit(1); // 不健康
  }
});

request.on('error', (error) => {
  console.error('Health check failed:', error.message);
  process.exit(1); // 不健康
});

request.on('timeout', () => {
  console.error('Health check timeout');
  request.destroy();
  process.exit(1); // 不健康
});

request.setTimeout(3000);
request.end();
