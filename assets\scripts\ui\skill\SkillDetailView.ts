/**
 * 技能详情视图组件
 * 负责显示技能的详细信息
 */

import { _decorator, Node, Sprite, Label, RichText, ProgressBar, Layout, Color } from 'cc';
import { BaseUIComponent } from '../base/BaseUIComponent';
import { ISkillData, IPlayerSkill, ISkillEffect, SkillDamageType, SkillEffectType } from '../../config/interfaces/ISkillData';
import { ResourceManager } from '../../managers/ResourceManager';

const { ccclass, property } = _decorator;

@ccclass('SkillDetailView')
export class SkillDetailView extends BaseUIComponent {
    
    @property({ type: Node, tooltip: '技能图标容器' })
    public iconContainer: Node = null!;
    
    @property({ type: Sprite, tooltip: '技能图标' })
    public skillIcon: Sprite = null!;
    
    @property({ type: Label, tooltip: '技能名称' })
    public skillNameLabel: Label = null!;
    
    @property({ type: Label, tooltip: '技能等级' })
    public skillLevelLabel: Label = null!;
    
    @property({ type: RichText, tooltip: '技能描述' })
    public skillDescText: RichText = null!;
    
    @property({ type: Label, tooltip: '法力消耗' })
    public manaCostLabel: Label = null!;
    
    @property({ type: Label, tooltip: '施法时间' })
    public castTimeLabel: Label = null!;
    
    @property({ type: Label, tooltip: '冷却时间' })
    public cooldownLabel: Label = null!;
    
    @property({ type: Label, tooltip: '伤害类型' })
    public damageTypeLabel: Label = null!;
    
    @property({ type: Label, tooltip: '目标类型' })
    public targetTypeLabel: Label = null!;
    
    @property({ type: Label, tooltip: '基础伤害' })
    public baseDamageLabel: Label = null!;
    
    @property({ type: ProgressBar, tooltip: '经验进度条' })
    public expProgressBar: ProgressBar = null!;
    
    @property({ type: Label, tooltip: '经验标签' })
    public expLabel: Label = null!;
    
    @property({ type: Node, tooltip: '技能效果容器' })
    public effectsContainer: Node = null!;
    
    @property({ type: Node, tooltip: '学习要求容器' })
    public requirementsContainer: Node = null!;
    
    // 当前显示的技能数据
    private _currentSkillData: ISkillData | null = null;
    private _currentPlayerSkill: IPlayerSkill | null = null;

    protected onComponentLoad(): void {
        console.log('⚔️ SkillDetailView: 组件加载');
        
        // 初始化为空状态
        this.clearDisplay();
    }

    protected onComponentEnable(): void {
        console.log('⚔️ SkillDetailView: 组件启用');
    }

    protected bindEvents(): void {
        // 技能详情视图通常不需要绑定事件
    }

    protected unbindEvents(): void {
        // 技能详情视图通常不需要解绑事件
    }

    /**
     * 初始化技能详情视图
     */
    public async initialize(): Promise<void> {
        console.log('⚔️ 初始化技能详情视图');
        
        // 设置效果容器布局
        this.setupEffectsLayout();
        
        // 设置要求容器布局
        this.setupRequirementsLayout();
        
        console.log('✅ 技能详情视图初始化完成');
    }

    /**
     * 设置效果容器布局
     */
    private setupEffectsLayout(): void {
        if (!this.effectsContainer) {
            return;
        }
        
        let layout = this.effectsContainer.getComponent(Layout);
        if (!layout) {
            layout = this.effectsContainer.addComponent(Layout);
        }
        
        layout.type = Layout.Type.VERTICAL;
        layout.spacingY = 5;
        layout.verticalDirection = Layout.VerticalDirection.TOP_TO_BOTTOM;
    }

    /**
     * 设置要求容器布局
     */
    private setupRequirementsLayout(): void {
        if (!this.requirementsContainer) {
            return;
        }
        
        let layout = this.requirementsContainer.getComponent(Layout);
        if (!layout) {
            layout = this.requirementsContainer.addComponent(Layout);
        }
        
        layout.type = Layout.Type.VERTICAL;
        layout.spacingY = 3;
        layout.verticalDirection = Layout.VerticalDirection.TOP_TO_BOTTOM;
    }

    /**
     * 显示技能详情
     */
    public async showSkillDetail(skillData: ISkillData, playerSkill: IPlayerSkill | null = null): Promise<void> {
        console.log(`⚔️ 显示技能详情: ${skillData.name}`);
        
        this._currentSkillData = skillData;
        this._currentPlayerSkill = playerSkill;
        
        // 更新基础信息
        await this.updateBasicInfo();
        
        // 更新属性信息
        this.updateProperties();
        
        // 更新经验信息
        this.updateExperience();
        
        // 更新技能效果
        this.updateEffects();
        
        // 更新学习要求
        this.updateRequirements();
        
        console.log(`✅ 技能详情显示完成: ${skillData.name}`);
    }

    /**
     * 更新基础信息
     */
    private async updateBasicInfo(): Promise<void> {
        const skillData = this._currentSkillData!;
        const playerSkill = this._currentPlayerSkill;
        
        // 更新技能图标
        await this.updateSkillIcon(skillData);
        
        // 更新技能名称
        if (this.skillNameLabel) {
            this.skillNameLabel.string = skillData.name;
        }
        
        // 更新技能等级
        if (this.skillLevelLabel) {
            if (playerSkill) {
                this.skillLevelLabel.string = `等级 ${playerSkill.level}/${skillData.maxLevel}`;
                this.skillLevelLabel.color = playerSkill.level >= skillData.maxLevel ? 
                    new Color(255, 215, 0, 255) : new Color(255, 255, 255, 255);
            } else {
                this.skillLevelLabel.string = `等级 1/${skillData.maxLevel}`;
                this.skillLevelLabel.color = new Color(150, 150, 150, 255);
            }
        }
        
        // 更新技能描述
        if (this.skillDescText) {
            this.skillDescText.string = this.formatSkillDescription(skillData.description, playerSkill);
        }
    }

    /**
     * 更新技能图标
     */
    private async updateSkillIcon(skillData: ISkillData): Promise<void> {
        if (!this.skillIcon) {
            return;
        }
        
        try {
            const iconPath = `textures/skills/${skillData.id}`;
            const spriteFrame = await ResourceManager.getInstance().loadSpriteFrame(iconPath);
            this.skillIcon.spriteFrame = spriteFrame;
            
        } catch (error) {
            console.warn(`⚠️ 加载技能图标失败: ${skillData.id}`, error);
            this.skillIcon.spriteFrame = null;
        }
    }

    /**
     * 格式化技能描述
     */
    private formatSkillDescription(description: string, playerSkill: IPlayerSkill | null): string {
        let formattedDesc = description;
        
        // 如果有玩家技能数据，可以根据等级动态计算数值
        if (playerSkill) {
            // TODO: 实现基于等级的数值替换
            // 例如：将 {damage} 替换为实际伤害值
        }
        
        return formattedDesc;
    }

    /**
     * 更新属性信息
     */
    private updateProperties(): void {
        const skillData = this._currentSkillData!;
        const playerSkill = this._currentPlayerSkill;
        
        // 更新法力消耗
        if (this.manaCostLabel) {
            this.manaCostLabel.string = `法力消耗: ${skillData.manaCost}`;
        }
        
        // 更新施法时间
        if (this.castTimeLabel) {
            if (skillData.castTime > 0) {
                this.castTimeLabel.string = `施法时间: ${skillData.castTime}秒`;
            } else {
                this.castTimeLabel.string = '施法时间: 瞬发';
            }
        }
        
        // 更新冷却时间
        if (this.cooldownLabel) {
            this.cooldownLabel.string = `冷却时间: ${skillData.cooldown}秒`;
        }
        
        // 更新伤害类型
        if (this.damageTypeLabel) {
            this.damageTypeLabel.string = `伤害类型: ${this.getDamageTypeText(skillData.damageType)}`;
            this.damageTypeLabel.color = this.getDamageTypeColor(skillData.damageType);
        }
        
        // 更新目标类型
        if (this.targetTypeLabel) {
            this.targetTypeLabel.string = `目标类型: ${this.getTargetTypeText(skillData.targetType)}`;
        }
        
        // 更新基础伤害
        if (this.baseDamageLabel) {
            const damage = this.calculateSkillDamage(skillData, playerSkill);
            this.baseDamageLabel.string = `基础伤害: ${damage}`;
        }
    }

    /**
     * 更新经验信息
     */
    private updateExperience(): void {
        const skillData = this._currentSkillData!;
        const playerSkill = this._currentPlayerSkill;
        
        if (!playerSkill) {
            // 隐藏经验相关UI
            if (this.expProgressBar) {
                this.expProgressBar.node.active = false;
            }
            if (this.expLabel) {
                this.expLabel.node.active = false;
            }
            return;
        }
        
        // 更新经验进度条
        if (this.expProgressBar) {
            if (playerSkill.level >= skillData.maxLevel) {
                this.expProgressBar.progress = 1;
                this.expProgressBar.node.active = true;
            } else {
                const progress = playerSkill.experienceRequired > 0 ? 
                    playerSkill.experience / playerSkill.experienceRequired : 0;
                this.expProgressBar.progress = progress;
                this.expProgressBar.node.active = true;
            }
        }
        
        // 更新经验标签
        if (this.expLabel) {
            if (playerSkill.level >= skillData.maxLevel) {
                this.expLabel.string = '已满级';
                this.expLabel.color = new Color(255, 215, 0, 255);
            } else {
                this.expLabel.string = `经验: ${playerSkill.experience}/${playerSkill.experienceRequired}`;
                this.expLabel.color = new Color(255, 255, 255, 255);
            }
            this.expLabel.node.active = true;
        }
    }

    /**
     * 更新技能效果
     */
    private updateEffects(): void {
        const skillData = this._currentSkillData!;
        
        if (!this.effectsContainer || !skillData.effects || skillData.effects.length === 0) {
            if (this.effectsContainer) {
                this.effectsContainer.active = false;
            }
            return;
        }
        
        // 清理现有效果显示
        this.clearEffectsDisplay();
        
        // 显示技能效果
        for (const effect of skillData.effects) {
            this.createEffectDisplay(effect);
        }
        
        this.effectsContainer.active = true;
    }

    /**
     * 清理效果显示
     */
    private clearEffectsDisplay(): void {
        if (!this.effectsContainer) {
            return;
        }
        
        // 移除所有子节点
        this.effectsContainer.removeAllChildren();
    }

    /**
     * 创建效果显示
     */
    private createEffectDisplay(effect: ISkillEffect): void {
        // TODO: 创建效果显示节点
        // 这里简化处理，实际项目中可能需要创建专门的效果显示组件
        
        const effectText = this.formatEffectText(effect);
        console.log(`⚔️ 技能效果: ${effectText}`);
    }

    /**
     * 格式化效果文本
     */
    private formatEffectText(effect: ISkillEffect): string {
        const effectTypeName = this.getEffectTypeText(effect.type);
        const durationText = effect.duration > 0 ? `，持续${effect.duration}秒` : '';
        
        return `${effectTypeName}: ${effect.value}${durationText}`;
    }

    /**
     * 更新学习要求
     */
    private updateRequirements(): void {
        const skillData = this._currentSkillData!;
        const playerSkill = this._currentPlayerSkill;
        
        if (!this.requirementsContainer || playerSkill?.learned) {
            if (this.requirementsContainer) {
                this.requirementsContainer.active = false;
            }
            return;
        }
        
        // 清理现有要求显示
        this.clearRequirementsDisplay();
        
        // 显示学习要求
        this.createRequirementDisplay(`等级要求: ${skillData.requirements.level}`);
        
        // 显示前置技能要求
        for (const skillReq of skillData.requirements.skills) {
            this.createRequirementDisplay(`前置技能: ${skillReq.skillId} Lv.${skillReq.level}`);
        }
        
        // 显示物品要求
        for (const itemReq of skillData.requirements.items) {
            this.createRequirementDisplay(`所需物品: ${itemReq.itemId} x${itemReq.quantity}`);
        }
        
        this.requirementsContainer.active = true;
    }

    /**
     * 清理要求显示
     */
    private clearRequirementsDisplay(): void {
        if (!this.requirementsContainer) {
            return;
        }
        
        this.requirementsContainer.removeAllChildren();
    }

    /**
     * 创建要求显示
     */
    private createRequirementDisplay(requirementText: string): void {
        // TODO: 创建要求显示节点
        // 这里简化处理
        console.log(`⚔️ 学习要求: ${requirementText}`);
    }

    /**
     * 清空显示
     */
    public clearDisplay(): void {
        this._currentSkillData = null;
        this._currentPlayerSkill = null;
        
        // 清空所有显示内容
        if (this.skillIcon) {
            this.skillIcon.spriteFrame = null;
        }
        
        if (this.skillNameLabel) {
            this.skillNameLabel.string = '';
        }
        
        if (this.skillLevelLabel) {
            this.skillLevelLabel.string = '';
        }
        
        if (this.skillDescText) {
            this.skillDescText.string = '选择一个技能查看详情';
        }
        
        // 隐藏其他UI元素
        const uiElements = [
            this.manaCostLabel, this.castTimeLabel, this.cooldownLabel,
            this.damageTypeLabel, this.targetTypeLabel, this.baseDamageLabel,
            this.expProgressBar?.node, this.expLabel,
            this.effectsContainer, this.requirementsContainer
        ];
        
        for (const element of uiElements) {
            if (element) {
                element.active = false;
            }
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 获取伤害类型文本
     */
    private getDamageTypeText(damageType: SkillDamageType): string {
        switch (damageType) {
            case SkillDamageType.Physical: return '物理';
            case SkillDamageType.Magical: return '魔法';
            case SkillDamageType.True: return '真实';
            case SkillDamageType.Healing: return '治疗';
            default: return '未知';
        }
    }

    /**
     * 获取伤害类型颜色
     */
    private getDamageTypeColor(damageType: SkillDamageType): Color {
        switch (damageType) {
            case SkillDamageType.Physical: return new Color(255, 100, 100, 255);
            case SkillDamageType.Magical: return new Color(100, 100, 255, 255);
            case SkillDamageType.True: return new Color(255, 255, 100, 255);
            case SkillDamageType.Healing: return new Color(100, 255, 100, 255);
            default: return new Color(255, 255, 255, 255);
        }
    }

    /**
     * 获取目标类型文本
     */
    private getTargetTypeText(targetType: any): string {
        // TODO: 实现目标类型文本转换
        return targetType.toString();
    }

    /**
     * 获取效果类型文本
     */
    private getEffectTypeText(effectType: SkillEffectType): string {
        switch (effectType) {
            case SkillEffectType.Damage: return '伤害';
            case SkillEffectType.Heal: return '治疗';
            case SkillEffectType.Buff: return '增益';
            case SkillEffectType.Debuff: return '减益';
            case SkillEffectType.Stun: return '眩晕';
            case SkillEffectType.Slow: return '减速';
            case SkillEffectType.Poison: return '中毒';
            case SkillEffectType.Burn: return '燃烧';
            case SkillEffectType.Shield: return '护盾';
            default: return '未知';
        }
    }

    /**
     * 计算技能伤害
     */
    private calculateSkillDamage(skillData: ISkillData, playerSkill: IPlayerSkill | null): number {
        const baseMultiplier = skillData.baseDamageMultiplier;
        const level = playerSkill ? playerSkill.level : 1;
        
        // 简化的伤害计算
        return Math.floor(baseMultiplier * 100 * (1 + (level - 1) * 0.1));
    }

    // ==================== 公共API ====================

    /**
     * 获取当前显示的技能数据
     */
    public getCurrentSkillData(): ISkillData | null {
        return this._currentSkillData;
    }

    /**
     * 获取当前显示的玩家技能数据
     */
    public getCurrentPlayerSkill(): IPlayerSkill | null {
        return this._currentPlayerSkill;
    }

    /**
     * 检查是否有技能详情显示
     */
    public hasSkillDetail(): boolean {
        return this._currentSkillData !== null;
    }
}
