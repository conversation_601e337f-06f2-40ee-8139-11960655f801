@echo off
REM Copyright (c) 2025 "放置". All rights reserved.
REM XML配置一键转换脚本

echo.
echo ========================================
echo    策划配置XML转换工具
echo ========================================
echo.

REM 设置路径
set XML_DIR=..\xml-configs
set OUTPUT_DIR=..\..\..\assets\configs
set CONVERTER=xml-to-json.js

echo 🔧 开始转换XML配置文件...
echo.
echo 📁 XML源目录: %XML_DIR%
echo 📁 输出目录: %OUTPUT_DIR%
echo.

REM 检查Node.js是否安装
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Node.js，请先安装Node.js
    pause
    exit /b 1
)

REM 检查xml2js依赖
if not exist "..\..\..\node_modules\xml2js" (
    echo ⚠️  警告: 未找到xml2js依赖，正在安装...
    cd ..\..\..
    npm install xml2js --save-dev
    cd tools\planning-configs\conversion-tools
)

REM 检查XML目录是否存在
if not exist "%XML_DIR%" (
    echo ❌ 错误: XML配置目录不存在: %XML_DIR%
    echo 💡 请确保在 %XML_DIR% 目录中放置XML配置文件
    pause
    exit /b 1
)

REM 检查是否有XML文件
dir "%XML_DIR%\*.xml" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  警告: 在 %XML_DIR% 中未找到XML文件
    echo 💡 请确保XML配置文件存在
    pause
    exit /b 1
)

REM 创建输出目录
if not exist "%OUTPUT_DIR%" (
    echo 📁 创建输出目录: %OUTPUT_DIR%
    mkdir "%OUTPUT_DIR%"
)

REM 执行转换
echo 🚀 开始转换...
echo.
node "%CONVERTER%" "%XML_DIR%" "%OUTPUT_DIR%"

REM 检查转换结果
if errorlevel 1 (
    echo.
    echo ❌ 转换过程中出现错误
    echo 💡 请检查XML文件格式是否正确
    pause
    exit /b 1
) else (
    echo.
    echo ✅ XML转换完成！
    echo 📍 JSON配置文件已生成到: %OUTPUT_DIR%
    echo.
    echo 🎮 现在可以在游戏中使用新的配置了！
)

echo.
echo ========================================
echo    转换完成
echo ========================================
echo.
pause
