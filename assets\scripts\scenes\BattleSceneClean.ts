/**
 * 战斗场景控制器 - 简化版
 * 专门用于UI交互测试，移除了所有测试脚本
 */

import { _decorator, Component, Node, Canvas, find } from 'cc';
import { UIManager } from '../managers/UIManager';
import { MobileUIInputHandler } from '../ui/input/MobileUIInputHandler';
import { EventManager } from '../managers/EventManager';

const { ccclass, property } = _decorator;

@ccclass('BattleSceneClean')
export class BattleSceneClean extends Component {
    
    @property({ type: Node, tooltip: 'UI根节点' })
    public uiRoot: Node | null = null;
    
    @property({ type: Node, tooltip: '游戏HUD节点' })
    public gameHUD: Node | null = null;
    
    // 私有属性
    private _uiManager: UIManager | null = null;
    private _inputHandler: MobileUIInputHandler | null = null;

    protected onLoad(): void {
        console.log('⚔️ 战斗场景加载 (简化版)');
        
        // 查找UI根节点
        this.findUINodes();
        
        // 初始化UI系统
        this.initializeUISystem();
        
        // 初始化输入处理
        this.initializeInputHandler();
    }

    protected start(): void {
        console.log('⚔️ 战斗场景开始');
        console.log('✅ UI交互测试环境已准备就绪');
        
        // 显示简单的使用说明
        this.showUsageInstructions();
        
        // 初始化游戏HUD
        this.initializeGameHUD();
    }

    protected onDestroy(): void {
        console.log('⚔️ 战斗场景销毁');
        this.cleanup();
    }

    /**
     * 查找UI节点
     */
    private findUINodes(): void {
        // 查找Canvas作为UI根节点
        if (!this.uiRoot) {
            const canvas = find('Canvas');
            if (canvas) {
                this.uiRoot = canvas;
                console.log('✅ 找到UI根节点: Canvas');
            } else {
                console.warn('⚠️ 未找到Canvas节点');
            }
        }
        
        // 查找游戏HUD节点
        if (!this.gameHUD && this.uiRoot) {
            this.gameHUD = this.uiRoot.getChildByName('GameHUD');
            if (this.gameHUD) {
                console.log('✅ 找到游戏HUD节点');
            }
        }
    }

    /**
     * 初始化UI系统
     */
    private async initializeUISystem(): Promise<void> {
        try {
            // 获取UI管理器实例
            this._uiManager = UIManager.getInstance();
            
            if (this._uiManager) {
                console.log('✅ UI管理器已初始化');
                
                // 获取UI统计信息
                const stats = this._uiManager.getUIStats();
                console.log('📊 UI系统状态:', {
                    totalPanels: stats.totalPanels,
                    activePanels: stats.activePanels,
                    visiblePanels: stats.visiblePanels
                });
            } else {
                console.warn('⚠️ UI管理器初始化失败');
            }
            
        } catch (error) {
            console.error('❌ UI系统初始化失败:', error);
        }
    }

    /**
     * 初始化输入处理器
     */
    private initializeInputHandler(): void {
        try {
            // 添加移动端输入处理器组件
            this._inputHandler = this.node.addComponent(MobileUIInputHandler);
            
            if (this._inputHandler) {
                // 设置触摸阈值
                this._inputHandler.setTouchThresholds(10, 500, 50);
                
                // 启用调试模式（开发时）
                this._inputHandler.setDebugMode(true);
                
                console.log('✅ 移动端输入处理器已初始化');
                
                // 监听触摸手势事件
                this.setupInputEventListeners();
            }
            
        } catch (error) {
            console.error('❌ 输入处理器初始化失败:', error);
        }
    }

    /**
     * 设置输入事件监听
     */
    private setupInputEventListeners(): void {
        const eventManager = EventManager.getInstance();

        // 监听触摸手势事件
        eventManager.on('mobile_touch_gesture', (eventData) => {
            console.log('👆 触摸手势:', eventData.type, eventData.position);
        });

        console.log('✅ 输入事件监听已设置');
    }



    /**
     * 初始化游戏HUD
     */
    private initializeGameHUD(): void {
        if (this.gameHUD) {
            // 确保HUD可见
            this.gameHUD.active = true;
            console.log('✅ 游戏HUD已激活');
        } else {
            console.log('ℹ️ 未找到游戏HUD节点，将使用默认UI');
        }
    }

    /**
     * 显示使用说明
     */
    private showUsageInstructions(): void {
        console.log('\n📱 ========== UI交互测试环境 ==========');
        console.log('🎯 这是一个简化的战斗场景，专门用于UI交互测试');
        console.log('');
        console.log('📱 触摸操作:');
        console.log('   • 单击 - 基础交互操作');
        console.log('   • 长按 - 查看详细信息');
        console.log('   • 滑动 - 切换面板或滚动');
        console.log('   • 双击 - 快速操作');
        console.log('');
        console.log('🐛 调试快捷键 (开发模式):');
        console.log('   • I键 - 切换背包面板');
        console.log('   • K键 - 切换技能面板');
        console.log('   • M键 - 切换主菜单');
        console.log('   • ESC键 - 关闭当前面板');
        console.log('   • F1键 - 显示帮助信息');
        console.log('');
        console.log('💡 提示: 这些快捷键仅在开发模式下可用');
        console.log('💡 正式版本将只支持触摸操作');
        console.log('🎮 =====================================');
    }

    /**
     * 清理资源
     */
    private cleanup(): void {
        // 清理事件监听
        const eventManager = EventManager.getInstance();
        eventManager.off('mobile_touch_gesture');
        
        console.log('🧹 战斗场景资源已清理');
    }

    // ==================== 公共API ====================

    /**
     * 显示UI面板
     */
    public showUIPanel(panelType: string): void {
        if (this._uiManager) {
            console.log(`📋 显示面板: ${panelType}`);
            // 这里可以调用UIManager的showPanel方法
        }
    }

    /**
     * 隐藏UI面板
     */
    public hideUIPanel(panelType: string): void {
        if (this._uiManager) {
            console.log(`📋 隐藏面板: ${panelType}`);
            // 这里可以调用UIManager的hidePanel方法
        }
    }

    /**
     * 获取UI系统状态
     */
    public getUIStatus(): any {
        if (this._uiManager) {
            return this._uiManager.getUIStats();
        }
        return null;
    }

    /**
     * 获取输入处理器状态
     */
    public getInputStatus(): any {
        if (this._inputHandler) {
            return this._inputHandler.getTouchStats();
        }
        return null;
    }

    /**
     * 切换调试模式
     */
    public toggleDebugMode(): void {
        if (this._inputHandler) {
            const currentStats = this._inputHandler.getTouchStats();
            const newDebugMode = !currentStats.debugMode;
            this._inputHandler.setDebugMode(newDebugMode);
            
            console.log(`🐛 调试模式: ${newDebugMode ? '启用' : '禁用'}`);
        }
    }

    /**
     * 显示系统信息
     */
    public showSystemInfo(): void {
        console.log('\n💻 ========== 系统信息 ==========');
        
        // UI系统信息
        const uiStatus = this.getUIStatus();
        if (uiStatus) {
            console.log('🎨 UI系统状态:');
            console.log(`   总面板数: ${uiStatus.totalPanels}`);
            console.log(`   活跃面板: ${uiStatus.activePanels}`);
            console.log(`   可见面板: ${uiStatus.visiblePanels}`);
            console.log(`   缓存预制体: ${uiStatus.cachedPrefabs}`);
        }
        
        // 输入系统信息
        const inputStatus = this.getInputStatus();
        if (inputStatus) {
            console.log('📱 输入系统状态:');
            console.log(`   单击阈值: ${inputStatus.tapThreshold}px`);
            console.log(`   长按阈值: ${inputStatus.longPressThreshold}ms`);
            console.log(`   滑动阈值: ${inputStatus.swipeThreshold}px`);
            console.log(`   调试模式: ${inputStatus.debugMode ? '启用' : '禁用'}`);
        }
        
        // 性能信息
        console.log('⚡ 性能信息:');
        console.log(`   当前时间: ${Date.now()}`);
        console.log(`   性能时间戳: ${performance.now().toFixed(2)}ms`);
        
        if ((performance as any).memory) {
            const memory = (performance as any).memory;
            const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
            const totalMB = Math.round(memory.totalJSHeapSize / 1024 / 1024);
            console.log(`   内存使用: ${usedMB}MB / ${totalMB}MB`);
        }
        
        console.log('💻 ==============================');
    }

    /**
     * 运行简单的UI测试
     */
    public runSimpleUITest(): void {
        console.log('\n🧪 ========== 简单UI测试 ==========');
        
        try {
            // 测试UI管理器
            if (this._uiManager) {
                console.log('✅ UI管理器: 正常');
            } else {
                console.log('❌ UI管理器: 未初始化');
            }
            
            // 测试输入处理器
            if (this._inputHandler) {
                console.log('✅ 输入处理器: 正常');
            } else {
                console.log('❌ 输入处理器: 未初始化');
            }
            
            // 测试事件系统
            const eventManager = EventManager.getInstance();
            if (eventManager) {
                console.log('✅ 事件管理器: 正常');
            } else {
                console.log('❌ 事件管理器: 未初始化');
            }
            
            console.log('🎉 简单UI测试完成');
            
        } catch (error) {
            console.error('❌ UI测试失败:', error);
        }
        
        console.log('🧪 ============================');
    }
}
