{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}, "disabled": false, "alwaysAllow": []}, "playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest", "--browser=chromium", "--headless", "--viewport-size=1280,720"], "alwaysAllow": ["browser_navigate"]}, "sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "cocos-creator": {"url": "http://localhost:3000"}}}