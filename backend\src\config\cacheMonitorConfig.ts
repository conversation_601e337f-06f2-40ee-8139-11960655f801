import { CacheAlertConfig } from '../utils/cacheMonitor';

/**
 * 缓存监控配置管理器
 */
export class CacheMonitorConfig {
  private static instance: CacheMonitorConfig;
  private config: CacheMonitorConfiguration;

  private constructor() {
    this.config = this.loadConfiguration();
  }

  public static getInstance(): CacheMonitorConfig {
    if (!CacheMonitorConfig.instance) {
      CacheMonitorConfig.instance = new CacheMonitorConfig();
    }
    return CacheMonitorConfig.instance;
  }

  /**
   * 加载配置
   */
  private loadConfiguration(): CacheMonitorConfiguration {
    return {
      monitoring: {
        enabled: process.env['CACHE_MONITORING_ENABLED'] !== 'false',
        metricsInterval: parseInt(process.env['CACHE_METRICS_INTERVAL'] || '60000'), // 1分钟
        alertInterval: parseInt(process.env['CACHE_ALERT_INTERVAL'] || '30000'), // 30秒
        retentionHours: parseInt(process.env['CACHE_RETENTION_HOURS'] || '168'), // 7天
        maxMetrics: parseInt(process.env['CACHE_MAX_METRICS'] || '1000'),
        maxAlerts: parseInt(process.env['CACHE_MAX_ALERTS'] || '100'),
      },
      alerts: {
        enabled: process.env['CACHE_ALERTS_ENABLED'] !== 'false',
        hitRateThreshold: parseFloat(process.env['CACHE_HIT_RATE_THRESHOLD'] || '80'), // 80%
        errorRateThreshold: parseFloat(process.env['CACHE_ERROR_RATE_THRESHOLD'] || '5'), // 5%
        responseTimeThreshold: parseInt(process.env['CACHE_RESPONSE_TIME_THRESHOLD'] || '100'), // 100ms
        memoryUsageThreshold: parseFloat(process.env['CACHE_MEMORY_USAGE_THRESHOLD'] || '80'), // 80%
        keyCountThreshold: parseInt(process.env['CACHE_KEY_COUNT_THRESHOLD'] || '100000'), // 10万个键
        duplicateAlertWindow: parseInt(process.env['CACHE_DUPLICATE_ALERT_WINDOW'] || '300000'), // 5分钟
      },
      notifications: {
        enabled: process.env['CACHE_NOTIFICATIONS_ENABLED'] === 'true',
        webhookUrl: process.env['CACHE_WEBHOOK_URL'],
        emailEnabled: process.env['CACHE_EMAIL_ENABLED'] === 'true',
        emailRecipients: process.env['CACHE_EMAIL_RECIPIENTS']?.split(',') || [],
        slackEnabled: process.env['CACHE_SLACK_ENABLED'] === 'true',
        slackWebhook: process.env['CACHE_SLACK_WEBHOOK'],
      },
      performance: {
        enableDetailedMetrics: process.env['CACHE_DETAILED_METRICS'] === 'true',
        enableTrendAnalysis: process.env['CACHE_TREND_ANALYSIS'] !== 'false',
        enablePerformanceAnalysis: process.env['CACHE_PERFORMANCE_ANALYSIS'] !== 'false',
        sampleRate: parseFloat(process.env['CACHE_SAMPLE_RATE'] || '1.0'), // 100%采样
      },
      reporting: {
        enableAutoReports: process.env['CACHE_AUTO_REPORTS'] === 'true',
        reportInterval: parseInt(process.env['CACHE_REPORT_INTERVAL'] || '86400000'), // 24小时
        reportRecipients: process.env['CACHE_REPORT_RECIPIENTS']?.split(',') || [],
        includeRecommendations: process.env['CACHE_INCLUDE_RECOMMENDATIONS'] !== 'false',
      },
    };
  }

  /**
   * 获取监控配置
   */
  public getMonitoringConfig(): MonitoringConfig {
    return this.config.monitoring;
  }

  /**
   * 获取告警配置
   */
  public getAlertConfig(): CacheAlertConfig {
    return this.config.alerts;
  }

  /**
   * 获取通知配置
   */
  public getNotificationConfig(): NotificationConfig {
    return this.config.notifications;
  }

  /**
   * 获取性能配置
   */
  public getPerformanceConfig(): PerformanceConfig {
    return this.config.performance;
  }

  /**
   * 获取报告配置
   */
  public getReportingConfig(): ReportingConfig {
    return this.config.reporting;
  }

  /**
   * 获取完整配置
   */
  public getFullConfig(): CacheMonitorConfiguration {
    return { ...this.config };
  }

  /**
   * 更新监控配置
   */
  public updateMonitoringConfig(config: Partial<MonitoringConfig>): void {
    this.config.monitoring = { ...this.config.monitoring, ...config };
  }

  /**
   * 更新告警配置
   */
  public updateAlertConfig(config: Partial<CacheAlertConfig>): void {
    this.config.alerts = { ...this.config.alerts, ...config };
  }

  /**
   * 更新通知配置
   */
  public updateNotificationConfig(config: Partial<NotificationConfig>): void {
    this.config.notifications = { ...this.config.notifications, ...config };
  }

  /**
   * 验证配置
   */
  public validateConfig(): ConfigValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 验证监控配置
    if (this.config.monitoring.metricsInterval < 10000) {
      warnings.push('指标收集间隔过短，可能影响性能');
    }

    if (this.config.monitoring.retentionHours < 24) {
      warnings.push('数据保留时间过短，可能影响趋势分析');
    }

    // 验证告警配置
    if (this.config.alerts.hitRateThreshold < 50 || this.config.alerts.hitRateThreshold > 100) {
      errors.push('命中率阈值应在50-100之间');
    }

    if (this.config.alerts.errorRateThreshold < 0 || this.config.alerts.errorRateThreshold > 50) {
      errors.push('错误率阈值应在0-50之间');
    }

    // 验证通知配置
    if (this.config.notifications.enabled) {
      if (this.config.notifications.emailEnabled && this.config.notifications.emailRecipients.length === 0) {
        warnings.push('启用了邮件通知但未配置收件人');
      }

      if (this.config.notifications.slackEnabled && !this.config.notifications.slackWebhook) {
        warnings.push('启用了Slack通知但未配置Webhook');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * 重置为默认配置
   */
  public resetToDefaults(): void {
    this.config = this.loadConfiguration();
  }

  /**
   * 导出配置
   */
  public exportConfig(): string {
    return JSON.stringify(this.config, null, 2);
  }

  /**
   * 导入配置
   */
  public importConfig(configJson: string): void {
    try {
      const importedConfig = JSON.parse(configJson);
      this.config = { ...this.config, ...importedConfig };
    } catch (error) {
      throw new Error('配置格式无效');
    }
  }
}

/**
 * 缓存监控配置接口
 */
export interface CacheMonitorConfiguration {
  monitoring: MonitoringConfig;
  alerts: CacheAlertConfig;
  notifications: NotificationConfig;
  performance: PerformanceConfig;
  reporting: ReportingConfig;
}

/**
 * 监控配置接口
 */
export interface MonitoringConfig {
  enabled: boolean;
  metricsInterval: number;
  alertInterval: number;
  retentionHours: number;
  maxMetrics: number;
  maxAlerts: number;
}

/**
 * 通知配置接口
 */
export interface NotificationConfig {
  enabled: boolean;
  webhookUrl?: string;
  emailEnabled: boolean;
  emailRecipients: string[];
  slackEnabled: boolean;
  slackWebhook?: string;
}

/**
 * 性能配置接口
 */
export interface PerformanceConfig {
  enableDetailedMetrics: boolean;
  enableTrendAnalysis: boolean;
  enablePerformanceAnalysis: boolean;
  sampleRate: number;
}

/**
 * 报告配置接口
 */
export interface ReportingConfig {
  enableAutoReports: boolean;
  reportInterval: number;
  reportRecipients: string[];
  includeRecommendations: boolean;
}

/**
 * 配置验证结果接口
 */
export interface ConfigValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * 预定义配置模板
 */
export const ConfigTemplates = {
  development: {
    monitoring: {
      enabled: true,
      metricsInterval: 30000, // 30秒
      alertInterval: 15000, // 15秒
      retentionHours: 24, // 1天
      maxMetrics: 500,
      maxAlerts: 50,
    },
    alerts: {
      enabled: true,
      hitRateThreshold: 70,
      errorRateThreshold: 10,
      responseTimeThreshold: 200,
      memoryUsageThreshold: 90,
      keyCountThreshold: 50000,
    },
  },
  production: {
    monitoring: {
      enabled: true,
      metricsInterval: 60000, // 1分钟
      alertInterval: 30000, // 30秒
      retentionHours: 168, // 7天
      maxMetrics: 1000,
      maxAlerts: 100,
    },
    alerts: {
      enabled: true,
      hitRateThreshold: 80,
      errorRateThreshold: 5,
      responseTimeThreshold: 100,
      memoryUsageThreshold: 80,
      keyCountThreshold: 100000,
    },
  },
  testing: {
    monitoring: {
      enabled: false,
      metricsInterval: 10000, // 10秒
      alertInterval: 5000, // 5秒
      retentionHours: 1, // 1小时
      maxMetrics: 100,
      maxAlerts: 20,
    },
    alerts: {
      enabled: false,
      hitRateThreshold: 50,
      errorRateThreshold: 20,
      responseTimeThreshold: 500,
      memoryUsageThreshold: 95,
      keyCountThreshold: 10000,
    },
  },
};
