# Copyright (c) 2025 "放置". All rights reserved.

# 优化后的Asset Bundle分包策略

## 🎯 分包策略概述

基于方案A的渐进式优化，我们重新设计了Asset Bundle分包策略，以支持更好的按需加载和用户体验。

## 📦 分包结构

### 主包 (Main Package)
- **内容**: 核心启动逻辑、基础UI、启动场景
- **大小限制**: 4MB (微信小游戏) / 4MB (抖音小游戏)
- **包含**: 
  - 启动场景
  - 核心管理器
  - 基础UI组件
  - 必要的配置文件

### 子包1: core (核心功能包)
- **优先级**: 1 (最高)
- **类型**: subpackage (子包)
- **内容**: 核心游戏逻辑、数据管理
- **预计大小**: 2-3MB
- **加载时机**: 游戏启动后立即加载

### 子包2: ui-basic (基础UI包)
- **优先级**: 2
- **类型**: subpackage (子包)
- **内容**: 基础游戏界面、常用UI组件
- **预计大小**: 1-2MB
- **加载时机**: 进入主界面时加载

### 子包3: ui-advanced (高级UI包)
- **优先级**: 3
- **类型**: subpackage (子包)
- **内容**: 高级功能界面、复杂UI组件
- **预计大小**: 2-3MB
- **加载时机**: 用户访问高级功能时加载

### 子包4: features (功能包)
- **优先级**: 2
- **类型**: subpackage (子包)
- **内容**: 特定游戏功能、装备系统、技能系统
- **预计大小**: 3-4MB
- **加载时机**: 用户解锁相应功能时加载

### 子包5: locations (地点包)
- **优先级**: 1
- **类型**: subpackage (子包)
- **内容**: 游戏地点配置、地点相关资源
- **预计大小**: 2-3MB
- **加载时机**: 用户切换地点时按需加载

### 子包6: ui-features (UI功能包)
- **优先级**: 2
- **类型**: subpackage (子包)
- **内容**: 特定功能的UI界面
- **预计大小**: 1-2MB
- **加载时机**: 用户访问特定功能时加载

### 子包7: audio (音频包)
- **优先级**: 4
- **类型**: subpackage (子包)
- **内容**: 背景音乐、音效文件
- **预计大小**: 3-5MB
- **加载时机**: 延迟加载，用户开启音频时加载

### 远程包1: configs (配置包)
- **优先级**: 5
- **类型**: remote bundle (远程包)
- **内容**: 游戏配置数据、可热更新的配置
- **预计大小**: 500KB-1MB
- **加载时机**: 游戏启动时从服务器加载

### 远程包2: remote-resources (远程资源包)
- **优先级**: 3
- **类型**: remote bundle (远程包)
- **内容**: 可选资源、活动资源
- **预计大小**: 1-2MB
- **加载时机**: 按需从服务器加载

## 🚀 加载策略

### 1. 启动阶段
```
主包 → core (立即) → ui-basic (立即) → configs (远程)
```

### 2. 游戏进行阶段
```
locations (按需) → features (解锁时) → ui-advanced (访问时)
```

### 3. 延迟加载阶段
```
audio (用户开启) → remote-resources (活动时) → ui-features (特定功能)
```

## 📊 平台适配

### 微信小游戏
- **主包限制**: 4MB
- **总子包限制**: 20MB
- **推荐配置**: 
  - 主包: 3.5MB (预留缓冲)
  - 核心子包: 8MB
  - 可选子包: 8.5MB

### 抖音小游戏
- **主包限制**: 4MB
- **总子包限制**: 40MB
- **推荐配置**:
  - 主包: 3.5MB (预留缓冲)
  - 核心子包: 15MB
  - 可选子包: 21.5MB

## 🔧 技术实现

### Bundle加载管理器
```typescript
class BundleManager {
  // 预加载核心包
  async preloadCoreBundles(): Promise<void> {
    await this.loadBundle('core');
    await this.loadBundle('ui-basic');
  }
  
  // 按需加载功能包
  async loadFeatureBundle(feature: string): Promise<void> {
    const bundleName = this.getBundleNameByFeature(feature);
    await this.loadBundle(bundleName);
  }
  
  // 延迟加载可选包
  async loadOptionalBundles(): Promise<void> {
    // 在空闲时间加载
    await this.loadBundle('audio');
    await this.loadBundle('ui-advanced');
  }
}
```

### 配置热更新
```typescript
class ConfigManager {
  // 检查配置更新
  async checkConfigUpdate(): Promise<boolean> {
    const remoteVersion = await this.getRemoteConfigVersion();
    const localVersion = this.getLocalConfigVersion();
    return remoteVersion > localVersion;
  }
  
  // 更新远程配置
  async updateRemoteConfigs(): Promise<void> {
    await this.loadBundle('configs', { reload: true });
  }
}
```

## 📈 性能优化

### 1. 压缩策略
- **子包**: 使用subpackage压缩
- **远程包**: 使用merge_all_json合并JSON文件
- **音频包**: 使用音频压缩算法

### 2. 缓存策略
- **本地缓存**: 子包缓存到本地存储
- **内存缓存**: 常用资源保持在内存中
- **智能清理**: 根据使用频率清理缓存

### 3. 预加载策略
- **关键路径**: 优先加载用户必需的资源
- **预测加载**: 根据用户行为预测需要的资源
- **后台加载**: 在用户空闲时加载可选资源

## 🎮 用户体验优化

### 1. 加载进度显示
- 显示详细的加载进度
- 提供加载阶段说明
- 支持取消非必要加载

### 2. 离线支持
- 核心功能支持离线游戏
- 智能缓存策略
- 网络恢复时自动同步

### 3. 错误处理
- 加载失败重试机制
- 降级方案支持
- 用户友好的错误提示

## 🔄 迁移计划

### 阶段1: 基础重构 (1周)
1. 创建新的bundle目录结构
2. 配置bundle元数据
3. 测试基础加载功能

### 阶段2: 资源迁移 (1周)
1. 将现有资源分类到对应bundle
2. 更新资源引用路径
3. 测试资源加载

### 阶段3: 优化测试 (3-5天)
1. 性能测试和优化
2. 兼容性测试
3. 用户体验测试

## ✅ 验证清单

- [ ] 主包大小控制在4MB以内
- [ ] 子包总大小符合平台限制
- [ ] 核心功能可正常加载
- [ ] 按需加载功能正常
- [ ] 远程配置更新正常
- [ ] 离线模式功能正常
- [ ] 加载性能满足要求
- [ ] 用户体验流畅

这个优化后的分包策略将显著提升游戏的加载速度和用户体验，同时为后续的功能扩展提供良好的架构基础。
