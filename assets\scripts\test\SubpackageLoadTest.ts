// Copyright (c) 2025 "放置". All rights reserved.
import { _decorator, Component, assetManager, director } from 'cc';
import { SceneManager } from '../managers/SceneManager';
import { ConfigManager } from '../managers/ConfigManager';

const { ccclass, property } = _decorator;

/**
 * 分包加载测试组件
 * 用于测试和验证分包加载功能
 */
@ccclass('SubpackageLoadTest')
export class SubpackageLoadTest extends Component {

    /**
     * 测试结果
     */
    private testResults: { [key: string]: boolean } = {};

    protected onLoad(): void {
        console.log('🧪 分包加载测试组件初始化');
    }

    protected start(): void {
        // 延迟执行测试，确保其他系统初始化完成
        this.scheduleOnce(() => {
            this.runAllTests();
        }, 1.0);
    }

    /**
     * 运行所有测试
     */
    public async runAllTests(): Promise<void> {
        console.log('🧪 ========== 分包加载测试开始 ==========');

        try {
            // 测试1: 基础分包加载
            await this.testBasicBundleLoading();

            // 测试2: 场景分包加载
            await this.testSceneBundleLoading();

            // 测试3: UI功能分包加载
            await this.testUIFeatureBundleLoading();

            // 测试4: 配置数据获取
            await this.testConfigDataLoading();

            // 测试5: 场景切换集成测试
            await this.testSceneSwitchingIntegration();

            // 输出测试结果
            this.printTestResults();

        } catch (error) {
            console.error('❌ 测试执行失败:', error);
        }

        console.log('🧪 ========== 分包加载测试结束 ==========');
    }

    /**
     * 测试基础分包加载
     */
    private async testBasicBundleLoading(): Promise<void> {
        console.log('🧪 测试1: 基础分包加载');

        try {
            // 测试加载场景分包
            const scenesBundle = await this.loadBundle('scenes');
            this.testResults['scenes_bundle_load'] = !!scenesBundle;
            console.log(`✅ 场景分包加载: ${scenesBundle ? '成功' : '失败'}`);

            // 测试加载UI功能分包
            const uiFeaturesBundle = await this.loadBundle('ui-features');
            this.testResults['ui_features_bundle_load'] = !!uiFeaturesBundle;
            console.log(`✅ UI功能分包加载: ${uiFeaturesBundle ? '成功' : '失败'}`);

        } catch (error) {
            console.error('❌ 基础分包加载测试失败:', error);
            this.testResults['scenes_bundle_load'] = false;
            this.testResults['ui_features_bundle_load'] = false;
        }
    }

    /**
     * 测试场景分包加载
     */
    private async testSceneBundleLoading(): Promise<void> {
        console.log('🧪 测试2: 场景分包加载');

        const sceneNames = ['Forest', 'Desert', 'Mountain'];

        for (const sceneName of sceneNames) {
            try {
                const success = await this.testSceneLoad(sceneName);
                this.testResults[`scene_${sceneName.toLowerCase()}_load`] = success;
                console.log(`✅ ${sceneName}场景加载: ${success ? '成功' : '失败'}`);
            } catch (error) {
                console.error(`❌ ${sceneName}场景加载测试失败:`, error);
                this.testResults[`scene_${sceneName.toLowerCase()}_load`] = false;
            }
        }
    }

    /**
     * 测试UI功能分包加载
     */
    private async testUIFeatureBundleLoading(): Promise<void> {
        console.log('🧪 测试3: UI功能分包加载');

        try {
            const bundle = await this.loadBundle('ui-features');
            if (bundle) {
                // 测试加载预制件
                const prefabNames = ['CharacterPanel', 'EquipmentPanel'];
                
                for (const prefabName of prefabNames) {
                    try {
                        const prefab = await this.loadPrefabFromBundle(bundle, prefabName);
                        this.testResults[`prefab_${prefabName.toLowerCase()}_load`] = !!prefab;
                        console.log(`✅ ${prefabName}预制件加载: ${prefab ? '成功' : '失败'}`);
                    } catch (error) {
                        console.error(`❌ ${prefabName}预制件加载失败:`, error);
                        this.testResults[`prefab_${prefabName.toLowerCase()}_load`] = false;
                    }
                }
            }
        } catch (error) {
            console.error('❌ UI功能分包加载测试失败:', error);
        }
    }

    /**
     * 测试配置数据获取
     */
    private async testConfigDataLoading(): Promise<void> {
        console.log('🧪 测试4: 配置数据获取');

        try {
            const configManager = ConfigManager.getInstance();
            
            // 测试单个场景配置获取
            const forestConfig = await configManager.getSceneConfig('Forest');
            this.testResults['config_forest_load'] = !!forestConfig;
            console.log(`✅ 森林场景配置获取: ${forestConfig ? '成功' : '失败'}`);

            // 测试批量场景配置获取
            const batchConfigs = await configManager.getSceneConfigsBatch(['Forest', 'Desert', 'Mountain']);
            this.testResults['config_batch_load'] = batchConfigs.size > 0;
            console.log(`✅ 批量场景配置获取: ${batchConfigs.size > 0 ? '成功' : '失败'} (${batchConfigs.size}个)`);

        } catch (error) {
            console.error('❌ 配置数据获取测试失败:', error);
            this.testResults['config_forest_load'] = false;
            this.testResults['config_batch_load'] = false;
        }
    }

    /**
     * 测试场景切换集成
     */
    private async testSceneSwitchingIntegration(): Promise<void> {
        console.log('🧪 测试5: 场景切换集成测试');

        try {
            const sceneManager = SceneManager.getInstance();
            
            // 获取当前场景
            const currentScene = director.getScene();
            const originalSceneName = currentScene ? currentScene.name : 'Unknown';
            
            console.log(`📍 当前场景: ${originalSceneName}`);
            
            // 注意：实际场景切换会改变当前场景，这里只测试加载能力
            // 在实际项目中，可能需要更复杂的测试策略
            
            // 测试分包信息获取
            const bundleInfo = sceneManager.getBundleInfo();
            this.testResults['scene_manager_bundle_info'] = Object.keys(bundleInfo).length > 0;
            console.log(`✅ 分包信息获取: ${Object.keys(bundleInfo).length > 0 ? '成功' : '失败'}`);
            console.log(`📦 已加载分包:`, Object.keys(bundleInfo));

        } catch (error) {
            console.error('❌ 场景切换集成测试失败:', error);
            this.testResults['scene_manager_bundle_info'] = false;
        }
    }

    /**
     * 加载分包
     */
    private async loadBundle(bundleName: string): Promise<any> {
        return new Promise((resolve, reject) => {
            assetManager.loadBundle(bundleName, (error, bundle) => {
                if (error) {
                    reject(error);
                } else {
                    resolve(bundle);
                }
            });
        });
    }

    /**
     * 测试场景加载
     */
    private async testSceneLoad(sceneName: string): Promise<boolean> {
        try {
            const bundle = await this.loadBundle('scenes');
            if (!bundle) {
                return false;
            }

            return new Promise((resolve) => {
                bundle.loadScene(sceneName, (error, sceneAsset) => {
                    resolve(!error && !!sceneAsset);
                });
            });
        } catch (error) {
            return false;
        }
    }

    /**
     * 从分包加载预制件
     */
    private async loadPrefabFromBundle(bundle: any, prefabName: string): Promise<any> {
        return new Promise((resolve, reject) => {
            bundle.load(prefabName, (error, prefab) => {
                if (error) {
                    reject(error);
                } else {
                    resolve(prefab);
                }
            });
        });
    }

    /**
     * 输出测试结果
     */
    private printTestResults(): void {
        console.log('🧪 ========== 测试结果汇总 ==========');
        
        let passedCount = 0;
        let totalCount = 0;

        for (const [testName, result] of Object.entries(this.testResults)) {
            totalCount++;
            if (result) {
                passedCount++;
                console.log(`✅ ${testName}: 通过`);
            } else {
                console.log(`❌ ${testName}: 失败`);
            }
        }

        const successRate = totalCount > 0 ? (passedCount / totalCount * 100).toFixed(1) : '0';
        console.log(`📊 测试通过率: ${passedCount}/${totalCount} (${successRate}%)`);
        
        if (passedCount === totalCount) {
            console.log('🎉 所有测试通过！分包加载功能正常');
        } else {
            console.log('⚠️ 部分测试失败，请检查分包配置和网络连接');
        }
        
        console.log('🧪 ===================================');
    }

    /**
     * 手动触发测试（供外部调用）
     */
    public triggerTest(): void {
        this.runAllTests();
    }
}
