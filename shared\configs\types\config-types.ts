/**
 * Copyright (c) 2025 "放置". All rights reserved.
 * 配置系统类型定义 - 多人在线游戏安全架构
 */

// ==================== 配置分层定义 ====================

/**
 * 配置安全级别
 */
export enum ConfigSecurityLevel {
  PUBLIC = 'public',           // 公开配置，客户端可访问
  PROTECTED = 'protected',     // 受保护配置，需要验证
  PRIVATE = 'private',         // 私有配置，仅服务器内部
  SECRET = 'secret'            // 机密配置，加密存储
}

/**
 * 配置类型分类
 */
export enum ConfigType {
  STATIC = 'static',           // 静态配置，随版本更新
  DYNAMIC = 'dynamic',         // 动态配置，可热更新
  USER = 'user',               // 用户配置，个人设置
  REALTIME = 'realtime'        // 实时配置，在线状态
}

// ==================== 客户端安全配置 ====================

/**
 * 客户端显示配置（安全）
 */
export interface ClientDisplayConfig {
  locationId: string;
  displayName: string;
  description: string;
  iconPath: string;
  backgroundPath: string;
  uiLayout: {
    mapSize: { width: number; height: number };
    nodeSpacing: { x: number; y: number };
    buttonPositions: Record<string, { x: number; y: number }>;
  };
  visualEffects: {
    weather: string;
    timeOfDay: string;
    lightingColor: { r: number; g: number; b: number; a: number };
    ambientSounds: string[];
  };
}

/**
 * 客户端音频配置（安全）
 */
export interface ClientAudioConfig {
  bgm: Record<string, {
    path: string;
    volume: number;
    loop: boolean;
  }>;
  sfx: Record<string, {
    path: string;
    volume: number;
  }>;
}

// ==================== 服务器敏感配置 ====================

/**
 * 地点解锁条件（服务器端）
 */
export interface LocationUnlockConditions {
  locationId: string;
  requirements: {
    playerLevel: number;
    requiredItems: Array<{ id: string; quantity: number }>;
    completedQuests: string[];
    requiredLocations: string[];
    customConditions: Array<{
      type: string;
      condition: any;
      errorMessage: string;
    }>;
  };
  unlockRewards?: {
    exp: number;
    gold: number;
    items: Array<{ id: string; quantity: number }>;
  };
}

/**
 * 行为配置（服务器端）
 */
export interface BehaviorConfig {
  behaviorId: string;
  locationId: string;
  name: string;
  type: string;
  duration: number;           // 执行时间（毫秒）
  cooldown: number;          // 冷却时间（毫秒）
  energyCost: number;        // 能量消耗
  requirements: {
    items: Array<{ id: string; quantity: number; consumed: boolean }>;
    skills: Array<{ id: string; level: number }>;
    playerLevel: number;
  };
  rewards: {
    baseExp: number;
    baseGold: number;
    items: Array<{
      id: string;
      probability: number;
      quantity: [number, number]; // [min, max]
    }>;
    currency: Record<string, [number, number]>;
  };
  antiCheat: {
    minDuration: number;       // 最小执行时间（防加速）
    maxConcurrent: number;     // 最大并发数
    cooldownEnforced: boolean; // 是否强制冷却
  };
}

// ==================== 多人游戏配置 ====================

/**
 * 多人游戏房间配置
 */
export interface MultiplayerRoomConfig {
  roomId: string;
  maxPlayers: number;
  gameMode: string;
  settings: {
    allowSpectators: boolean;
    autoStart: boolean;
    roundDuration: number;
    syncInterval: number;      // 同步间隔（毫秒）
  };
  antiCheat: {
    enableValidation: boolean;
    maxDesyncTolerance: number; // 最大同步误差
    kickOnCheat: boolean;
  };
}

/**
 * 玩家同步数据
 */
export interface PlayerSyncData {
  playerId: string;
  timestamp: number;
  position: { x: number; y: number };
  state: string;
  actions: Array<{
    actionId: string;
    timestamp: number;
    data: any;
  }>;
  checksum: string;          // 数据校验和
}

// ==================== 缓存配置 ====================

/**
 * 缓存策略配置
 */
export interface CacheStrategyConfig {
  type: ConfigType;
  securityLevel: ConfigSecurityLevel;
  storage: {
    redis: {
      enabled: boolean;
      ttl: number;           // 生存时间（秒）
      prefix: string;
      cluster: boolean;
    };
    memory: {
      enabled: boolean;
      maxSize: number;       // 最大缓存大小（MB）
      ttl: number;
    };
    database: {
      enabled: boolean;
      table: string;
      indexFields: string[];
    };
  };
  sync: {
    strategy: 'push' | 'pull' | 'hybrid';
    interval: number;        // 同步间隔（毫秒）
    batchSize: number;       // 批量大小
  };
  invalidation: {
    onUpdate: boolean;       // 更新时失效
    onUserAction: boolean;   // 用户操作时失效
    cascade: string[];       // 级联失效的键
  };
}

// ==================== 安全配置 ====================

/**
 * 防作弊配置
 */
export interface AntiCheatConfig {
  enabled: boolean;
  checks: {
    timeValidation: {
      enabled: boolean;
      maxClockSkew: number;  // 最大时钟偏差（毫秒）
      minActionInterval: number; // 最小操作间隔
    };
    progressValidation: {
      enabled: boolean;
      maxProgressPerSecond: number; // 最大进度/秒
      validateRewards: boolean;
    };
    behaviorValidation: {
      enabled: boolean;
      checkDuration: boolean;
      checkCooldown: boolean;
      checkRequirements: boolean;
    };
    concurrencyValidation: {
      enabled: boolean;
      maxConcurrentActions: number;
      maxSessionsPerUser: number;
    };
  };
  penalties: {
    warning: { threshold: number; action: string };
    tempBan: { threshold: number; duration: number };
    permBan: { threshold: number };
  };
  logging: {
    logSuspicious: boolean;
    logLevel: 'info' | 'warn' | 'error';
    retentionDays: number;
  };
}

/**
 * 频率限制配置
 */
export interface RateLimitConfig {
  endpoint: string;
  limits: Array<{
    window: number;          // 时间窗口（秒）
    maxRequests: number;     // 最大请求数
    skipSuccessful?: boolean; // 是否跳过成功请求
  }>;
  keyGenerator: 'ip' | 'user' | 'session' | 'custom';
  customKeyFunction?: string;
  onLimitExceeded: {
    action: 'block' | 'delay' | 'queue';
    message: string;
    statusCode: number;
  };
}

// ==================== 配置版本控制 ====================

/**
 * 配置版本信息
 */
export interface ConfigVersion {
  version: string;           // 语义化版本号
  timestamp: number;         // 创建时间戳
  checksum: string;          // 配置校验和
  changes: Array<{
    type: 'add' | 'modify' | 'delete';
    path: string;
    description: string;
  }>;
  compatibility: {
    minClientVersion: string;
    maxClientVersion?: string;
  };
  rollback: {
    enabled: boolean;
    previousVersion?: string;
    autoRollbackOnError: boolean;
  };
}

/**
 * 配置更新事件
 */
export interface ConfigUpdateEvent {
  eventId: string;
  timestamp: number;
  configType: ConfigType;
  configPath: string;
  version: string;
  updateType: 'create' | 'update' | 'delete';
  affectedUsers?: string[];  // 受影响的用户ID
  metadata: {
    source: string;          // 更新来源
    reason: string;          // 更新原因
    operator: string;        // 操作者
  };
}

// ==================== 导出所有类型 ====================

export * from './game-types';
export * from './api-types';
