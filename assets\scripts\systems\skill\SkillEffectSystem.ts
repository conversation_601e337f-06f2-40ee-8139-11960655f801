/**
 * 技能效果系统
 * 负责处理技能效果的应用和管理
 */

import { ISkillData, ISkillResult, IAppliedEffect, SkillEffectType } from '../../config/interfaces/ISkillData';

/**
 * 技能效果系统
 */
export class SkillEffectSystem {
    
    /**
     * 应用技能效果
     */
    public static applySkillEffect(skill: ISkillData, caster: any, target: any): ISkillResult {
        console.log(`⚔️ 应用技能效果: ${skill.name}`);
        
        const appliedEffects: IAppliedEffect[] = [];
        let totalDamage = 0;
        let totalHealing = 0;
        
        // 处理技能效果
        for (const effect of skill.effects) {
            const appliedEffect = this.processSkillEffect(effect, skill.id, caster?.id || 'unknown', target?.id || 'unknown');
            appliedEffects.push(appliedEffect);
            
            // 累计伤害和治疗
            if (effect.type === SkillEffectType.Damage) {
                totalDamage += effect.value;
            } else if (effect.type === SkillEffectType.Heal) {
                totalHealing += effect.value;
            }
        }
        
        return {
            success: true,
            damage: totalDamage,
            healing: totalHealing,
            manaCost: skill.manaCost,
            cooldown: skill.cooldown,
            effects: appliedEffects,
            isCritical: Math.random() < 0.1, // 10%暴击率
            experienceGained: 10
        };
    }
    
    /**
     * 计算技能伤害
     */
    public static calculateDamage(skill: ISkillData, caster: any): number {
        const baseDamage = skill.baseDamageMultiplier * 100;
        const levelBonus = (caster?.level || 1) * 10;
        
        return baseDamage + levelBonus;
    }
    
    /**
     * 处理单个技能效果
     */
    private static processSkillEffect(effect: any, sourceSkillId: string, casterId: string, targetId: string): IAppliedEffect {
        return {
            type: effect.type,
            value: effect.value,
            remainingDuration: effect.duration,
            sourceSkillId: sourceSkillId,
            casterId: casterId,
            targetId: targetId
        };
    }
}
