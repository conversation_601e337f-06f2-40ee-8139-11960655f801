/**
 * Copyright (c) 2025 "放置". All rights reserved.
 * API接口类型定义
 */

import { PlayerInfo, GameLocation, GameItem, SkillInfo, EquipmentInfo, BehaviorResult, RoomInfo, PlayerState } from './game-types';

// ==================== 请求类型 ====================

/**
 * 登录请求
 */
export interface LoginRequest {
  username: string;
  password?: string;
  platform: 'wechat' | 'douyin' | 'web';
  code?: string; // 小程序登录码
}

/**
 * 执行行为请求
 */
export interface ExecuteBehaviorRequest {
  behaviorId: string;
  locationId: string;
  duration?: number;
  items?: Array<{ id: string; quantity: number }>;
}

/**
 * 装备操作请求
 */
export interface EquipmentActionRequest {
  equipmentId: string;
  action: 'equip' | 'unequip' | 'upgrade' | 'sell';
  targetSlot?: string;
}

/**
 * 房间操作请求
 */
export interface RoomActionRequest {
  roomId?: string;
  action: 'create' | 'join' | 'leave' | 'start';
  gameMode?: string;
  maxPlayers?: number;
}

// ==================== 响应类型 ====================

/**
 * 登录响应
 */
export interface LoginResponse {
  success: boolean;
  token?: string;
  playerInfo?: PlayerInfo;
  gameConfig?: any;
  error?: string;
}

/**
 * 玩家信息响应
 */
export interface PlayerInfoResponse {
  success: boolean;
  playerInfo?: PlayerInfo;
  locations?: GameLocation[];
  inventory?: GameItem[];
  skills?: SkillInfo[];
  equipment?: EquipmentInfo[];
  error?: string;
}

/**
 * 行为执行响应
 */
export interface BehaviorExecuteResponse {
  success: boolean;
  result?: BehaviorResult;
  playerInfo?: Partial<PlayerInfo>;
  error?: string;
}

/**
 * 房间信息响应
 */
export interface RoomInfoResponse {
  success: boolean;
  roomInfo?: RoomInfo;
  players?: PlayerState[];
  error?: string;
}

/**
 * 游戏状态同步响应
 */
export interface GameSyncResponse {
  success: boolean;
  players?: PlayerState[];
  events?: Array<{
    type: string;
    playerId: string;
    data: any;
    timestamp: number;
  }>;
  error?: string;
}

// ==================== WebSocket消息类型 ====================

/**
 * WebSocket消息基础结构
 */
export interface WSMessage<T = any> {
  type: string;
  data: T;
  timestamp: number;
  messageId?: string;
}

/**
 * 玩家加入消息
 */
export interface PlayerJoinMessage {
  playerId: string;
  playerInfo: PlayerInfo;
  roomId: string;
}

/**
 * 玩家离开消息
 */
export interface PlayerLeaveMessage {
  playerId: string;
  roomId: string;
  reason?: string;
}

/**
 * 游戏状态更新消息
 */
export interface GameStateUpdateMessage {
  roomId: string;
  players: PlayerState[];
  gameData?: any;
}

/**
 * 聊天消息
 */
export interface ChatMessage {
  playerId: string;
  playerName: string;
  message: string;
  timestamp: number;
  roomId?: string;
}

// ==================== API端点类型 ====================

/**
 * API端点枚举
 */
export enum ApiEndpoint {
  // 认证相关
  LOGIN = '/api/auth/login',
  LOGOUT = '/api/auth/logout',
  REFRESH_TOKEN = '/api/auth/refresh',
  
  // 玩家相关
  PLAYER_INFO = '/api/player/info',
  PLAYER_UPDATE = '/api/player/update',
  
  // 游戏行为
  BEHAVIOR_EXECUTE = '/api/behavior/execute',
  BEHAVIOR_LIST = '/api/behavior/list',
  
  // 装备系统
  EQUIPMENT_LIST = '/api/equipment/list',
  EQUIPMENT_ACTION = '/api/equipment/action',
  
  // 多人游戏
  ROOM_CREATE = '/api/room/create',
  ROOM_JOIN = '/api/room/join',
  ROOM_LEAVE = '/api/room/leave',
  ROOM_LIST = '/api/room/list',
  
  // 配置相关
  CONFIG_GET = '/api/config/get',
  CONFIG_VERSION = '/api/config/version'
}

/**
 * HTTP方法类型
 */
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

/**
 * API请求配置
 */
export interface ApiRequestConfig {
  endpoint: ApiEndpoint | string;
  method: HttpMethod;
  data?: any;
  params?: Record<string, any>;
  headers?: Record<string, string>;
  timeout?: number;
}
