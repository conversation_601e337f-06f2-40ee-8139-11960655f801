/**
 * 物品数据接口定义
 * 基于原Godot项目的items.xml结构
 */

/**
 * 物品类型枚举
 */
export enum ItemType {
    WEAPON = 'weapon',
    ARMOR = 'armor',
    ACCESSORY = 'accessory',
    CONSUMABLE = 'consumable',
    MATERIAL = 'material',
    QUEST = 'quest',
    MISC = 'misc'
}

/**
 * 物品稀有度枚举
 */
export enum ItemRarity {
    COMMON = 'common',
    UNCOMMON = 'uncommon',
    RARE = 'rare',
    EPIC = 'epic',
    LEGENDARY = 'legendary',
    MYTHIC = 'mythic'
}

/**
 * 装备部位枚举
 */
export enum EquipmentSlot {
    WEAPON = 'weapon',
    HELMET = 'helmet',
    ARMOR = 'armor',
    GLOVES = 'gloves',
    BOOTS = 'boots',
    NECKLACE = 'necklace',
    RING = 'ring',
    BELT = 'belt',
    CLOAK = 'cloak'
}

/**
 * 属性加成接口
 */
export interface IAttributeBonus {
    /** 属性名称 */
    attribute: string;
    
    /** 加成值 */
    value: number;
    
    /** 是否为百分比加成 */
    isPercentage?: boolean;
}

/**
 * 物品效果接口
 */
export interface IItemEffect {
    /** 效果类型 */
    type: string;
    
    /** 效果值 */
    value: number;
    
    /** 持续时间(毫秒) */
    duration?: number;
    
    /** 效果描述 */
    description: string;
    
    /** 触发条件 */
    condition?: string;
}

/**
 * 物品数据接口
 */
export interface IItemData {
    /** 物品ID */
    id: string;
    
    /** 物品名称 */
    name: string;
    
    /** 物品描述 */
    description: string;
    
    /** 物品类型 */
    type: ItemType;
    
    /** 稀有度 */
    rarity: ItemRarity;
    
    /** 等级要求 */
    levelRequirement: number;
    
    /** 职业要求 */
    classRequirement?: string[];
    
    /** 最大堆叠数量 */
    maxStack: number;
    
    /** 基础价格 */
    basePrice: number;
    
    /** 出售价格 */
    sellPrice: number;
    
    /** 图标路径 */
    iconPath: string;
    
    /** 模型路径 */
    modelPath?: string;
    
    /** 是否可交易 */
    isTradeable: boolean;
    
    /** 是否可丢弃 */
    isDroppable: boolean;
    
    /** 是否可使用 */
    isUsable: boolean;
    
    /** 使用冷却时间(毫秒) */
    useCooldown?: number;
    
    /** 属性加成 */
    attributeBonuses?: IAttributeBonus[];
    
    /** 物品效果 */
    effects?: IItemEffect[];
    
    /** 标签 */
    tags?: string[];
    
    /** 重量 */
    weight?: number;
    
    /** 耐久度 */
    durability?: number;
    
    /** 最大耐久度 */
    maxDurability?: number;
}

/**
 * 装备数据接口
 */
export interface IEquipmentData extends IItemData {
    /** 装备部位 */
    equipmentSlot: EquipmentSlot;
    
    /** 基础攻击力 */
    baseAttack?: number;
    
    /** 基础防御力 */
    baseDefense?: number;
    
    /** 基础魔法攻击力 */
    baseMagicAttack?: number;
    
    /** 基础魔法防御力 */
    baseMagicDefense?: number;
    
    /** 强化等级 */
    enhanceLevel?: number;
    
    /** 最大强化等级 */
    maxEnhanceLevel?: number;
    
    /** 镶嵌宝石槽数量 */
    gemSlots?: number;
    
    /** 已镶嵌的宝石 */
    equippedGems?: string[];
    
    /** 套装ID */
    setId?: string;
    
    /** 套装效果 */
    setEffects?: IItemEffect[];
}

/**
 * 消耗品数据接口
 */
export interface IConsumableData extends IItemData {
    /** 恢复生命值 */
    healthRestore?: number;
    
    /** 恢复魔法值 */
    manaRestore?: number;
    
    /** 是否立即生效 */
    isInstant: boolean;
    
    /** 持续时间(毫秒) */
    duration?: number;
    
    /** 使用动画 */
    useAnimation?: string;
    
    /** 使用音效 */
    useSound?: string;
}

/**
 * 材料数据接口
 */
export interface IMaterialData extends IItemData {
    /** 材料等级 */
    materialLevel: number;
    
    /** 用途描述 */
    usage: string[];
    
    /** 可制作物品 */
    craftableItems?: string[];
    
    /** 获取方式 */
    obtainMethods?: string[];
}

/**
 * 背包物品接口
 */
export interface IInventoryItem {
    /** 物品ID */
    itemId: string;
    
    /** 实例ID */
    instanceId: string;
    
    /** 数量 */
    quantity: number;
    
    /** 背包位置 */
    slotIndex: number;
    
    /** 获得时间 */
    obtainedAt: Date;
    
    /** 是否已装备 */
    isEquipped?: boolean;
    
    /** 装备部位 */
    equippedSlot?: EquipmentSlot;
    
    /** 强化等级 */
    enhanceLevel?: number;
    
    /** 镶嵌的宝石 */
    equippedGems?: string[];
    
    /** 当前耐久度 */
    currentDurability?: number;
    
    /** 绑定状态 */
    isBound?: boolean;
    
    /** 过期时间 */
    expiresAt?: Date;
}

/**
 * 背包接口
 */
export interface IInventory {
    /** 背包容量 */
    capacity: number;
    
    /** 已使用槽位 */
    usedSlots: number;
    
    /** 物品列表 */
    items: IInventoryItem[];
    
    /** 背包类型 */
    type: 'main' | 'equipment' | 'quest' | 'material';
    
    /** 最后更新时间 */
    lastUpdated: Date;
}

/**
 * 装备集合接口
 */
export interface IEquipmentSet {
    /** 武器 */
    weapon?: IInventoryItem;
    
    /** 头盔 */
    helmet?: IInventoryItem;
    
    /** 护甲 */
    armor?: IInventoryItem;
    
    /** 手套 */
    gloves?: IInventoryItem;
    
    /** 靴子 */
    boots?: IInventoryItem;
    
    /** 项链 */
    necklace?: IInventoryItem;
    
    /** 戒指 */
    ring?: IInventoryItem;
    
    /** 腰带 */
    belt?: IInventoryItem;
    
    /** 斗篷 */
    cloak?: IInventoryItem;
}

/**
 * 物品配置接口
 */
export interface IItemConfig {
    /** 物品列表 */
    items: IItemData[];
    
    /** 装备列表 */
    equipment?: IEquipmentData[];
    
    /** 消耗品列表 */
    consumables?: IConsumableData[];
    
    /** 材料列表 */
    materials?: IMaterialData[];
    
    /** 全局设置 */
    globalSettings?: {
        /** 默认背包容量 */
        defaultInventoryCapacity: number;
        
        /** 最大堆叠数量 */
        maxStackSize: number;
        
        /** 装备耐久度损耗率 */
        durabilityDecayRate: number;
        
        /** 修理费用倍数 */
        repairCostMultiplier: number;
    };
}
