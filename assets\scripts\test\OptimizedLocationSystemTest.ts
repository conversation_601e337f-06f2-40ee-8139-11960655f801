/**
 * Copyright (c) 2025 "放置". All rights reserved.
 * 优化后的地点系统测试（适配新的节点结构）
 */

import { _decorator, Component, input, Input, KeyCode, find } from 'cc';
import { LocationConfigManager } from '../managers/LocationConfigManager';
import { MapPanelController } from '../ui/controllers/MapPanelController';
import { EventManager } from '../managers/EventManager';

const { ccclass, property } = _decorator;

/**
 * 优化后的地点系统测试组件
 * 适配新的BehaviorPanel节点结构和MapPanelController
 */
@ccclass('OptimizedLocationSystemTest')
export class OptimizedLocationSystemTest extends Component {

    @property({ tooltip: '是否启用键盘快捷键' })
    public enableKeyboardShortcuts: boolean = true;

    @property({ tooltip: '是否显示详细日志' })
    public enableDetailedLogging: boolean = true;

    @property({ tooltip: '是否自动运行测试' })
    public autoRunTest: boolean = false;

    // 管理器和控制器实例
    private _locationConfigManager: LocationConfigManager | null = null;
    private _mapPanelController: MapPanelController | null = null;

    protected onLoad(): void {
        console.log('🧪 OptimizedLocationSystemTest: 优化后的地点系统测试组件加载');
        this.initializeComponents();
        
        if (this.enableKeyboardShortcuts) {
            this.setupKeyboardShortcuts();
        }

        // 设置事件监听
        this.setupEventListeners();
    }

    protected start(): void {
        if (this.autoRunTest) {
            this.scheduleOnce(() => {
                this.runSystemTest();
            }, 2.0);
        }
    }

    protected onDestroy(): void {
        if (this.enableKeyboardShortcuts) {
            input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        }
        this.removeEventListeners();
    }

    /**
     * 初始化组件实例
     */
    private initializeComponents(): void {
        this._locationConfigManager = LocationConfigManager.getInstance();
        
        // 查找MapPanelController
        const behaviorPanelNode = find('Canvas/MainUI/BehaviorPanel');
        if (behaviorPanelNode) {
            const mapPanelNode = behaviorPanelNode.getChildByName('MapPanel');
            if (mapPanelNode) {
                this._mapPanelController = mapPanelNode.getComponent(MapPanelController);
            }
        }

        if (this.enableDetailedLogging) {
            console.log('🧪 OptimizedLocationSystemTest: 组件实例获取状态:', {
                locationConfigManager: !!this._locationConfigManager,
                mapPanelController: !!this._mapPanelController,
                behaviorPanelFound: !!behaviorPanelNode
            });
        }
    }

    /**
     * 设置事件监听
     */
    private setupEventListeners(): void {
        const eventManager = EventManager.getInstance();
        
        // 监听地点切换事件
        eventManager.on('location:switched', this.onLocationSwitched.bind(this));
        eventManager.on('map_panel:location_switched', this.onMapPanelLocationSwitched.bind(this));
    }

    /**
     * 移除事件监听
     */
    private removeEventListeners(): void {
        const eventManager = EventManager.getInstance();
        
        eventManager.off('location:switched', this.onLocationSwitched.bind(this));
        eventManager.off('map_panel:location_switched', this.onMapPanelLocationSwitched.bind(this));
    }

    /**
     * 设置键盘快捷键
     */
    private setupKeyboardShortcuts(): void {
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        
        console.log('🧪 OptimizedLocationSystemTest: 键盘快捷键已启用');
        console.log('快捷键说明:');
        console.log('  T - 运行系统测试');
        console.log('  1 - 切换到鱼塘');
        console.log('  2 - 切换到农场');
        console.log('  3 - 切换到森林');
        console.log('  R - 刷新按钮状态');
        console.log('  S - 显示系统状态');
        console.log('  C - 检查节点结构');
    }

    /**
     * 键盘按键处理
     */
    private onKeyDown(event: any): void {
        switch (event.keyCode) {
            case KeyCode.KEY_T:
                this.runSystemTest();
                break;
            case KeyCode.DIGIT_1:
                this.testLocationSwitch('fishpond');
                break;
            case KeyCode.DIGIT_2:
                this.testLocationSwitch('farm');
                break;
            case KeyCode.DIGIT_3:
                this.testLocationSwitch('forest');
                break;
            case KeyCode.KEY_R:
                this.testRefreshButtonStates();
                break;
            case KeyCode.KEY_S:
                this.showSystemStatus();
                break;
            case KeyCode.KEY_C:
                this.checkNodeStructure();
                break;
        }
    }

    /**
     * 运行系统测试
     */
    public async runSystemTest(): Promise<void> {
        console.log('🧪 OptimizedLocationSystemTest: 开始系统测试');

        try {
            // 测试1: 组件初始化检查
            await this.testComponentInitialization();

            // 测试2: 节点结构检查
            await this.testNodeStructure();

            // 测试3: MapPanelController功能测试
            await this.testMapPanelController();

            // 测试4: LocationConfigManager功能测试
            await this.testLocationConfigManager();

            // 测试5: 地点切换测试
            await this.testLocationSwitching();

            console.log('✅ OptimizedLocationSystemTest: 系统测试完成');

        } catch (error) {
            console.error('❌ OptimizedLocationSystemTest: 系统测试失败', error);
        }
    }

    /**
     * 测试组件初始化
     */
    private async testComponentInitialization(): Promise<void> {
        console.log('🧪 测试1: 组件初始化检查');

        const results = {
            locationConfigManager: !!this._locationConfigManager,
            mapPanelController: !!this._mapPanelController
        };

        console.log('   LocationConfigManager:', results.locationConfigManager ? '✅' : '❌');
        console.log('   MapPanelController:', results.mapPanelController ? '✅' : '❌');

        if (!results.locationConfigManager || !results.mapPanelController) {
            throw new Error('关键组件初始化失败');
        }
    }

    /**
     * 测试节点结构
     */
    private async testNodeStructure(): Promise<void> {
        console.log('🧪 测试2: 节点结构检查');

        const behaviorPanel = find('Canvas/MainUI/BehaviorPanel');
        const mapContainer = find('Canvas/MainUI/BehaviorPanel/LinearBranchMapContainer');
        const mapPanel = find('Canvas/MainUI/BehaviorPanel/MapPanel');
        const maplist = find('Canvas/MainUI/BehaviorPanel/MapPanel/Maplist');

        console.log('   BehaviorPanel:', behaviorPanel ? '✅' : '❌');
        console.log('   LinearBranchMapContainer:', mapContainer ? '✅' : '❌');
        console.log('   MapPanel:', mapPanel ? '✅' : '❌');
        console.log('   Maplist:', maplist ? '✅' : '❌');

        if (!behaviorPanel || !mapContainer || !mapPanel || !maplist) {
            throw new Error('节点结构不完整');
        }
    }

    /**
     * 测试MapPanelController功能
     */
    private async testMapPanelController(): Promise<void> {
        console.log('🧪 测试3: MapPanelController功能测试');

        if (!this._mapPanelController) {
            throw new Error('MapPanelController不可用');
        }

        // 测试获取按钮配置
        const buttonConfigs = this._mapPanelController.getMapButtonConfigs();
        console.log(`   按钮配置数量: ${buttonConfigs.length} ${buttonConfigs.length > 0 ? '✅' : '❌'}`);

        // 测试获取当前地点
        const currentLocation = this._mapPanelController.getCurrentLocationId();
        console.log(`   当前地点: ${currentLocation} ${currentLocation ? '✅' : '❌'}`);

        if (this.enableDetailedLogging) {
            console.log('   按钮配置详情:', buttonConfigs.map(config => 
                `${config.buttonName} -> ${config.locationId} (${config.isUnlocked ? '已解锁' : '未解锁'})`
            ));
        }
    }

    /**
     * 测试LocationConfigManager功能
     */
    private async testLocationConfigManager(): Promise<void> {
        console.log('🧪 测试4: LocationConfigManager功能测试');

        if (!this._locationConfigManager) {
            throw new Error('LocationConfigManager不可用');
        }

        // 测试获取地点配置
        const fishpondConfig = await this._locationConfigManager.getLocationConfig('fishpond');
        console.log(`   鱼塘配置加载: ${fishpondConfig ? '✅' : '❌'}`);

        if (fishpondConfig && this.enableDetailedLogging) {
            console.log('   鱼塘配置详情:', {
                name: fishpondConfig.name,
                type: fishpondConfig.type,
                behaviorsCount: fishpondConfig.behaviors?.length || 0
            });
        }

        // 测试解锁状态检查
        const unlockStatus = await this._locationConfigManager.getLocationUnlockStatus('fishpond');
        console.log(`   解锁状态检查: ${unlockStatus ? '✅' : '❌'}`);
    }

    /**
     * 测试地点切换
     */
    private async testLocationSwitching(): Promise<void> {
        console.log('🧪 测试5: 地点切换测试');

        if (!this._mapPanelController) {
            throw new Error('MapPanelController不可用');
        }

        // 测试切换到鱼塘
        const success = await this._mapPanelController.switchToLocation('fishpond');
        console.log(`   切换到鱼塘: ${success ? '✅' : '❌'}`);

        if (!success) {
            throw new Error('地点切换失败');
        }
    }

    /**
     * 测试地点切换
     */
    public async testLocationSwitch(locationId: string): Promise<void> {
        console.log(`🧪 OptimizedLocationSystemTest: 测试切换到地点 - ${locationId}`);

        if (!this._mapPanelController) {
            console.error('❌ MapPanelController不可用');
            return;
        }

        try {
            const success = await this._mapPanelController.switchToLocation(locationId);
            if (success) {
                console.log(`✅ 地点切换成功: ${locationId}`);
            } else {
                console.log(`❌ 地点切换失败: ${locationId}`);
            }
        } catch (error) {
            console.error(`❌ 地点切换异常: ${locationId}`, error);
        }
    }

    /**
     * 测试刷新按钮状态
     */
    public async testRefreshButtonStates(): Promise<void> {
        console.log('🧪 OptimizedLocationSystemTest: 测试刷新按钮状态');

        if (!this._mapPanelController) {
            console.error('❌ MapPanelController不可用');
            return;
        }

        try {
            await this._mapPanelController.refreshAllButtonStates();
            console.log('✅ 按钮状态刷新完成');
        } catch (error) {
            console.error('❌ 刷新按钮状态失败', error);
        }
    }

    /**
     * 显示系统状态
     */
    public showSystemStatus(): void {
        console.log('📊 OptimizedLocationSystemTest: 系统状态');
        console.log('='.repeat(50));

        if (this._locationConfigManager) {
            const cacheStats = this._locationConfigManager.getLocationConfigCacheStats();
            console.log('📋 LocationConfigManager状态:');
            console.log(`   缓存数量: ${cacheStats.totalCached}`);
            console.log(`   过期数量: ${cacheStats.expiredCount}`);
        }

        if (this._mapPanelController) {
            const currentLocation = this._mapPanelController.getCurrentLocationId();
            const buttonConfigs = this._mapPanelController.getMapButtonConfigs();
            const unlockedCount = buttonConfigs.filter(config => config.isUnlocked).length;

            console.log('🗺️ MapPanelController状态:');
            console.log(`   当前地点: ${currentLocation}`);
            console.log(`   按钮总数: ${buttonConfigs.length}`);
            console.log(`   已解锁: ${unlockedCount}`);
        }

        console.log('='.repeat(50));
    }

    /**
     * 检查节点结构
     */
    public checkNodeStructure(): void {
        console.log('🔍 OptimizedLocationSystemTest: 检查节点结构');
        
        const nodes = {
            'Canvas': find('Canvas'),
            'MainUI': find('Canvas/MainUI'),
            'BehaviorPanel': find('Canvas/MainUI/BehaviorPanel'),
            'LinearBranchMapContainer': find('Canvas/MainUI/BehaviorPanel/LinearBranchMapContainer'),
            'MapContainer': find('Canvas/MainUI/BehaviorPanel/LinearBranchMapContainer/MapContainer'),
            'MapPanel': find('Canvas/MainUI/BehaviorPanel/MapPanel'),
            'Maplist': find('Canvas/MainUI/BehaviorPanel/MapPanel/Maplist'),
            'map-001': find('Canvas/MainUI/BehaviorPanel/MapPanel/Maplist/list/map-001')
        };

        console.log('节点结构检查结果:');
        for (const [name, node] of Object.entries(nodes)) {
            console.log(`   ${name}: ${node ? '✅ 存在' : '❌ 缺失'}`);
        }
    }

    /**
     * 地点切换事件处理
     */
    private onLocationSwitched(eventData: any): void {
        if (this.enableDetailedLogging) {
            console.log('🎮 OptimizedLocationSystemTest: 收到地点切换事件', eventData);
        }
    }

    /**
     * MapPanel地点切换事件处理
     */
    private onMapPanelLocationSwitched(eventData: any): void {
        if (this.enableDetailedLogging) {
            console.log('🗺️ OptimizedLocationSystemTest: 收到MapPanel地点切换事件', eventData);
        }
    }
}
