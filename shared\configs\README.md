# Copyright (c) 2025 "放置". All rights reserved.

# 共享配置表结构说明

## 目录结构
```
shared/configs/
├── base/               # 基础配置
│   ├── game_constants/ # 游戏常量
│   ├── feature_flags/  # 功能开关
│   └── system_limits/  # 系统限制
├── version/            # 版本控制
│   ├── config_versions/# 配置版本
│   ├── compatibility/  # 兼容性配置
│   └── migration/      # 迁移配置
└── unlock/             # 解锁条件
    ├── features/       # 功能解锁
    ├── content/        # 内容解锁
    └── progression/    # 进度解锁
```

## 共享配置原则
1. **数据一致性**：前后端使用相同的基础配置
2. **版本同步**：统一的版本控制和更新机制
3. **最小暴露**：只共享必要的非敏感数据
4. **缓存友好**：支持本地缓存和增量更新
5. **向后兼容**：保证版本升级的平滑过渡

## 配置同步机制
- 前端启动时检查配置版本
- 版本不匹配时自动下载最新配置
- 支持增量更新和差异同步
- 本地缓存配置以减少网络请求

## 安全考虑
- 共享配置不包含敏感数值
- 所有配置都有完整性校验
- 支持配置签名验证
- 防止配置被恶意篡改

## 使用指南
- 前端通过ConfigManager访问共享配置
- 后端通过SharedConfigService管理配置
- 配置更新需要同时更新版本号
- 重要配置变更需要兼容性测试
