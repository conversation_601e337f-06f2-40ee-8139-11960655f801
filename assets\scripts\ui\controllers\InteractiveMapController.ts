/**
 * 交互地图控制器
 * 整合LinearBranchMapPanel和BehaviorPanel，实现完整的交互地图系统
 */

import { _decorator, Component, Node } from 'cc';
import { EventManager } from '../../managers/EventManager';
import { LinearBranchMapPanel, IBranchNodeData } from '../panels/LinearBranchMapPanel';
import { BehaviorPanel, BehaviorType, IBehaviorData } from '../panels/BehaviorPanel';

const { ccclass, property } = _decorator;

/**
 * 地图控制器配置
 */
export interface IMapControllerConfig {
    /** 是否自动显示地图 */
    autoShowMap: boolean;
    
    /** 是否自动处理行为 */
    autoHandleBehavior: boolean;
    
    /** 默认行为持续时间 */
    defaultBehaviorDuration: number;
    
    /** 是否显示进度提示 */
    showProgressHints: boolean;
}

@ccclass('InteractiveMapController')
export class InteractiveMapController extends Component {
    
    @property({ type: LinearBranchMapPanel, tooltip: '线性分支地图面板' })
    public branchMapPanel: LinearBranchMapPanel | null = null;
    
    @property({ type: BehaviorPanel, tooltip: '行为面板' })
    public behaviorPanel: BehaviorPanel | null = null;
    
    @property({ type: Node, tooltip: '地图容器节点' })
    public mapContainer: Node | null = null;
    
    @property({ type: Node, tooltip: '行为容器节点' })
    public behaviorContainer: Node | null = null;
    
    @property({ tooltip: '是否自动显示地图' })
    public autoShowMap: boolean = true;
    
    @property({ tooltip: '是否自动处理行为' })
    public autoHandleBehavior: boolean = true;
    
    @property({ tooltip: '默认行为持续时间' })
    public defaultBehaviorDuration: number = 3.0;
    
    // 私有属性
    private _config: IMapControllerConfig = {
        autoShowMap: true,
        autoHandleBehavior: true,
        defaultBehaviorDuration: 3.0,
        showProgressHints: true
    };
    
    private _currentActiveNode: IBranchNodeData | null = null;
    private _isProcessingBehavior: boolean = false;

    protected onLoad(): void {
        console.log('🎮 InteractiveMapController: 交互地图控制器加载');
        this.initializeComponents();
        this.bindEvents();
        this.updateConfig();
    }

    protected start(): void {
        if (this._config.autoShowMap) {
            this.showMap();
        }
    }

    protected onDestroy(): void {
        this.unbindEvents();
    }

    /**
     * 初始化组件
     */
    private initializeComponents(): void {
        // 自动查找线性分支地图面板
        if (!this.branchMapPanel) {
            this.branchMapPanel = this.node.getComponent(LinearBranchMapPanel);
            if (!this.branchMapPanel && this.mapContainer) {
                this.branchMapPanel = this.mapContainer.getComponent(LinearBranchMapPanel);
            }
        }
        
        // 自动查找行为面板
        if (!this.behaviorPanel) {
            this.behaviorPanel = this.node.getComponent(BehaviorPanel);
            if (!this.behaviorPanel && this.behaviorContainer) {
                this.behaviorPanel = this.behaviorContainer.getComponent(BehaviorPanel);
            }
            if (!this.behaviorPanel) {
                this.behaviorPanel = this.node.getComponentInChildren(BehaviorPanel);
            }
        }
        
        console.log('🎮 InteractiveMapController: 组件初始化完成', {
            branchMapPanel: !!this.branchMapPanel,
            behaviorPanel: !!this.behaviorPanel
        });
    }

    /**
     * 绑定事件
     */
    private bindEvents(): void {
        const eventManager = EventManager.getInstance();
        
        // 监听分支节点点击事件
        eventManager.on('branch_node_clicked', this.onBranchNodeClicked, this);
        
        // 监听行为触发事件
        eventManager.on('trigger_behavior', this.onTriggerBehavior, this);
        
        // 监听行为完成事件
        eventManager.on('behavior_complete', this.onBehaviorComplete, this);
        
        // 监听节点解锁事件
        eventManager.on('branch_node_unlocked', this.onNodeUnlocked, this);
        
        // 监听节点锁定消息事件
        eventManager.on('show_node_locked_message', this.onShowNodeLockedMessage, this);
        
        console.log('🎮 InteractiveMapController: 事件绑定完成');
    }

    /**
     * 解绑事件
     */
    private unbindEvents(): void {
        const eventManager = EventManager.getInstance();
        
        eventManager.off('branch_node_clicked', this.onBranchNodeClicked, this);
        eventManager.off('trigger_behavior', this.onTriggerBehavior, this);
        eventManager.off('behavior_complete', this.onBehaviorComplete, this);
        eventManager.off('branch_node_unlocked', this.onNodeUnlocked, this);
        eventManager.off('show_node_locked_message', this.onShowNodeLockedMessage, this);
        
        console.log('🎮 InteractiveMapController: 事件解绑完成');
    }

    /**
     * 更新配置
     */
    private updateConfig(): void {
        this._config.autoShowMap = this.autoShowMap;
        this._config.autoHandleBehavior = this.autoHandleBehavior;
        this._config.defaultBehaviorDuration = this.defaultBehaviorDuration;
    }

    /**
     * 分支节点点击处理
     */
    private onBranchNodeClicked(eventData: any): void {
        const { nodeData, behaviorType } = eventData;
        
        console.log('🎮 InteractiveMapController: 处理分支节点点击', nodeData.name);
        
        this._currentActiveNode = nodeData;
        
        // 发送节点激活事件
        EventManager.getInstance().emit('map_node_activated', {
            nodeData: nodeData,
            behaviorType: behaviorType
        });
    }

    /**
     * 行为触发处理
     */
    private onTriggerBehavior(eventData: any): void {
        const { behaviorType, nodeData } = eventData;
        
        if (!this._config.autoHandleBehavior || !this.behaviorPanel) {
            console.log('🎮 InteractiveMapController: 自动行为处理已禁用');
            return;
        }
        
        if (this._isProcessingBehavior) {
            console.warn('🎮 InteractiveMapController: 正在处理其他行为，忽略新请求');
            return;
        }
        
        console.log('🎮 InteractiveMapController: 触发行为', behaviorType);
        
        this._isProcessingBehavior = true;
        
        // 创建行为数据
        const behaviorData: Partial<IBehaviorData> = {
            name: nodeData.name,
            description: nodeData.description,
            duration: this._config.defaultBehaviorDuration,
            extraData: {
                nodeId: nodeData.id,
                nodeData: nodeData
            }
        };
        
        // 执行行为
        this.behaviorPanel.executeBehavior(behaviorType, behaviorData);
        
        // 聚焦到当前节点
        if (this.branchMapPanel) {
            this.branchMapPanel.focusOnNode(nodeData.id);
        }
    }

    /**
     * 行为完成处理
     */
    private onBehaviorComplete(eventData: any): void {
        const { behaviorData } = eventData;
        
        console.log('🎮 InteractiveMapController: 行为完成', behaviorData?.name);
        
        this._isProcessingBehavior = false;
        
        // 如果有关联的节点数据，标记节点为完成状态
        if (behaviorData?.extraData?.nodeData && this._currentActiveNode) {
            this.markNodeAsCompleted(this._currentActiveNode.id);
        }
        
        // 发送行为完成事件
        EventManager.getInstance().emit('map_behavior_completed', {
            behaviorData: behaviorData,
            nodeData: this._currentActiveNode
        });
        
        this._currentActiveNode = null;
    }

    /**
     * 节点解锁处理
     */
    private onNodeUnlocked(eventData: any): void {
        const { nodeId, nodeData } = eventData;
        
        console.log('🎮 InteractiveMapController: 节点解锁', nodeData.name);
        
        // 可以在这里添加解锁特效或提示
        this.showNodeUnlockedEffect(nodeData);
        
        // 发送节点解锁通知
        EventManager.getInstance().emit('map_node_unlocked_notification', {
            nodeId: nodeId,
            nodeData: nodeData
        });
    }

    /**
     * 显示节点锁定消息
     */
    private onShowNodeLockedMessage(eventData: any): void {
        const { nodeData, prerequisites } = eventData;
        
        console.log('🎮 InteractiveMapController: 显示节点锁定消息', nodeData.name);
        
        // 这里可以显示UI提示
        const prereqNames = prerequisites.map((prereqId: string) => {
            const prereqNode = this.branchMapPanel?.getNodeData(prereqId);
            return prereqNode ? prereqNode.name : prereqId;
        }).join(', ');
        
        const message = `节点"${nodeData.name}"已锁定。需要完成前置条件：${prereqNames}`;
        
        // 发送提示消息事件
        EventManager.getInstance().emit('show_ui_message', {
            message: message,
            type: 'warning',
            duration: 3000
        });
    }

    /**
     * 标记节点为完成状态
     */
    private markNodeAsCompleted(nodeId: string): void {
        // 这里可以添加节点完成的视觉效果
        console.log('🎮 InteractiveMapController: 标记节点完成', nodeId);
        
        // 发送节点完成事件
        EventManager.getInstance().emit('map_node_completed', {
            nodeId: nodeId
        });
    }

    /**
     * 显示节点解锁特效
     */
    private showNodeUnlockedEffect(nodeData: IBranchNodeData): void {
        console.log('✨ InteractiveMapController: 显示解锁特效', nodeData.name);
        
        // 这里可以添加粒子特效、音效等
        // 目前只是发送事件通知
        EventManager.getInstance().emit('play_unlock_effect', {
            nodeData: nodeData,
            position: nodeData.position
        });
    }

    // ==================== 公共API ====================

    /**
     * 显示地图
     */
    public showMap(): void {
        if (this.branchMapPanel) {
            this.branchMapPanel.showPanel();
        }
        
        console.log('🎮 InteractiveMapController: 显示地图');
    }

    /**
     * 隐藏地图
     */
    public hideMap(): void {
        if (this.branchMapPanel) {
            this.branchMapPanel.hidePanel();
        }
        
        console.log('🎮 InteractiveMapController: 隐藏地图');
    }

    /**
     * 聚焦到指定节点
     */
    public focusOnNode(nodeId: string): void {
        if (this.branchMapPanel) {
            this.branchMapPanel.focusOnNode(nodeId);
        }
    }

    /**
     * 手动解锁节点
     */
    public unlockNode(nodeId: string): void {
        if (this.branchMapPanel) {
            this.branchMapPanel.unlockNode(nodeId);
        }
    }

    /**
     * 获取当前活动节点
     */
    public getCurrentActiveNode(): IBranchNodeData | null {
        return this._currentActiveNode;
    }

    /**
     * 是否正在处理行为
     */
    public isProcessingBehavior(): boolean {
        return this._isProcessingBehavior;
    }

    /**
     * 设置配置
     */
    public setConfig(config: Partial<IMapControllerConfig>): void {
        this._config = { ...this._config, ...config };
        this.updateConfig();
    }

    /**
     * 获取节点数据
     */
    public getNodeData(nodeId: string): IBranchNodeData | undefined {
        return this.branchMapPanel?.getNodeData(nodeId);
    }

    /**
     * 停止当前行为
     */
    public stopCurrentBehavior(): void {
        if (this.behaviorPanel) {
            this.behaviorPanel.stopCurrentBehavior();
        }
        this._isProcessingBehavior = false;
        this._currentActiveNode = null;
    }

    /**
     * 重置地图状态
     */
    public resetMapState(): void {
        this.stopCurrentBehavior();
        
        // 重置所有节点状态（除了起始节点）
        // 这里可以根据需要实现具体的重置逻辑
        
        console.log('🎮 InteractiveMapController: 地图状态已重置');
    }
}
