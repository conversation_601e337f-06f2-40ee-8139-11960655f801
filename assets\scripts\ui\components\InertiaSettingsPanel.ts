/**
 * 惯性设置面板
 * 提供UI界面来调整拖拽惯性参数
 */

import { _decorator, Component, Node, Slider, Label, Toggle, Button } from 'cc';
import { DraggableMapContainer } from './DraggableMapContainer';

const { ccclass, property } = _decorator;

@ccclass('InertiaSettingsPanel')
export class InertiaSettingsPanel extends Component {
    
    @property({ tooltip: '惯性开关' })
    public inertiaToggle: Toggle = null!;
    
    @property({ tooltip: '惯性阻尼滑块' })
    public dampingSlider: Slider = null!;
    
    @property({ tooltip: '惯性阻尼数值显示' })
    public dampingLabel: Label = null!;
    
    @property({ tooltip: '惯性持续时间滑块' })
    public durationSlider: Slider = null!;
    
    @property({ tooltip: '惯性持续时间数值显示' })
    public durationLabel: Label = null!;
    
    @property({ tooltip: '最小速度阈值滑块' })
    public thresholdSlider: Slider = null!;
    
    @property({ tooltip: '最小速度阈值数值显示' })
    public thresholdLabel: Label = null!;
    
    @property({ tooltip: '重置按钮' })
    public resetButton: Button = null!;
    
    @property({ tooltip: '关闭按钮' })
    public closeButton: Button = null!;
    
    // 目标拖拽容器
    private _draggableContainer: DraggableMapContainer | null = null;
    
    // 惯性参数
    private _inertiaSettings = {
        enabled: true,
        damping: 0.95,
        duration: 1.0,
        threshold: 50
    };

    protected onLoad(): void {
        this.setupEventListeners();
        this.findDraggableContainer();
        this.loadCurrentSettings();
        this.updateUI();
    }

    /**
     * 设置事件监听器
     */
    private setupEventListeners(): void {
        // 惯性开关
        if (this.inertiaToggle) {
            this.inertiaToggle.node.on('toggle', this.onInertiaToggleChanged, this);
        }
        
        // 阻尼滑块
        if (this.dampingSlider) {
            this.dampingSlider.node.on('slide', this.onDampingSliderChanged, this);
        }
        
        // 持续时间滑块
        if (this.durationSlider) {
            this.durationSlider.node.on('slide', this.onDurationSliderChanged, this);
        }
        
        // 速度阈值滑块
        if (this.thresholdSlider) {
            this.thresholdSlider.node.on('slide', this.onThresholdSliderChanged, this);
        }
        
        // 重置按钮
        if (this.resetButton) {
            this.resetButton.node.on(Button.EventType.CLICK, this.onResetButtonClicked, this);
        }
        
        // 关闭按钮
        if (this.closeButton) {
            this.closeButton.node.on(Button.EventType.CLICK, this.onCloseButtonClicked, this);
        }
    }

    /**
     * 查找拖拽容器
     */
    private findDraggableContainer(): void {
        // 在场景中查找DraggableMapContainer组件
        const containerNode = this.node.scene.getChildByPath('Main/Canvas/MainUI/BackPanel/LinearBranchMapContainer');
        if (containerNode) {
            this._draggableContainer = containerNode.getComponent(DraggableMapContainer);
            if (this._draggableContainer) {
                console.log('🎛️ InertiaSettingsPanel: 找到拖拽容器');
            } else {
                console.warn('🎛️ InertiaSettingsPanel: 节点存在但没有DraggableMapContainer组件');
            }
        } else {
            console.warn('🎛️ InertiaSettingsPanel: 未找到拖拽容器节点');
        }
    }

    /**
     * 加载当前设置
     */
    private loadCurrentSettings(): void {
        if (this._draggableContainer) {
            this._inertiaSettings.enabled = this._draggableContainer.enableInertia;
            this._inertiaSettings.damping = this._draggableContainer.inertiaDamping;
            // 其他参数使用默认值
        }
    }

    /**
     * 更新UI显示
     */
    private updateUI(): void {
        // 更新惯性开关
        if (this.inertiaToggle) {
            this.inertiaToggle.isChecked = this._inertiaSettings.enabled;
        }
        
        // 更新阻尼滑块和标签
        if (this.dampingSlider) {
            this.dampingSlider.progress = (this._inertiaSettings.damping - 0.1) / 0.9; // 映射到0-1
        }
        if (this.dampingLabel) {
            this.dampingLabel.string = this._inertiaSettings.damping.toFixed(2);
        }
        
        // 更新持续时间滑块和标签
        if (this.durationSlider) {
            this.durationSlider.progress = (this._inertiaSettings.duration - 0.1) / 2.9; // 0.1-3.0秒映射到0-1
        }
        if (this.durationLabel) {
            this.durationLabel.string = this._inertiaSettings.duration.toFixed(1) + 's';
        }
        
        // 更新速度阈值滑块和标签
        if (this.thresholdSlider) {
            this.thresholdSlider.progress = (this._inertiaSettings.threshold - 10) / 190; // 10-200映射到0-1
        }
        if (this.thresholdLabel) {
            this.thresholdLabel.string = this._inertiaSettings.threshold.toFixed(0);
        }
    }

    /**
     * 应用设置到拖拽容器
     */
    private applySettings(): void {
        if (!this._draggableContainer) return;
        
        this._draggableContainer.enableInertia = this._inertiaSettings.enabled;
        this._draggableContainer.inertiaDamping = this._inertiaSettings.damping;
        
        console.log('🎛️ InertiaSettingsPanel: 应用惯性设置', this._inertiaSettings);
    }

    // ==================== 事件处理器 ====================

    /**
     * 惯性开关改变
     */
    private onInertiaToggleChanged(toggle: Toggle): void {
        this._inertiaSettings.enabled = toggle.isChecked;
        this.applySettings();
        console.log('🎛️ 惯性开关:', this._inertiaSettings.enabled ? '启用' : '禁用');
    }

    /**
     * 阻尼滑块改变
     */
    private onDampingSliderChanged(slider: Slider): void {
        this._inertiaSettings.damping = 0.1 + slider.progress * 0.9; // 0.1-1.0
        this.dampingLabel.string = this._inertiaSettings.damping.toFixed(2);
        this.applySettings();
        console.log('🎛️ 惯性阻尼:', this._inertiaSettings.damping);
    }

    /**
     * 持续时间滑块改变
     */
    private onDurationSliderChanged(slider: Slider): void {
        this._inertiaSettings.duration = 0.1 + slider.progress * 2.9; // 0.1-3.0秒
        this.durationLabel.string = this._inertiaSettings.duration.toFixed(1) + 's';
        console.log('🎛️ 惯性持续时间:', this._inertiaSettings.duration);
    }

    /**
     * 速度阈值滑块改变
     */
    private onThresholdSliderChanged(slider: Slider): void {
        this._inertiaSettings.threshold = 10 + slider.progress * 190; // 10-200
        this.thresholdLabel.string = this._inertiaSettings.threshold.toFixed(0);
        console.log('🎛️ 惯性速度阈值:', this._inertiaSettings.threshold);
    }

    /**
     * 重置按钮点击
     */
    private onResetButtonClicked(): void {
        this._inertiaSettings = {
            enabled: true,
            damping: 0.95,
            duration: 1.0,
            threshold: 50
        };
        this.updateUI();
        this.applySettings();
        console.log('🎛️ 惯性设置已重置');
    }

    /**
     * 关闭按钮点击
     */
    private onCloseButtonClicked(): void {
        this.node.active = false;
        console.log('🎛️ 惯性设置面板已关闭');
    }

    // ==================== 公共API ====================

    /**
     * 显示面板
     */
    public show(): void {
        this.node.active = true;
        this.loadCurrentSettings();
        this.updateUI();
        console.log('🎛️ 惯性设置面板已显示');
    }

    /**
     * 隐藏面板
     */
    public hide(): void {
        this.node.active = false;
        console.log('🎛️ 惯性设置面板已隐藏');
    }

    /**
     * 获取当前惯性设置
     */
    public getInertiaSettings(): any {
        return { ...this._inertiaSettings };
    }
}
