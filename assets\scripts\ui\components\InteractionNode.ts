/**
 * 交互节点组件
 * 表示地图上的可交互点，支持不同类型的交互行为
 */

import { _decorator, Component, Node, Sprite, Label, Button, Color, UITransform, tween, Vec3 } from 'cc';
import { IInteractionNodeData } from '../panels/InteractiveMapPanel';

const { ccclass, property } = _decorator;

/**
 * 节点状态枚举
 */
export enum NodeState {
    Locked = 'locked',
    Unlocked = 'unlocked',
    Active = 'active',
    Completed = 'completed'
}

/**
 * 节点视觉配置
 */
export interface INodeVisualConfig {
    /** 锁定状态颜色 */
    lockedColor: Color;
    
    /** 解锁状态颜色 */
    unlockedColor: Color;
    
    /** 激活状态颜色 */
    activeColor: Color;
    
    /** 完成状态颜色 */
    completedColor: Color;
    
    /** 节点大小 */
    nodeSize: number;
    
    /** 是否显示连接线 */
    showConnections: boolean;
}

@ccclass('InteractionNode')
export class InteractionNode extends Component {
    
    @property({ type: Sprite, tooltip: '节点图标精灵' })
    public iconSprite: Sprite | null = null;
    
    @property({ type: Label, tooltip: '节点名称标签' })
    public nameLabel: Label | null = null;
    
    @property({ type: Button, tooltip: '节点按钮组件' })
    public nodeButton: Button | null = null;
    
    @property({ type: Node, tooltip: '连接线容器' })
    public connectionContainer: Node | null = null;
    
    @property({ tooltip: '节点大小' })
    public nodeSize: number = 80;
    
    @property({ tooltip: '是否显示名称' })
    public showName: boolean = true;
    
    @property({ tooltip: '是否启用动画' })
    public enableAnimation: boolean = true;
    
    // 私有属性
    private _nodeData: IInteractionNodeData | null = null;
    private _currentState: NodeState = NodeState.Locked;
    private _onClickCallback: ((nodeData: IInteractionNodeData) => void) | null = null;
    
    // 视觉配置
    private _visualConfig: INodeVisualConfig = {
        lockedColor: new Color(100, 100, 100, 255),      // 灰色
        unlockedColor: new Color(100, 200, 255, 255),    // 蓝色
        activeColor: new Color(255, 200, 100, 255),      // 橙色
        completedColor: new Color(100, 255, 100, 255),   // 绿色
        nodeSize: 80,
        showConnections: true
    };

    protected onLoad(): void {
        console.log('🎯 InteractionNode: 交互节点组件加载');
        this.initializeComponents();
        this.setupButton();
    }

    protected onDestroy(): void {
        this.cleanupButton();
    }

    /**
     * 初始化组件
     */
    private initializeComponents(): void {
        // 自动查找图标精灵
        if (!this.iconSprite) {
            this.iconSprite = this.node.getComponent(Sprite);
            if (!this.iconSprite) {
                this.iconSprite = this.node.getComponentInChildren(Sprite);
            }
        }
        
        // 自动查找名称标签
        if (!this.nameLabel) {
            this.nameLabel = this.node.getComponentInChildren(Label);
        }
        
        // 自动查找按钮组件
        if (!this.nodeButton) {
            this.nodeButton = this.node.getComponent(Button);
            if (!this.nodeButton) {
                this.nodeButton = this.node.addComponent(Button);
            }
        }
        
        // 设置UITransform
        const uiTransform = this.node.getComponent(UITransform);
        if (uiTransform) {
            uiTransform.setContentSize(this.nodeSize, this.nodeSize);
        }
        
        console.log('🎯 InteractionNode: 组件初始化完成', {
            iconSprite: !!this.iconSprite,
            nameLabel: !!this.nameLabel,
            nodeButton: !!this.nodeButton
        });
    }

    /**
     * 设置按钮事件
     */
    private setupButton(): void {
        if (this.nodeButton) {
            this.nodeButton.node.on(Button.EventType.CLICK, this.onNodeClick, this);
        }
    }

    /**
     * 清理按钮事件
     */
    private cleanupButton(): void {
        if (this.nodeButton) {
            this.nodeButton.node.off(Button.EventType.CLICK, this.onNodeClick, this);
        }
    }

    /**
     * 节点点击处理
     */
    private onNodeClick(): void {
        if (!this._nodeData) return;
        
        console.log('🎯 InteractionNode: 节点被点击', this._nodeData.name);
        
        // 播放点击动画
        if (this.enableAnimation) {
            this.playClickAnimation();
        }
        
        // 触发回调
        if (this._onClickCallback) {
            this._onClickCallback(this._nodeData);
        }
    }

    /**
     * 播放点击动画
     */
    private playClickAnimation(): void {
        tween(this.node)
            .to(0.1, { scale: new Vec3(1.2, 1.2, 1) })
            .to(0.1, { scale: new Vec3(1, 1, 1) })
            .start();
    }

    /**
     * 播放解锁动画
     */
    private playUnlockAnimation(): void {
        if (!this.enableAnimation) return;
        
        // 缩放动画
        tween(this.node)
            .to(0.3, { scale: new Vec3(1.3, 1.3, 1) })
            .to(0.2, { scale: new Vec3(1, 1, 1) })
            .start();
        
        // 可以添加更多特效，如粒子效果等
    }

    /**
     * 更新节点视觉状态
     */
    private updateVisualState(): void {
        if (!this.iconSprite) return;
        
        let targetColor: Color;
        
        switch (this._currentState) {
            case NodeState.Locked:
                targetColor = this._visualConfig.lockedColor;
                break;
            case NodeState.Unlocked:
                targetColor = this._visualConfig.unlockedColor;
                break;
            case NodeState.Active:
                targetColor = this._visualConfig.activeColor;
                break;
            case NodeState.Completed:
                targetColor = this._visualConfig.completedColor;
                break;
            default:
                targetColor = this._visualConfig.lockedColor;
        }
        
        this.iconSprite.color = targetColor;
        
        // 更新按钮交互状态
        if (this.nodeButton) {
            this.nodeButton.interactable = (this._currentState !== NodeState.Locked);
        }
        
        // 更新透明度
        const alpha = (this._currentState === NodeState.Locked) ? 0.5 : 1.0;
        this.node.getComponent(UITransform)?.setOpacity(alpha * 255);
    }

    /**
     * 根据节点类型设置图标
     */
    private updateIconByType(): void {
        if (!this._nodeData || !this.iconSprite) return;
        
        // 这里可以根据节点类型设置不同的图标
        // 目前使用颜色区分，实际项目中可以加载不同的图片资源
        
        let baseColor: Color;
        
        switch (this._nodeData.type) {
            case 'action':
                baseColor = new Color(255, 100, 100, 255); // 红色
                break;
            case 'location':
                baseColor = new Color(100, 255, 100, 255); // 绿色
                break;
            case 'npc':
                baseColor = new Color(255, 255, 100, 255); // 黄色
                break;
            case 'quest':
                baseColor = new Color(255, 100, 255, 255); // 紫色
                break;
            case 'resource':
                baseColor = new Color(100, 255, 255, 255); // 青色
                break;
            default:
                baseColor = new Color(200, 200, 200, 255); // 灰色
        }
        
        // 根据状态调整颜色
        if (this._currentState === NodeState.Locked) {
            baseColor = this._visualConfig.lockedColor;
        }
        
        this.iconSprite.color = baseColor;
    }

    // ==================== 公共API ====================

    /**
     * 初始化节点数据
     */
    public initialize(nodeData: IInteractionNodeData): void {
        this._nodeData = nodeData;
        this._currentState = nodeData.unlocked ? NodeState.Unlocked : NodeState.Locked;
        
        // 更新名称
        if (this.nameLabel && this.showName) {
            this.nameLabel.string = nodeData.name;
        }
        
        // 更新图标
        this.updateIconByType();
        
        // 更新视觉状态
        this.updateVisualState();
        
        console.log('🎯 InteractionNode: 节点初始化完成', nodeData.name);
    }

    /**
     * 设置解锁状态
     */
    public setUnlocked(unlocked: boolean): void {
        if (!this._nodeData) return;
        
        const wasLocked = (this._currentState === NodeState.Locked);
        this._nodeData.unlocked = unlocked;
        this._currentState = unlocked ? NodeState.Unlocked : NodeState.Locked;
        
        // 如果从锁定变为解锁，播放解锁动画
        if (wasLocked && unlocked) {
            this.playUnlockAnimation();
        }
        
        this.updateVisualState();
        
        console.log('🎯 InteractionNode: 设置解锁状态', this._nodeData.name, unlocked);
    }

    /**
     * 设置激活状态
     */
    public setActive(active: boolean): void {
        if (!this._nodeData || this._currentState === NodeState.Locked) return;
        
        this._currentState = active ? NodeState.Active : NodeState.Unlocked;
        this.updateVisualState();
        
        console.log('🎯 InteractionNode: 设置激活状态', this._nodeData.name, active);
    }

    /**
     * 设置完成状态
     */
    public setCompleted(completed: boolean): void {
        if (!this._nodeData || this._currentState === NodeState.Locked) return;
        
        this._currentState = completed ? NodeState.Completed : NodeState.Unlocked;
        this.updateVisualState();
        
        console.log('🎯 InteractionNode: 设置完成状态', this._nodeData.name, completed);
    }

    /**
     * 设置点击回调
     */
    public setOnClickCallback(callback: (nodeData: IInteractionNodeData) => void): void {
        this._onClickCallback = callback;
    }

    /**
     * 获取节点数据
     */
    public getNodeData(): IInteractionNodeData | null {
        return this._nodeData;
    }

    /**
     * 获取当前状态
     */
    public getCurrentState(): NodeState {
        return this._currentState;
    }

    /**
     * 设置视觉配置
     */
    public setVisualConfig(config: Partial<INodeVisualConfig>): void {
        this._visualConfig = { ...this._visualConfig, ...config };
        this.updateVisualState();
    }

    /**
     * 显示节点
     */
    public show(): void {
        this.node.active = true;
    }

    /**
     * 隐藏节点
     */
    public hide(): void {
        this.node.active = false;
    }
}
