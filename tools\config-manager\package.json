{"name": "config-manager", "version": "1.0.0", "description": "配置表管理工具", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "ts-node src/index.ts", "validate": "ts-node src/cli.ts validate", "generate-manifest": "ts-node src/cli.ts generate-manifest", "sync": "ts-node src/cli.ts sync", "backup": "ts-node src/cli.ts backup", "restore": "ts-node src/cli.ts restore", "test": "jest", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "dependencies": {"commander": "^9.4.1", "chalk": "^4.1.2", "fs-extra": "^11.1.0", "glob": "^8.0.3", "ajv": "^8.12.0", "crypto": "^1.0.1", "axios": "^1.3.0", "lodash": "^4.17.21"}, "devDependencies": {"@types/node": "^18.15.0", "@types/fs-extra": "^11.0.1", "@types/lodash": "^4.14.191", "typescript": "^4.9.5", "ts-node": "^10.9.1", "jest": "^29.5.0", "@types/jest": "^29.5.0", "eslint": "^8.36.0", "@typescript-eslint/eslint-plugin": "^5.55.0", "@typescript-eslint/parser": "^5.55.0", "prettier": "^2.8.4"}, "keywords": ["config", "management", "game", "tool"], "author": "放置团队", "license": "MIT"}