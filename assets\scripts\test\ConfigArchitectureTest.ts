/**
 * Copyright (c) 2025 "放置". All rights reserved.
 * 配置架构测试 - 验证修复效果和分包策略
 */

import { _decorator, Component, Node, Label, AssetManager } from 'cc';
import { EnhancedConfigManager } from '../managers/EnhancedConfigManager';
import { ConfigSecurityLevel, ConfigType } from '../../../../shared/configs/types/config-types';
import { PlayerInfo } from '../../../../shared/configs/types/game-types';
import { ApiEndpoint } from '../../../../shared/configs/types/api-types';

const { ccclass, property } = _decorator;

@ccclass('ConfigArchitectureTest')
export class ConfigArchitectureTest extends Component {
  @property(Label)
  statusLabel: Label = null!;

  @property(Label)
  resultLabel: Label = null!;

  private testResults: string[] = [];
  private configManager: EnhancedConfigManager = null!;

  async onLoad() {
    this.statusLabel.string = '🔧 开始配置架构测试...';
    this.resultLabel.string = '';
    
    try {
      await this.runAllTests();
    } catch (error) {
      this.addResult(`❌ 测试执行失败: ${error.message}`);
      this.statusLabel.string = '❌ 测试失败';
    }
  }

  /**
   * 运行所有测试
   */
  private async runAllTests(): Promise<void> {
    this.addResult('🚀 开始配置架构测试');
    
    // 测试1: Shared类型定义
    await this.testSharedTypes();
    
    // 测试2: 配置管理器初始化
    await this.testConfigManagerInit();
    
    // 测试3: Bundle加载测试
    await this.testBundleLoading();
    
    // 测试4: 配置加载测试
    await this.testConfigLoading();
    
    // 测试5: 分包策略测试
    await this.testBundleStrategy();
    
    // 测试6: 缓存机制测试
    await this.testCacheMechanism();
    
    this.statusLabel.string = '✅ 测试完成';
    this.addResult('🎉 所有测试完成');
  }

  /**
   * 测试Shared类型定义
   */
  private async testSharedTypes(): Promise<void> {
    this.addResult('📋 测试1: Shared类型定义');
    
    try {
      // 测试类型导入
      const securityLevel: ConfigSecurityLevel = ConfigSecurityLevel.PUBLIC;
      const configType: ConfigType = ConfigType.STATIC;
      const endpoint: ApiEndpoint = ApiEndpoint.LOGIN;
      
      // 测试类型使用
      const testPlayer: PlayerInfo = {
        playerId: 'test-001',
        username: '测试玩家',
        level: 1,
        exp: 0,
        gold: 100,
        energy: 100,
        maxEnergy: 100,
        lastLoginTime: Date.now(),
        createTime: Date.now()
      };
      
      this.addResult(`  ✅ 类型定义正常: ${securityLevel}, ${configType}, ${endpoint}`);
      this.addResult(`  ✅ 类型使用正常: 玩家${testPlayer.username}`);
      
    } catch (error) {
      this.addResult(`  ❌ Shared类型测试失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 测试配置管理器初始化
   */
  private async testConfigManagerInit(): Promise<void> {
    this.addResult('📋 测试2: 配置管理器初始化');
    
    try {
      this.configManager = EnhancedConfigManager.getInstance();
      await this.configManager.initialize();
      
      const status = this.configManager.getManagerStatus();
      this.addResult(`  ✅ 配置管理器初始化成功`);
      this.addResult(`  📊 Bundle状态: ${status.bundleStates.size}个`);
      this.addResult(`  📊 缓存大小: ${status.cacheSize}项`);
      
    } catch (error) {
      this.addResult(`  ❌ 配置管理器初始化失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 测试Bundle加载
   */
  private async testBundleLoading(): Promise<void> {
    this.addResult('📋 测试3: Bundle加载测试');
    
    const testBundles = ['core', 'ui-basic', 'features'];
    
    for (const bundleName of testBundles) {
      try {
        const startTime = Date.now();
        const bundle = await this.configManager.loadBundle(bundleName);
        const loadTime = Date.now() - startTime;
        
        if (bundle) {
          this.addResult(`  ✅ ${bundleName} Bundle加载成功 (${loadTime}ms)`);
        } else {
          this.addResult(`  ⚠️ ${bundleName} Bundle加载返回null`);
        }
        
      } catch (error) {
        this.addResult(`  ⚠️ ${bundleName} Bundle加载失败: ${error.message}`);
        // 不抛出错误，继续测试其他Bundle
      }
    }
  }

  /**
   * 测试配置加载
   */
  private async testConfigLoading(): Promise<void> {
    this.addResult('📋 测试4: 配置加载测试');
    
    try {
      // 测试从configs bundle加载配置
      const config = await this.configManager.loadConfigFromBundle(
        'configs',
        'display/locations',
        {
          securityLevel: ConfigSecurityLevel.PUBLIC,
          type: ConfigType.STATIC
        }
      );
      
      if (config) {
        this.addResult(`  ✅ 配置加载成功: display/locations`);
      } else {
        this.addResult(`  ⚠️ 配置加载返回null`);
      }
      
    } catch (error) {
      this.addResult(`  ⚠️ 配置加载失败: ${error.message}`);
      // 测试降级方案
      this.addResult(`  🔄 测试降级方案...`);
    }
  }

  /**
   * 测试分包策略
   */
  private async testBundleStrategy(): Promise<void> {
    this.addResult('📋 测试5: 分包策略测试');
    
    try {
      const bundleStates = this.configManager.getBundleStates();
      
      // 检查Bundle优先级
      const priorities = new Map<number, string[]>();
      bundleStates.forEach((state, name) => {
        if (!priorities.has(state.priority)) {
          priorities.set(state.priority, []);
        }
        priorities.get(state.priority)!.push(name);
      });
      
      this.addResult(`  📊 分包策略分析:`);
      Array.from(priorities.keys()).sort().forEach(priority => {
        const bundles = priorities.get(priority)!;
        this.addResult(`    优先级${priority}: ${bundles.join(', ')}`);
      });
      
      // 检查核心Bundle是否已加载
      const coreBundles = ['core', 'ui-basic'];
      const loadedCore = coreBundles.filter(name => {
        const state = bundleStates.get(name);
        return state && state.loaded;
      });
      
      this.addResult(`  ✅ 核心Bundle加载: ${loadedCore.length}/${coreBundles.length}`);
      
    } catch (error) {
      this.addResult(`  ❌ 分包策略测试失败: ${error.message}`);
    }
  }

  /**
   * 测试缓存机制
   */
  private async testCacheMechanism(): Promise<void> {
    this.addResult('📋 测试6: 缓存机制测试');
    
    try {
      const status1 = this.configManager.getManagerStatus();
      const initialCacheSize = status1.cacheSize;
      
      // 清除缓存
      this.configManager.clearCache();
      
      const status2 = this.configManager.getManagerStatus();
      const clearedCacheSize = status2.cacheSize;
      
      this.addResult(`  📊 缓存清除前: ${initialCacheSize}项`);
      this.addResult(`  📊 缓存清除后: ${clearedCacheSize}项`);
      
      if (clearedCacheSize < initialCacheSize) {
        this.addResult(`  ✅ 缓存清除功能正常`);
      } else {
        this.addResult(`  ⚠️ 缓存清除可能未生效`);
      }
      
      // 测试缓存预热
      await this.configManager.warmupCache();
      
      const status3 = this.configManager.getManagerStatus();
      this.addResult(`  📊 缓存预热后: ${status3.cacheSize}项`);
      
    } catch (error) {
      this.addResult(`  ❌ 缓存机制测试失败: ${error.message}`);
    }
  }

  /**
   * 添加测试结果
   */
  private addResult(message: string): void {
    this.testResults.push(message);
    this.resultLabel.string = this.testResults.join('\n');
    console.log(message);
  }

  /**
   * 获取测试报告
   */
  public getTestReport(): string {
    return this.testResults.join('\n');
  }

  /**
   * 手动触发测试
   */
  public async runManualTest(): Promise<void> {
    this.testResults = [];
    await this.runAllTests();
  }
}
