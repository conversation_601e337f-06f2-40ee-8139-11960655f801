# ==================== 基础服务器配置 ====================
NODE_ENV=development
PORT=3000
HOST=localhost

# API 基础URL (前端连接地址)
API_BASE_URL=http://localhost:3000

# 生产环境配置 (部署到云服务器时使用)
API_BASE_URL_PROD=https://your-aliyun-server.com
PORT_PROD=3000

# ==================== 数据库配置 ====================
# 本地开发数据库
MONGODB_URI=mongodb://localhost:27017/idlegame_dev
MONGODB_TEST_URI=mongodb://localhost:27017/idlegame_test

# 生产环境数据库 (云服务器)
MONGODB_URI_PROD=mongodb://your-production-server:27017/idlegame_prod

# ==================== Redis配置 ====================
# 本地Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 生产环境Redis
REDIS_HOST_PROD=your-production-redis-host
REDIS_PORT_PROD=6379
REDIS_PASSWORD_PROD=your-redis-password
REDIS_DB_PROD=0

# JWT配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your_jwt_refresh_secret_key_here
JWT_REFRESH_EXPIRES_IN=30d

# 加密配置
BCRYPT_ROUNDS=12

# 日志配置
LOG_LEVEL=debug
LOG_FILE_PATH=logs/app.log

# 微信小程序配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# 抖音小程序配置
DOUYIN_APP_ID=your_douyin_app_id
DOUYIN_APP_SECRET=your_douyin_app_secret

# 游戏配置
GAME_VERSION=1.0.0
MAX_PLAYERS_PER_ROOM=100
IDLE_TIMEOUT=300000

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090

# ==================== 安全配置 ====================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS配置 (本地开发允许所有来源)
CORS_ORIGIN=*

# 生产环境CORS (限制特定域名)
CORS_ORIGIN_PROD=https://your-frontend-domain.com

# ==================== 部署环境标识 ====================
# 当前部署环境: local | remote | production
DEPLOYMENT_ENV=local

# 地点配置文件路径 (相对于项目根目录)
LOCATION_CONFIG_PATH=assets/bundles/locations

# ==================== 缓存配置 ====================
# 地点配置缓存时间 (秒)
LOCATION_CONFIG_CACHE_TTL=3600

# 启用缓存刷新API
ENABLE_CACHE_REFRESH=true
