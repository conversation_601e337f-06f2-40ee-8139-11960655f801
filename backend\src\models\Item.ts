import mongoose, { Document, Schema } from 'mongoose';

/**
 * 物品类型枚举（与前端IItemData.ts保持一致）
 */
export enum ItemType {
  WEAPON = 'weapon',
  ARMOR = 'armor',
  ACCESSORY = 'accessory',
  CONSUMABLE = 'consumable',
  MATERIAL = 'material',
  QUEST = 'quest',
  TREASURE = 'treasure',
  CURRENCY = 'currency',
}

/**
 * 物品稀有度枚举
 */
export enum ItemRarity {
  COMMON = 'common',
  UNCOMMON = 'uncommon',
  RARE = 'rare',
  EPIC = 'epic',
  LEGENDARY = 'legendary',
  MYTHIC = 'mythic',
}

/**
 * 装备槽位枚举
 */
export enum EquipSlot {
  WEAPON = 'weapon',
  ARMOR = 'armor',
  HELMET = 'helmet',
  BOOTS = 'boots',
  GLOVES = 'gloves',
  ACCESSORY1 = 'accessory1',
  ACCESSORY2 = 'accessory2',
}

/**
 * 物品属性接口
 */
export interface IItemStats {
  strength?: number;
  agility?: number;
  intelligence?: number;
  vitality?: number;
  spirit?: number;
  attack?: number;
  defense?: number;
  health?: number;
  mana?: number;
  criticalRate?: number;
  criticalDamage?: number;
  accuracy?: number;
  dodge?: number;
  penetration?: number;
  resistance?: number;
}

/**
 * 物品使用要求接口
 */
export interface IItemRequirements {
  level: number;
  class?: string[];
  attributes?: {
    strength?: number;
    agility?: number;
    intelligence?: number;
    vitality?: number;
    spirit?: number;
  };
}

/**
 * 物品使用效果接口
 */
export interface IItemUseEffect {
  type: string;
  value: number;
  duration?: number;
  target: string;
  description: string;
}

/**
 * 物品数据接口（基于前端IItemData.ts）
 */
export interface IItem extends Document {
  _id: mongoose.Types.ObjectId;
  id: string; // 物品唯一标识符
  name: string;
  description: string;
  type: ItemType;
  rarity: ItemRarity;
  value: number;
  stackable: boolean;
  maxStack: number;
  usable: boolean;
  equipable: boolean;
  stats: IItemStats;
  requirements: IItemRequirements;
  useEffects?: IItemUseEffect[];
  equipSlot?: EquipSlot;
  iconPath?: string;
  modelPath?: string;
  rewardTableId?: string;
  cooldown?: number;
  tradeable?: boolean;
  droppable?: boolean;
  
  // 额外的服务器端字段
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 用户物品接口（背包中的物品实例）
 */
export interface IUserItem extends Document {
  _id: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  itemId: string; // 引用物品配置的ID
  quantity: number;
  slot: number;
  acquiredTime: Date;
  equipped: boolean;
  instanceId: string;
  enhanceLevel?: number;
  enchantments?: IEnchantment[];
  durability?: number;
  maxDurability?: number;
  
  createdAt: Date;
  updatedAt: Date;
  
  // 方法
  canUse(): boolean;
  use(): Promise<void>;
  enhance(): Promise<boolean>;
  repair(amount: number): void;
}

/**
 * 附魔接口
 */
export interface IEnchantment {
  id: string;
  type: string;
  level: number;
  value: number;
  description: string;
}

/**
 * 物品Schema（配置数据）
 */
const ItemSchema = new Schema<IItem>({
  id: {
    type: String,
    required: [true, '物品ID是必填项'],
    unique: true,
    trim: true,
    index: true,
  },
  name: {
    type: String,
    required: [true, '物品名称是必填项'],
    trim: true,
    maxlength: [50, '物品名称最多50个字符'],
  },
  description: {
    type: String,
    required: [true, '物品描述是必填项'],
    maxlength: [500, '物品描述最多500个字符'],
  },
  type: {
    type: String,
    enum: Object.values(ItemType),
    required: [true, '物品类型是必填项'],
    index: true,
  },
  rarity: {
    type: String,
    enum: Object.values(ItemRarity),
    required: [true, '物品稀有度是必填项'],
    index: true,
  },
  value: {
    type: Number,
    required: [true, '物品价值是必填项'],
    min: [0, '物品价值不能为负数'],
  },
  stackable: {
    type: Boolean,
    default: false,
  },
  maxStack: {
    type: Number,
    default: 1,
    min: [1, '最大堆叠数量不能小于1'],
  },
  usable: {
    type: Boolean,
    default: false,
  },
  equipable: {
    type: Boolean,
    default: false,
  },
  stats: {
    strength: { type: Number, default: 0 },
    agility: { type: Number, default: 0 },
    intelligence: { type: Number, default: 0 },
    vitality: { type: Number, default: 0 },
    spirit: { type: Number, default: 0 },
    attack: { type: Number, default: 0 },
    defense: { type: Number, default: 0 },
    health: { type: Number, default: 0 },
    mana: { type: Number, default: 0 },
    criticalRate: { type: Number, default: 0 },
    criticalDamage: { type: Number, default: 0 },
    accuracy: { type: Number, default: 0 },
    dodge: { type: Number, default: 0 },
    penetration: { type: Number, default: 0 },
    resistance: { type: Number, default: 0 },
  },
  requirements: {
    level: {
      type: Number,
      default: 1,
      min: [1, '所需等级不能小于1'],
    },
    class: [{
      type: String,
    }],
    attributes: {
      strength: { type: Number, min: 0 },
      agility: { type: Number, min: 0 },
      intelligence: { type: Number, min: 0 },
      vitality: { type: Number, min: 0 },
      spirit: { type: Number, min: 0 },
    },
  },
  useEffects: [{
    type: {
      type: String,
      required: true,
    },
    value: {
      type: Number,
      required: true,
    },
    duration: {
      type: Number,
      default: 0,
    },
    target: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
      maxlength: [200, '效果描述最多200个字符'],
    },
  }],
  equipSlot: {
    type: String,
    enum: Object.values(EquipSlot),
  },
  iconPath: {
    type: String,
    trim: true,
  },
  modelPath: {
    type: String,
    trim: true,
  },
  rewardTableId: {
    type: String,
    trim: true,
  },
  cooldown: {
    type: Number,
    default: 0,
    min: [0, '冷却时间不能为负数'],
  },
  tradeable: {
    type: Boolean,
    default: true,
  },
  droppable: {
    type: Boolean,
    default: true,
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

/**
 * 用户物品Schema
 */
const UserItemSchema = new Schema<IUserItem>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '用户ID是必填项'],
    index: true,
  },
  itemId: {
    type: String,
    required: [true, '物品ID是必填项'],
    index: true,
  },
  quantity: {
    type: Number,
    required: [true, '数量是必填项'],
    min: [1, '数量不能小于1'],
  },
  slot: {
    type: Number,
    required: [true, '背包位置是必填项'],
    min: [0, '背包位置不能为负数'],
  },
  acquiredTime: {
    type: Date,
    default: Date.now,
  },
  equipped: {
    type: Boolean,
    default: false,
    index: true,
  },
  instanceId: {
    type: String,
    required: [true, '实例ID是必填项'],
    unique: true,
    default: () => new mongoose.Types.ObjectId().toString(),
  },
  enhanceLevel: {
    type: Number,
    default: 0,
    min: [0, '强化等级不能为负数'],
    max: [15, '强化等级不能超过15'],
  },
  enchantments: [{
    id: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      required: true,
    },
    level: {
      type: Number,
      required: true,
      min: 1,
    },
    value: {
      type: Number,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
  }],
  durability: {
    type: Number,
    min: [0, '耐久度不能为负数'],
  },
  maxDurability: {
    type: Number,
    min: [1, '最大耐久度不能小于1'],
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// 索引
ItemSchema.index({ id: 1 });
ItemSchema.index({ type: 1 });
ItemSchema.index({ rarity: 1 });
ItemSchema.index({ equipSlot: 1 });

UserItemSchema.index({ userId: 1, slot: 1 }, { unique: true });
UserItemSchema.index({ userId: 1, equipped: 1 });
UserItemSchema.index({ instanceId: 1 });

// 虚拟字段
UserItemSchema.virtual('durabilityPercentage').get(function() {
  if (!this.durability || !this.maxDurability) return 100;
  return (this.durability / this.maxDurability * 100).toFixed(1);
});

UserItemSchema.virtual('isBroken').get(function() {
  return this.durability !== undefined && this.durability <= 0;
});

// 中间件：设置默认耐久度
UserItemSchema.pre('save', async function(next) {
  if (this.isNew && this.durability === undefined) {
    // 获取物品配置，设置默认耐久度
    const itemConfig = await Item.findOne({ id: this.itemId });
    if (itemConfig && itemConfig.equipable) {
      this.maxDurability = 100; // 默认最大耐久度
      this.durability = 100;
    }
  }
  next();
});

// 实例方法：检查是否可以使用
UserItemSchema.methods.canUse = function(): boolean {
  return this.quantity > 0 && (!this.durability || this.durability > 0);
};

// 实例方法：使用物品
UserItemSchema.methods.use = async function(): Promise<void> {
  if (!this.canUse()) {
    throw new Error('物品无法使用');
  }
  
  // 获取物品配置数据
  const itemConfig = await Item.findOne({ id: this.itemId });
  if (!itemConfig || !itemConfig.usable) {
    throw new Error('物品不可使用');
  }
  
  // 消耗物品
  if (itemConfig.stackable) {
    this.quantity -= 1;
    if (this.quantity <= 0) {
      await this.deleteOne();
      return;
    }
  }
  
  // 减少耐久度
  if (this.durability !== undefined) {
    this.durability = Math.max(0, this.durability - 1);
  }
  
  await this.save();
};

// 实例方法：强化物品
UserItemSchema.methods.enhance = async function(): Promise<boolean> {
  if (!this.enhanceLevel || this.enhanceLevel >= 15) {
    return false;
  }
  
  // 强化成功率计算（简单实现）
  const successRate = Math.max(0.1, 1 - this.enhanceLevel * 0.1);
  const success = Math.random() < successRate;
  
  if (success) {
    this.enhanceLevel += 1;
    await this.save();
  }
  
  return success;
};

// 实例方法：修理物品
UserItemSchema.methods.repair = function(amount: number): void {
  if (this.durability !== undefined && this.maxDurability !== undefined) {
    this.durability = Math.min(this.maxDurability, this.durability + amount);
  }
};

export const Item = mongoose.model<IItem>('Item', ItemSchema);
export const UserItem = mongoose.model<IUserItem>('UserItem', UserItemSchema);
