/**
 * 简化版行为面板组件
 * 用于演示行动条功能
 */

import { _decorator, Component, Node, ProgressBar, Label, Button, find } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('BehaviorPanelSimple')
export class BehaviorPanelSimple extends Component {
    
    @property({ type: ProgressBar, tooltip: '行动条进度条' })
    public actionProgressBar: ProgressBar | null = null;
    
    @property({ type: Label, tooltip: '行动条文本' })
    public actionLabel: Label | null = null;
    
    @property({ type: [Button], tooltip: '行为按钮列表' })
    public behaviorButtons: Button[] = [];
    
    // 私有属性
    private _isExecuting: boolean = false;
    private _currentAction: string = '';
    private _actionDuration: number = 3.0;
    private _currentTime: number = 0;

    protected onLoad(): void {
        console.log('🎭 BehaviorPanelSimple: 简化版行为面板加载');
        this.initializeComponents();
        this.bindEvents();
    }

    protected start(): void {
        this.resetActionBar();
    }

    protected onDestroy(): void {
        this.stopAction();
        this.unbindEvents();
    }

    /**
     * 初始化组件
     */
    private initializeComponents(): void {
        // 自动查找进度条
        if (!this.actionProgressBar) {
            this.actionProgressBar = this.node.getComponentInChildren(ProgressBar);
        }
        
        // 自动查找标签
        if (!this.actionLabel) {
            const labels = this.node.getComponentsInChildren(Label);
            // 查找名为ProgressLabel的标签
            for (const label of labels) {
                if (label.node.name === 'ProgressLabel') {
                    this.actionLabel = label;
                    break;
                }
            }
            // 如果没找到，使用第一个标签
            if (!this.actionLabel && labels.length > 0) {
                this.actionLabel = labels[0];
            }
        }
        
        // 自动查找按钮
        if (this.behaviorButtons.length === 0) {
            this.behaviorButtons = this.node.getComponentsInChildren(Button);
        }
        
        console.log('🎭 BehaviorPanelSimple: 组件初始化完成', {
            progressBar: !!this.actionProgressBar,
            label: !!this.actionLabel,
            buttonsCount: this.behaviorButtons.length
        });
    }

    /**
     * 绑定事件
     */
    private bindEvents(): void {
        this.behaviorButtons.forEach((button, index) => {
            if (button && button.node) {
                button.node.on(Button.EventType.CLICK, () => {
                    this.onBehaviorButtonClick(button, index);
                }, this);
            }
        });
        
        console.log('🎭 BehaviorPanelSimple: 事件绑定完成');
    }

    /**
     * 解绑事件
     */
    private unbindEvents(): void {
        this.behaviorButtons.forEach(button => {
            if (button && button.node) {
                button.node.off(Button.EventType.CLICK);
            }
        });
    }

    /**
     * 行为按钮点击处理
     */
    private onBehaviorButtonClick(button: Button, index: number): void {
        if (this._isExecuting) {
            console.warn('🎭 BehaviorPanelSimple: 正在执行其他行为');
            return;
        }
        
        const buttonName = button.node.name;
        console.log('🎭 BehaviorPanelSimple: 行为按钮点击', { buttonName, index });
        
        // 根据按钮名称确定行为
        let actionName = '未知行为';
        let duration = 3.0;
        
        if (buttonName.includes('Attack') || buttonName.includes('攻击')) {
            actionName = '攻击';
            duration = 2.0;
        } else if (buttonName.includes('Defend') || buttonName.includes('防御')) {
            actionName = '防御';
            duration = 1.5;
        } else if (buttonName.includes('Skill') || buttonName.includes('技能')) {
            actionName = '技能';
            duration = 3.0;
        } else if (buttonName.includes('Item') || buttonName.includes('道具')) {
            actionName = '道具';
            duration = 1.0;
        }
        
        this.startAction(actionName, duration);
    }

    /**
     * 开始执行行为
     */
    public startAction(actionName: string, duration: number = 3.0): void {
        if (this._isExecuting) {
            console.warn('🎭 BehaviorPanelSimple: 已在执行行为');
            return;
        }
        
        console.log('🎭 BehaviorPanelSimple: 开始执行行为', { actionName, duration });
        
        this._isExecuting = true;
        this._currentAction = actionName;
        this._actionDuration = duration;
        this._currentTime = 0;
        
        // 禁用按钮
        this.setButtonsEnabled(false);
        
        // 开始进度更新 - 提高更新频率到60FPS
        this.schedule(this.updateProgress, 1/60);
        
        // 更新UI
        this.updateActionUI();
    }

    /**
     * 停止当前行为
     */
    public stopAction(): void {
        if (!this._isExecuting) {
            return;
        }
        
        console.log('🎭 BehaviorPanelSimple: 停止行为');
        
        this._isExecuting = false;
        this.unschedule(this.updateProgress);
        
        // 启用按钮
        this.setButtonsEnabled(true);
        
        // 重置UI
        this.resetActionBar();
    }

    /**
     * 更新进度
     */
    private updateProgress(): void {
        if (!this._isExecuting) {
            return;
        }

        // 使用更精确的时间步长 (1/60秒)
        this._currentTime += 1/60;
        const progress = Math.min(this._currentTime / this._actionDuration, 1.0);

        // 更新进度条
        if (this.actionProgressBar) {
            this.actionProgressBar.progress = progress;
        }

        // 更新文本
        this.updateActionUI();

        // 检查是否完成
        if (progress >= 1.0) {
            this.onActionComplete();
        }
    }

    /**
     * 行为完成处理
     */
    private onActionComplete(): void {
        console.log('🎭 BehaviorPanelSimple: 行为完成', this._currentAction);
        
        const completedAction = this._currentAction;
        this._isExecuting = false;
        this.unschedule(this.updateProgress);
        
        // 启用按钮
        this.setButtonsEnabled(true);
        
        // 显示完成状态
        if (this.actionLabel) {
            this.actionLabel.string = `${completedAction} 完成！`;
        }
        
        // 延迟重置
        this.scheduleOnce(() => {
            this.resetActionBar();
        }, 1.0);
    }

    /**
     * 更新行动条UI
     */
    private updateActionUI(): void {
        if (!this.actionLabel) {
            return;
        }
        
        if (this._isExecuting) {
            const current = Math.floor(this._currentTime * 10) / 10;
            const total = this._actionDuration;
            const percent = Math.floor((this._currentTime / this._actionDuration) * 100);
            
            this.actionLabel.string = `${this._currentAction}: ${current.toFixed(1)}s / ${total.toFixed(1)}s (${percent}%)`;
        } else {
            this.actionLabel.string = '准备中...';
        }
    }

    /**
     * 重置行动条
     */
    private resetActionBar(): void {
        this._currentTime = 0;
        this._currentAction = '';
        
        if (this.actionProgressBar) {
            this.actionProgressBar.progress = 0;
        }
        
        if (this.actionLabel) {
            this.actionLabel.string = '准备中...';
        }
    }

    /**
     * 设置按钮启用状态
     */
    private setButtonsEnabled(enabled: boolean): void {
        this.behaviorButtons.forEach(button => {
            if (button && button.node) {
                button.interactable = enabled;
            }
        });
    }

    // ==================== 公共API ====================

    /**
     * 是否正在执行行为
     */
    public isExecuting(): boolean {
        return this._isExecuting;
    }

    /**
     * 获取当前行为
     */
    public getCurrentAction(): string {
        return this._currentAction;
    }

    /**
     * 获取当前进度（0-1）
     */
    public getProgress(): number {
        return this._actionDuration > 0 ? this._currentTime / this._actionDuration : 0;
    }

    /**
     * 设置行为持续时间
     */
    public setActionDuration(duration: number): void {
        this._actionDuration = Math.max(0.1, duration);
    }
}
