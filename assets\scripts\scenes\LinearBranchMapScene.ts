/**
 * 线性分支地图场景脚本
 * 专门用于演示原型图中的中间交互区域实现
 * 整合LinearBranchMapPanel和BehaviorPanel
 */

import { _decorator, Component, Node, Canvas, find, input, Input, EventKeyboard, KeyCode } from 'cc';
import { EventManager } from '../managers/EventManager';
import { InteractiveMapController } from '../ui/controllers/InteractiveMapController';
import { LinearBranchMapPanel, IBranchNodeData } from '../ui/panels/LinearBranchMapPanel';
import { BehaviorPanel } from '../ui/panels/BehaviorPanel';

const { ccclass, property } = _decorator;

@ccclass('LinearBranchMapScene')
export class LinearBranchMapScene extends Component {
    
    @property({ type: InteractiveMapController, tooltip: '交互地图控制器' })
    public mapController: InteractiveMapController | null = null;
    
    @property({ type: LinearBranchMapPanel, tooltip: '线性分支地图面板' })
    public branchMapPanel: LinearBranchMapPanel | null = null;
    
    @property({ type: BehaviorPanel, tooltip: '行为面板' })
    public behaviorPanel: BehaviorPanel | null = null;
    
    @property({ type: Node, tooltip: 'UI根节点' })
    public uiRoot: Node | null = null;
    
    @property({ tooltip: '是否自动创建UI结构' })
    public autoCreateUI: boolean = true;
    
    @property({ tooltip: '是否启用调试模式' })
    public debugMode: boolean = true;
    
    // 私有属性
    private _currentActiveNode: IBranchNodeData | null = null;
    private _isInitialized: boolean = false;

    protected onLoad(): void {
        console.log('🎬 LinearBranchMapScene: 线性分支地图场景加载');
        this.initializeScene();
        this.setupEventListeners();
        this.initializeKeyboardInput();
    }

    protected start(): void {
        if (this.autoCreateUI) {
            this.createUIStructure();
        }
        this.initializeComponents();
        this.startScene();
    }

    protected onDestroy(): void {
        this.cleanupEventListeners();
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
    }

    /**
     * 初始化场景
     */
    private initializeScene(): void {
        // 查找UI根节点
        if (!this.uiRoot) {
            this.uiRoot = find('Canvas') || this.node;
        }
        
        console.log('🎬 LinearBranchMapScene: 场景初始化完成');
    }

    /**
     * 创建UI结构
     */
    private createUIStructure(): void {
        if (!this.uiRoot || this._isInitialized) return;
        
        console.log('🎬 LinearBranchMapScene: 开始创建UI结构');
        
        // 创建主容器
        const mainContainer = this.createNode('MainContainer', this.uiRoot);
        
        // 创建地图区域容器
        const mapAreaContainer = this.createNode('MapAreaContainer', mainContainer);
        
        // 创建地图面板节点
        const mapPanelNode = this.createNode('LinearBranchMapPanel', mapAreaContainer);
        this.branchMapPanel = mapPanelNode.addComponent(LinearBranchMapPanel);
        
        // 创建行为面板节点
        const behaviorPanelNode = this.createNode('BehaviorPanel', mainContainer);
        this.behaviorPanel = behaviorPanelNode.addComponent(BehaviorPanel);
        
        // 创建控制器节点
        const controllerNode = this.createNode('InteractiveMapController', mainContainer);
        this.mapController = controllerNode.addComponent(InteractiveMapController);
        
        this._isInitialized = true;
        console.log('🎬 LinearBranchMapScene: UI结构创建完成');
    }

    /**
     * 创建节点辅助方法
     */
    private createNode(name: string, parent: Node): Node {
        const node = new Node(name);
        node.setParent(parent);
        return node;
    }

    /**
     * 初始化组件
     */
    private initializeComponents(): void {
        // 配置地图控制器
        if (this.mapController) {
            this.mapController.branchMapPanel = this.branchMapPanel;
            this.mapController.behaviorPanel = this.behaviorPanel;
            this.mapController.autoShowMap = true;
            this.mapController.autoHandleBehavior = true;
            this.mapController.defaultBehaviorDuration = 3.0;
        }
        
        // 配置分支地图面板
        if (this.branchMapPanel) {
            this.branchMapPanel.backgroundImagePath = 'ui/scene/BattleScene1';
            this.branchMapPanel.nodeSpacingX = 200;
            this.branchMapPanel.nodeSpacingY = 150;
            this.branchMapPanel.branchCount = 3;
            this.branchMapPanel.nodesPerBranch = 5;
        }
        
        // 配置行为面板
        if (this.behaviorPanel) {
            this.behaviorPanel.autoShow = false; // 由控制器管理显示
            this.behaviorPanel.defaultBehaviorDuration = 3.0;
        }
        
        console.log('🎬 LinearBranchMapScene: 组件初始化完成');
    }

    /**
     * 设置事件监听器
     */
    private setupEventListeners(): void {
        const eventManager = EventManager.getInstance();
        
        // 监听地图节点激活事件
        eventManager.on('map_node_activated', this.onMapNodeActivated, this);
        
        // 监听地图行为完成事件
        eventManager.on('map_behavior_completed', this.onMapBehaviorCompleted, this);
        
        // 监听节点解锁通知事件
        eventManager.on('map_node_unlocked_notification', this.onNodeUnlockedNotification, this);
        
        // 监听UI消息显示事件
        eventManager.on('show_ui_message', this.onShowUIMessage, this);
        
        // 监听分支节点点击事件
        eventManager.on('branch_node_clicked', this.onBranchNodeClicked, this);
        
        if (this.debugMode) {
            console.log('🎬 LinearBranchMapScene: 事件监听器设置完成');
        }
    }

    /**
     * 清理事件监听器
     */
    private cleanupEventListeners(): void {
        const eventManager = EventManager.getInstance();
        
        eventManager.off('map_node_activated', this.onMapNodeActivated, this);
        eventManager.off('map_behavior_completed', this.onMapBehaviorCompleted, this);
        eventManager.off('map_node_unlocked_notification', this.onNodeUnlockedNotification, this);
        eventManager.off('show_ui_message', this.onShowUIMessage, this);
        eventManager.off('branch_node_clicked', this.onBranchNodeClicked, this);
        
        if (this.debugMode) {
            console.log('🎬 LinearBranchMapScene: 事件监听器清理完成');
        }
    }

    /**
     * 初始化键盘输入
     */
    private initializeKeyboardInput(): void {
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log('⌨️ LinearBranchMapScene: 键盘输入已初始化');
    }

    /**
     * 启动场景
     */
    private startScene(): void {
        console.log('🎬 LinearBranchMapScene: 场景启动');
        
        // 显示欢迎消息
        this.showWelcomeMessage();
        
        // 显示操作说明
        this.showInstructions();
        
        // 启动地图控制器
        if (this.mapController) {
            this.mapController.showMap();
        }
        
        // 发送场景就绪事件
        EventManager.getInstance().emit('linear_branch_map_scene_ready', {
            sceneNode: this.node,
            mapController: this.mapController
        });
    }

    /**
     * 显示欢迎消息
     */
    private showWelcomeMessage(): void {
        EventManager.getInstance().emit('show_ui_message', {
            message: '欢迎来到线性分支交互地图！拖动地图找到你想要的行为按钮并点击交互。',
            type: 'info',
            duration: 5000
        });
    }

    /**
     * 显示操作说明
     */
    private showInstructions(): void {
        console.log('🗺️ ========== 线性分支地图系统演示 ==========');
        console.log('📍 当前场景: LinearBranchMap (线性分支地图)');
        console.log('🖱️ 拖拽地图来移动视角');
        console.log('🖱️ 点击节点进行交互');
        console.log('🔓 完成行为解锁后续节点');
        console.log('⌨️ 键盘快捷键:');
        console.log('   按 1 键 - 聚焦到起点');
        console.log('   按 2 键 - 聚焦到第一分支');
        console.log('   按 3 键 - 聚焦到第二分支');
        console.log('   按 R 键 - 重置地图位置');
        console.log('   按 + 键 - 放大地图');
        console.log('   按 - 键 - 缩小地图');
        console.log('   按 S 键 - 停止当前行为');
        console.log('   按 H 键 - 显示帮助信息');
        console.log('   按 ESC 键 - 返回主场景');
        console.log('🗺️ =====================================');
    }

    // ==================== 事件处理器 ====================

    /**
     * 地图节点激活事件处理
     */
    private onMapNodeActivated(eventData: any): void {
        const { nodeData, behaviorType } = eventData;
        
        if (this.debugMode) {
            console.log('🎬 LinearBranchMapScene: 地图节点激活', nodeData.name, behaviorType);
        }
        
        this._currentActiveNode = nodeData;
    }

    /**
     * 地图行为完成事件处理
     */
    private onMapBehaviorCompleted(eventData: any): void {
        const { behaviorData, nodeData } = eventData;
        
        if (this.debugMode) {
            console.log('🎬 LinearBranchMapScene: 地图行为完成', behaviorData?.name);
        }
        
        // 显示完成消息
        if (nodeData) {
            EventManager.getInstance().emit('show_ui_message', {
                message: `完成了"${nodeData.name}"的行为！`,
                type: 'success',
                duration: 2000
            });
        }
        
        this._currentActiveNode = null;
    }

    /**
     * 节点解锁通知事件处理
     */
    private onNodeUnlockedNotification(eventData: any): void {
        const { nodeId, nodeData } = eventData;
        
        if (this.debugMode) {
            console.log('🎬 LinearBranchMapScene: 节点解锁通知', nodeData.name);
        }
        
        // 显示解锁消息
        EventManager.getInstance().emit('show_ui_message', {
            message: `新节点"${nodeData.name}"已解锁！`,
            type: 'success',
            duration: 3000
        });
    }

    /**
     * UI消息显示事件处理
     */
    private onShowUIMessage(eventData: any): void {
        const { message, type, duration } = eventData;
        
        if (this.debugMode) {
            console.log(`🎬 LinearBranchMapScene: [${type}] ${message}`);
        }
        
        // 这里可以实现实际的UI消息显示逻辑
        // 目前只是在控制台输出
    }

    /**
     * 分支节点点击事件处理
     */
    private onBranchNodeClicked(eventData: any): void {
        const { nodeData, behaviorType } = eventData;
        
        if (this.debugMode) {
            console.log('🎬 LinearBranchMapScene: 分支节点点击', nodeData.name, behaviorType);
        }
    }

    // ==================== 键盘输入处理 ====================

    /**
     * 键盘按键处理
     */
    private onKeyDown(event: EventKeyboard): void {
        switch (event.keyCode) {
            case KeyCode.DIGIT_1:
                this.focusOnNode('start');
                break;
            case KeyCode.DIGIT_2:
                this.focusOnNode('branch_0_level_1');
                break;
            case KeyCode.DIGIT_3:
                this.focusOnNode('branch_1_level_1');
                break;
            case KeyCode.KEY_R:
                this.resetMapView();
                break;
            case KeyCode.EQUAL: // + 键
                this.zoomIn();
                break;
            case KeyCode.MINUS: // - 键
                this.zoomOut();
                break;
            case KeyCode.KEY_S:
                this.stopCurrentBehavior();
                break;
            case KeyCode.KEY_H:
                this.showInstructions();
                break;
            case KeyCode.ESCAPE:
                this.returnToMainScene();
                break;
        }
    }

    /**
     * 聚焦到指定节点
     */
    private focusOnNode(nodeId: string): void {
        if (this.mapController) {
            this.mapController.focusOnNode(nodeId);
            console.log('🎯 LinearBranchMapScene: 聚焦到节点', nodeId);
        }
    }

    /**
     * 重置地图视图
     */
    private resetMapView(): void {
        if (this.branchMapPanel) {
            this.branchMapPanel.focusOnNode('start');
            console.log('🗺️ LinearBranchMapScene: 地图视图已重置');
        }
    }

    /**
     * 放大地图
     */
    private zoomIn(): void {
        // 这里可以实现地图缩放逻辑
        console.log('🔍 LinearBranchMapScene: 放大地图');
    }

    /**
     * 缩小地图
     */
    private zoomOut(): void {
        // 这里可以实现地图缩放逻辑
        console.log('🔍 LinearBranchMapScene: 缩小地图');
    }

    /**
     * 停止当前行为
     */
    private stopCurrentBehavior(): void {
        if (this.mapController) {
            this.mapController.stopCurrentBehavior();
            console.log('⏹️ LinearBranchMapScene: 停止当前行为');
        }
    }

    /**
     * 返回主场景
     */
    private returnToMainScene(): void {
        console.log('🏠 LinearBranchMapScene: 返回主场景');
        // director.loadScene('Main');
    }

    // ==================== 公共API ====================

    /**
     * 重置场景
     */
    public resetScene(): void {
        if (this.mapController) {
            this.mapController.resetMapState();
        }
        
        console.log('🎬 LinearBranchMapScene: 场景已重置');
    }

    /**
     * 获取当前活动节点
     */
    public getCurrentActiveNode(): IBranchNodeData | null {
        return this._currentActiveNode;
    }

    /**
     * 获取地图控制器
     */
    public getMapController(): InteractiveMapController | null {
        return this.mapController;
    }

    /**
     * 设置调试模式
     */
    public setDebugMode(enabled: boolean): void {
        this.debugMode = enabled;
        console.log('🎬 LinearBranchMapScene: 调试模式', enabled ? '已启用' : '已禁用');
    }
}
