# 技能系统架构说明

## 文件结构

```
assets/scripts/
├── managers/
│   └── SkillManager.ts          # 技能管理器（核心逻辑）
├── systems/skill/
│   ├── SkillEffectSystem.ts     # 技能效果系统
│   ├── SkillCalculationService.ts # 技能计算服务
│   └── index.ts                 # 导出文件
├── ui/skill/
│   ├── SkillBarUI.ts           # 技能栏UI组件
│   ├── SkillSlot.ts            # 技能槽组件
│   ├── SkillSelectionPanel.ts  # 技能选择面板
│   ├── SkillListItem.ts        # 技能列表项组件
│   ├── SkillDetailView.ts      # 技能详情视图组件
│   └── index.ts                # 导出文件
└── config/interfaces/
    └── ISkillData.ts           # 技能数据接口定义
```

## 架构设计

### 1. 管理器层 (Managers)
- **SkillManager**: 技能系统的核心管理器，负责：
  - 技能学习和使用逻辑
  - 技能冷却时间管理
  - 技能槽位管理
  - 技能经验系统
  - 事件分发和处理

### 2. 系统层 (Systems)
- **SkillEffectSystem**: 处理技能效果的应用和计算
- **SkillCalculationService**: 提供技能相关的数值计算服务

### 3. UI层 (UI Components)
- **SkillBarUI**: 主游戏界面的技能栏
- **SkillSlot**: 单个技能槽的显示和交互
- **SkillSelectionPanel**: 技能选择面板
- **SkillListItem**: 技能列表中的单个技能项
- **SkillDetailView**: 技能详情显示组件

### 4. 数据层 (Interfaces)
- **ISkillData**: 技能配置数据接口
- **IPlayerSkill**: 玩家技能数据接口
- **ISkillResult**: 技能使用结果接口
- 各种事件数据接口

## 初始化顺序

1. EventManager
2. ResourceManager
3. ConfigManager
4. **SkillManager** ← 新增
5. UIManager
6. 其他管理器...

## 使用方式

### 获取技能管理器
```typescript
import { Managers } from '../managers';

// 方式1：通过Managers快捷访问器
const skillManager = Managers.Skill;

// 方式2：直接获取实例
const skillManager = SkillManager.getInstance();
```

### 在UI中使用
```typescript
import { SkillManager } from '../../managers/SkillManager';

const skillManager = SkillManager.getInstance();
await skillManager.learnSkill('fireball');
```

## 注意事项

1. **SkillManager已移动到managers文件夹**，遵循项目的管理器存放规范
2. 所有UI组件的导入路径已更新
3. 管理器初始化顺序已调整，SkillManager在ConfigManager之后初始化
4. 提供了Managers.Skill快捷访问器
