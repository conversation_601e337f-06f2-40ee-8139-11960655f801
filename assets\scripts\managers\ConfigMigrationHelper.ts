/**
 * Copyright (c) 2025 "放置". All rights reserved.
 * 配置迁移助手 - 确保新旧配置格式兼容
 */

import { _decorator, Component, resources } from 'cc';
import { EventManager } from './EventManager';

const { ccclass, property } = _decorator;

/**
 * 配置迁移助手
 * 负责处理新旧配置格式的兼容性，确保平滑过渡
 */
@ccclass('ConfigMigrationHelper')
export class ConfigMigrationHelper extends Component {

    @property({ tooltip: '是否启用调试日志' })
    public enableDebugLog: boolean = true;

    private static instance: ConfigMigrationHelper | null = null;

    /**
     * 获取单例实例
     */
    public static getInstance(): ConfigMigrationHelper | null {
        return ConfigMigrationHelper.instance;
    }

    protected onLoad(): void {
        ConfigMigrationHelper.instance = this;
        console.log('🔄 ConfigMigrationHelper: 配置迁移助手初始化');
    }

    protected onDestroy(): void {
        ConfigMigrationHelper.instance = null;
    }

    /**
     * 加载显示配置（新格式）
     */
    public async loadDisplayConfig(locationId: string): Promise<any | null> {
        try {
            if (this.enableDebugLog) {
                console.log(`🔄 ConfigMigrationHelper: 尝试加载新格式显示配置 ${locationId}`);
            }

            return new Promise((resolve) => {
                resources.load('configs/display/locations', (err, asset: any) => {
                    if (err) {
                        if (this.enableDebugLog) {
                            console.log('🔄 ConfigMigrationHelper: 新格式配置不存在，将使用旧格式');
                        }
                        resolve(null);
                        return;
                    }

                    try {
                        const config = asset.json || asset;
                        const locationConfig = config.locations?.[locationId];
                        
                        if (locationConfig) {
                            if (this.enableDebugLog) {
                                console.log(`✅ ConfigMigrationHelper: 成功加载新格式显示配置 ${locationId}`);
                            }
                            resolve(locationConfig);
                        } else {
                            if (this.enableDebugLog) {
                                console.log(`⚠️ ConfigMigrationHelper: 新格式配置中未找到 ${locationId}`);
                            }
                            resolve(null);
                        }
                    } catch (parseError) {
                        console.error('❌ ConfigMigrationHelper: 解析新格式配置失败', parseError);
                        resolve(null);
                    }
                });
            });

        } catch (error) {
            console.error('❌ ConfigMigrationHelper: 加载新格式配置失败', error);
            return null;
        }
    }

    /**
     * 加载旧格式配置（兼容性）
     */
    public async loadLegacyConfig(locationId: string): Promise<any | null> {
        try {
            if (this.enableDebugLog) {
                console.log(`🔄 ConfigMigrationHelper: 加载旧格式配置 ${locationId}`);
            }

            return new Promise((resolve) => {
                resources.load('configs/areas/game_areas', (err, asset: any) => {
                    if (err) {
                        console.error('❌ ConfigMigrationHelper: 加载旧格式配置失败', err);
                        resolve(null);
                        return;
                    }

                    try {
                        const config = asset.json || asset;
                        const locationConfig = config.locations?.[locationId];
                        
                        if (locationConfig) {
                            if (this.enableDebugLog) {
                                console.log(`✅ ConfigMigrationHelper: 成功加载旧格式配置 ${locationId}`);
                            }
                            resolve(locationConfig);
                        } else {
                            console.warn(`⚠️ ConfigMigrationHelper: 旧格式配置中未找到 ${locationId}`);
                            resolve(null);
                        }
                    } catch (parseError) {
                        console.error('❌ ConfigMigrationHelper: 解析旧格式配置失败', parseError);
                        resolve(null);
                    }
                });
            });

        } catch (error) {
            console.error('❌ ConfigMigrationHelper: 加载旧格式配置异常', error);
            return null;
        }
    }

    /**
     * 智能配置加载（优先新格式，降级到旧格式）
     */
    public async loadLocationConfig(locationId: string): Promise<any | null> {
        try {
            if (this.enableDebugLog) {
                console.log(`🔄 ConfigMigrationHelper: 智能加载配置 ${locationId}`);
            }

            // 1. 尝试加载新格式显示配置
            let displayConfig = await this.loadDisplayConfig(locationId);
            
            // 2. 如果新格式不存在，加载旧格式配置
            if (!displayConfig) {
                const legacyConfig = await this.loadLegacyConfig(locationId);
                if (legacyConfig) {
                    // 将旧格式转换为新格式
                    displayConfig = this.convertLegacyToDisplay(legacyConfig);
                    if (this.enableDebugLog) {
                        console.log(`🔄 ConfigMigrationHelper: 已将旧格式转换为显示格式 ${locationId}`);
                    }
                }
            }

            // 3. 尝试从服务器获取敏感配置（如果需要）
            const serverConfig = await this.loadServerConfig(locationId);

            // 4. 合并配置
            const mergedConfig = this.mergeConfigs(displayConfig, serverConfig);

            if (mergedConfig) {
                if (this.enableDebugLog) {
                    console.log(`✅ ConfigMigrationHelper: 配置加载完成 ${locationId}`);
                }
                
                // 发送配置加载完成事件
                EventManager.getInstance().emit('config:loaded', {
                    locationId,
                    hasDisplayConfig: !!displayConfig,
                    hasServerConfig: !!serverConfig,
                    timestamp: Date.now()
                });
            }

            return mergedConfig;

        } catch (error) {
            console.error(`❌ ConfigMigrationHelper: 智能配置加载失败 ${locationId}`, error);
            return null;
        }
    }

    /**
     * 将旧格式配置转换为新格式显示配置
     */
    private convertLegacyToDisplay(legacyConfig: any): any {
        try {
            const displayConfig = {
                locationId: legacyConfig.locationId,
                displayName: legacyConfig.name,
                description: legacyConfig.description,
                type: legacyConfig.type,
                displayLevel: legacyConfig.level,
                iconPath: `textures/locations/${legacyConfig.locationId}_icon`,
                backgroundPath: `textures/locations/${legacyConfig.locationId}_bg`,
                
                mapConfig: legacyConfig.mapConfig || {},
                environment: legacyConfig.environment || {},
                uiLayout: legacyConfig.uiLayout || {},
                
                behaviorDisplay: []
            };

            // 转换行为显示信息
            if (legacyConfig.behaviors && Array.isArray(legacyConfig.behaviors)) {
                displayConfig.behaviorDisplay = legacyConfig.behaviors.map((behavior: any) => ({
                    id: behavior.id,
                    displayName: behavior.name,
                    description: behavior.description,
                    iconPath: `textures/behaviors/${behavior.id}`,
                    displayDuration: `${behavior.duration}秒`,
                    displayCooldown: behavior.cooldown > 0 ? `${behavior.cooldown}秒` : '无',
                    displayEnergyCost: behavior.energyCost?.toString() || '0'
                }));
            }

            if (this.enableDebugLog) {
                console.log('🔄 ConfigMigrationHelper: 旧格式转换完成', {
                    locationId: displayConfig.locationId,
                    behaviorsCount: displayConfig.behaviorDisplay.length
                });
            }

            return displayConfig;

        } catch (error) {
            console.error('❌ ConfigMigrationHelper: 旧格式转换失败', error);
            return null;
        }
    }

    /**
     * 从服务器加载敏感配置（模拟）
     */
    private async loadServerConfig(locationId: string): Promise<any | null> {
        try {
            // 这里应该从服务器API获取敏感配置
            // 目前返回null，表示暂时使用本地配置
            if (this.enableDebugLog) {
                console.log(`🔄 ConfigMigrationHelper: 服务器配置加载 ${locationId} (暂未实现)`);
            }
            return null;

        } catch (error) {
            console.error(`❌ ConfigMigrationHelper: 服务器配置加载失败 ${locationId}`, error);
            return null;
        }
    }

    /**
     * 合并显示配置和服务器配置
     */
    private mergeConfigs(displayConfig: any, serverConfig: any): any {
        if (!displayConfig) {
            return null;
        }

        // 如果没有服务器配置，只返回显示配置
        if (!serverConfig) {
            return displayConfig;
        }

        // 合并配置（服务器配置优先）
        return {
            ...displayConfig,
            serverData: serverConfig
        };
    }

    /**
     * 检查配置格式版本
     */
    public async checkConfigVersion(): Promise<{ hasNewFormat: boolean; hasLegacyFormat: boolean }> {
        try {
            const hasNewFormat = await this.checkNewFormatExists();
            const hasLegacyFormat = await this.checkLegacyFormatExists();

            if (this.enableDebugLog) {
                console.log('🔄 ConfigMigrationHelper: 配置格式检查', {
                    hasNewFormat,
                    hasLegacyFormat
                });
            }

            return { hasNewFormat, hasLegacyFormat };

        } catch (error) {
            console.error('❌ ConfigMigrationHelper: 配置版本检查失败', error);
            return { hasNewFormat: false, hasLegacyFormat: false };
        }
    }

    /**
     * 检查新格式配置是否存在
     */
    private async checkNewFormatExists(): Promise<boolean> {
        return new Promise((resolve) => {
            resources.load('configs/display/locations', (err) => {
                resolve(!err);
            });
        });
    }

    /**
     * 检查旧格式配置是否存在
     */
    private async checkLegacyFormatExists(): Promise<boolean> {
        return new Promise((resolve) => {
            resources.load('configs/areas/game_areas', (err) => {
                resolve(!err);
            });
        });
    }

    /**
     * 获取配置迁移状态
     */
    public async getMigrationStatus(): Promise<{
        status: 'new_only' | 'legacy_only' | 'both' | 'none';
        recommendation: string;
    }> {
        const { hasNewFormat, hasLegacyFormat } = await this.checkConfigVersion();

        if (hasNewFormat && hasLegacyFormat) {
            return {
                status: 'both',
                recommendation: '建议逐步迁移到新格式，完成后删除旧格式配置'
            };
        } else if (hasNewFormat) {
            return {
                status: 'new_only',
                recommendation: '已使用新格式配置，系统运行正常'
            };
        } else if (hasLegacyFormat) {
            return {
                status: 'legacy_only',
                recommendation: '建议迁移到新格式配置以提高安全性'
            };
        } else {
            return {
                status: 'none',
                recommendation: '未找到配置文件，请检查配置文件是否存在'
            };
        }
    }
}
