# Copyright (c) 2025 "放置". All rights reserved.

# 场景切换协议 (Scene Switch Protocol - SSP) v1.0

## 🎯 协议概述

场景切换协议 (SSP) 是一套专门为挂机放置类游戏设计的场景数据通信协议，规范了前端与后端之间场景切换的完整流程。

## 📋 协议特性

- **版本控制**: 支持协议版本管理和向后兼容
- **统一格式**: 标准化的请求/响应格式
- **错误处理**: 完整的错误码体系和重试机制
- **状态同步**: 实时的场景状态同步
- **扩展性**: 支持未来功能扩展

## 🔧 协议结构

### 基础消息格式

```typescript
interface SSPMessage {
  protocol: "SSP";           // 协议标识
  version: string;           // 协议版本 (如 "1.0")
  messageId: string;         // 消息唯一ID
  timestamp: number;         // 时间戳
  type: SSPMessageType;      // 消息类型
  payload: any;              // 消息载荷
  metadata?: SSPMetadata;    // 元数据
}

interface SSPMetadata {
  userId?: string;           // 用户ID
  sessionId?: string;        // 会话ID
  clientVersion?: string;    // 客户端版本
  platform?: string;         // 平台信息
  retry?: number;            // 重试次数
}
```

### 消息类型枚举

```typescript
enum SSPMessageType {
  // 请求类型
  SCENE_DATA_REQUEST = "scene_data_request",
  SCENE_SWITCH_REQUEST = "scene_switch_request",
  SCENE_CONFIG_REQUEST = "scene_config_request",
  
  // 响应类型
  SCENE_DATA_RESPONSE = "scene_data_response",
  SCENE_SWITCH_RESPONSE = "scene_switch_response",
  SCENE_CONFIG_RESPONSE = "scene_config_response",
  
  // 事件类型
  SCENE_SWITCH_EVENT = "scene_switch_event",
  SCENE_UPDATE_EVENT = "scene_update_event",
  
  // 错误类型
  ERROR_RESPONSE = "error_response"
}
```

## 🚀 核心协议流程

### 1. 场景数据请求协议

**请求格式:**
```typescript
interface SceneDataRequest {
  sceneId: string;           // 场景ID
  version?: string;          // 请求的配置版本
  includeServerData: boolean; // 是否包含服务器数据
  includeXMLConfig: boolean;  // 是否包含XML配置
  cachePolicy?: CachePolicy;  // 缓存策略
}

enum CachePolicy {
  USE_CACHE = "use_cache",           // 优先使用缓存
  FORCE_REFRESH = "force_refresh",   // 强制刷新
  CACHE_FIRST = "cache_first"        // 缓存优先，失败时请求服务器
}
```

**响应格式:**
```typescript
interface SceneDataResponse {
  sceneId: string;
  version: string;
  data: {
    xmlConfig: XMLSceneConfig;      // XML配置数据
    serverData: ServerSceneData;    // 服务器数据
    combinedConfig: CombinedSceneConfig; // 合并后的配置
  };
  metadata: {
    dataSource: "cache" | "server" | "hybrid";
    lastUpdated: number;
    cacheExpiry?: number;
    hasXMLConfig: boolean;
    hasServerData: boolean;
  };
}
```

### 2. 场景切换请求协议

**请求格式:**
```typescript
interface SceneSwitchRequest {
  fromSceneId?: string;      // 当前场景ID
  toSceneId: string;         // 目标场景ID
  switchType: SwitchType;    // 切换类型
  playerData?: PlayerData;   // 玩家数据
  transitionConfig?: TransitionConfig; // 过渡配置
}

enum SwitchType {
  NORMAL = "normal",         // 普通切换
  FAST = "fast",            // 快速切换
  PRELOAD = "preload"       // 预加载切换
}

interface TransitionConfig {
  type: "fade" | "slide" | "none";
  duration: number;
  easing?: string;
}
```

**响应格式:**
```typescript
interface SceneSwitchResponse {
  success: boolean;
  sceneId: string;
  switchId: string;          // 切换操作ID
  sceneData: SceneDataResponse;
  playerState?: PlayerState; // 更新后的玩家状态
  unlockStatus?: UnlockStatus; // 解锁状态
  timing: {
    requestTime: number;
    processTime: number;
    totalTime: number;
  };
}
```

### 3. 错误处理协议

**错误响应格式:**
```typescript
interface SSPErrorResponse {
  errorCode: SSPErrorCode;
  errorMessage: string;
  errorDetails?: any;
  retryable: boolean;
  retryAfter?: number;       // 重试延迟（毫秒）
  suggestedAction?: string;  // 建议的处理方式
}

enum SSPErrorCode {
  // 客户端错误 (4xx)
  INVALID_REQUEST = "SSP_4001",
  SCENE_NOT_FOUND = "SSP_4004",
  SCENE_LOCKED = "SSP_4003",
  INVALID_SCENE_ID = "SSP_4002",
  
  // 服务器错误 (5xx)
  SERVER_ERROR = "SSP_5001",
  CONFIG_LOAD_FAILED = "SSP_5002",
  DATABASE_ERROR = "SSP_5003",
  CACHE_ERROR = "SSP_5004",
  
  // 业务错误 (6xx)
  PLAYER_LEVEL_TOO_LOW = "SSP_6001",
  MISSING_REQUIRED_ITEMS = "SSP_6002",
  QUEST_NOT_COMPLETED = "SSP_6003"
}
```

## 🔄 协议状态机

```
[客户端] --场景数据请求--> [服务器]
[客户端] <--场景数据响应-- [服务器]
[客户端] --场景切换请求--> [服务器]
[客户端] <--场景切换响应-- [服务器]
[客户端] <--场景状态事件-- [服务器] (可选)
```

## 📊 协议实现示例

### 客户端请求示例
```typescript
const sceneRequest: SSPMessage = {
  protocol: "SSP",
  version: "1.0",
  messageId: "req_" + Date.now(),
  timestamp: Date.now(),
  type: SSPMessageType.SCENE_DATA_REQUEST,
  payload: {
    sceneId: "fishpond",
    version: "1.0.0",
    includeServerData: true,
    includeXMLConfig: true,
    cachePolicy: CachePolicy.CACHE_FIRST
  },
  metadata: {
    userId: "player_123",
    sessionId: "session_456",
    clientVersion: "1.0.0",
    platform: "wechat"
  }
};
```

### 服务器响应示例
```typescript
const sceneResponse: SSPMessage = {
  protocol: "SSP",
  version: "1.0",
  messageId: "resp_" + Date.now(),
  timestamp: Date.now(),
  type: SSPMessageType.SCENE_DATA_RESPONSE,
  payload: {
    sceneId: "fishpond",
    version: "1.0.0",
    data: {
      xmlConfig: { /* XML配置数据 */ },
      serverData: { /* 服务器数据 */ },
      combinedConfig: { /* 合并配置 */ }
    },
    metadata: {
      dataSource: "hybrid",
      lastUpdated: Date.now(),
      hasXMLConfig: true,
      hasServerData: true
    }
  }
};
```

## 🛡️ 安全性考虑

### 1. 请求验证
- 消息ID唯一性验证
- 时间戳有效性检查
- 用户权限验证
- 场景访问权限检查

### 2. 数据完整性
- 消息签名验证
- 数据校验和检查
- 版本兼容性验证

### 3. 防重放攻击
- 消息ID去重
- 时间窗口限制
- 会话状态验证

## 🔧 协议扩展机制

### 版本兼容性
```typescript
interface SSPVersionInfo {
  current: string;           // 当前版本
  supported: string[];       // 支持的版本列表
  deprecated: string[];      // 已弃用的版本
  breaking: string[];        // 破坏性变更版本
}
```

### 自定义扩展
```typescript
interface SSPExtension {
  name: string;              // 扩展名称
  version: string;           // 扩展版本
  data: any;                 // 扩展数据
}
```

## 📈 性能优化

### 1. 缓存策略
- 客户端缓存：场景配置本地缓存
- 服务器缓存：热点数据内存缓存
- CDN缓存：静态资源分发

### 2. 数据压缩
- 请求/响应数据压缩
- 增量更新机制
- 差异化传输

### 3. 连接优化
- 连接池管理
- 请求合并
- 异步处理

## 🧪 协议测试

### 测试用例覆盖
- 正常场景切换流程
- 错误处理和重试机制
- 并发请求处理
- 网络异常恢复
- 版本兼容性测试

### 性能基准
- 请求响应时间 < 500ms
- 并发处理能力 > 1000 QPS
- 错误率 < 0.1%
- 缓存命中率 > 90%

这个协议为场景切换提供了完整的标准化解决方案，确保了系统的可靠性、可扩展性和可维护性。
