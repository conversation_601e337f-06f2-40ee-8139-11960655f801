# Copyright (c) 2025 "放置". All rights reserved.

# 协议目录架构设计

## 🎯 设计原则

1. **共享优先**: 前后端共享协议放在顶层，避免重复定义
2. **功能聚合**: 按功能模块组织，便于维护和扩展
3. **层次分明**: 不同层级协议分开存放，职责清晰
4. **命名规范**: 统一的命名约定，提高可读性
5. **依赖管理**: 清晰的依赖关系，避免循环依赖

## 📁 协议目录结构

```
protocols/
├── 📁 shared/                    # 前后端共享协议 (最重要)
│   ├── 📁 core/                  # 核心基础协议
│   │   ├── base.types.ts         # 基础类型定义
│   │   ├── error.types.ts        # 错误类型定义
│   │   ├── common.messages.ts    # 通用消息协议
│   │   └── index.ts              # 统一导出
│   │
│   ├── 📁 scene/                 # 场景相关协议
│   │   ├── scene.types.ts        # 场景类型定义
│   │   ├── scene.messages.ts     # 场景消息协议
│   │   ├── scene-switch.types.ts # 场景切换类型
│   │   └── index.ts
│   │
│   ├── 📁 player/                # 玩家相关协议
│   │   ├── player.types.ts       # 玩家类型定义
│   │   ├── player.messages.ts    # 玩家消息协议
│   │   ├── player-state.types.ts # 玩家状态类型
│   │   └── index.ts
│   │
│   ├── 📁 item/                  # 物品相关协议
│   │   ├── item.types.ts         # 物品类型定义
│   │   ├── item.messages.ts      # 物品消息协议
│   │   ├── inventory.types.ts    # 背包类型定义
│   │   └── index.ts
│   │
│   ├── 📁 battle/                # 战斗相关协议
│   │   ├── battle.types.ts       # 战斗类型定义
│   │   ├── battle.messages.ts    # 战斗消息协议
│   │   ├── skill.types.ts        # 技能类型定义
│   │   └── index.ts
│   │
│   ├── 📁 social/                # 社交相关协议
│   │   ├── friend.types.ts       # 好友类型定义
│   │   ├── guild.types.ts        # 公会类型定义
│   │   ├── chat.messages.ts      # 聊天消息协议
│   │   └── index.ts
│   │
│   ├── 📁 economy/               # 经济相关协议
│   │   ├── currency.types.ts     # 货币类型定义
│   │   ├── shop.types.ts         # 商店类型定义
│   │   ├── trade.messages.ts     # 交易消息协议
│   │   └── index.ts
│   │
│   └── index.ts                  # 共享协议总入口
│
├── 📁 frontend/                  # 前端专用协议
│   ├── 📁 ui/                    # UI层协议
│   │   ├── 📁 components/        # UI组件协议
│   │   │   ├── button.types.ts   # 按钮组件协议
│   │   │   ├── panel.types.ts    # 面板组件协议
│   │   │   ├── dialog.types.ts   # 对话框组件协议
│   │   │   └── index.ts
│   │   │
│   │   ├── 📁 events/            # UI事件协议
│   │   │   ├── click.events.ts   # 点击事件协议
│   │   │   ├── input.events.ts   # 输入事件协议
│   │   │   ├── gesture.events.ts # 手势事件协议
│   │   │   └── index.ts
│   │   │
│   │   ├── 📁 adapters/          # UI适配器协议
│   │   │   ├── scene-ui.adapter.ts    # 场景UI适配器
│   │   │   ├── player-ui.adapter.ts   # 玩家UI适配器
│   │   │   ├── item-ui.adapter.ts     # 物品UI适配器
│   │   │   └── index.ts
│   │   │
│   │   └── index.ts
│   │
│   ├── 📁 business/              # 业务层协议
│   │   ├── 📁 managers/          # 管理器协议
│   │   │   ├── scene-manager.types.ts     # 场景管理器协议
│   │   │   ├── player-manager.types.ts    # 玩家管理器协议
│   │   │   ├── config-manager.types.ts    # 配置管理器协议
│   │   │   └── index.ts
│   │   │
│   │   ├── 📁 services/          # 服务层协议
│   │   │   ├── game-service.types.ts      # 游戏服务协议
│   │   │   ├── network-service.types.ts   # 网络服务协议
│   │   │   ├── storage-service.types.ts   # 存储服务协议
│   │   │   └── index.ts
│   │   │
│   │   └── index.ts
│   │
│   ├── 📁 data/                  # 数据层协议
│   │   ├── 📁 cache/             # 缓存协议
│   │   │   ├── cache.types.ts    # 缓存类型定义
│   │   │   ├── cache.messages.ts # 缓存消息协议
│   │   │   └── index.ts
│   │   │
│   │   ├── 📁 storage/           # 存储协议
│   │   │   ├── local-storage.types.ts     # 本地存储协议
│   │   │   ├── session-storage.types.ts   # 会话存储协议
│   │   │   └── index.ts
│   │   │
│   │   ├── 📁 config/            # 配置协议
│   │   │   ├── config-loader.types.ts     # 配置加载器协议
│   │   │   ├── config-validator.types.ts  # 配置验证器协议
│   │   │   └── index.ts
│   │   │
│   │   └── index.ts
│   │
│   ├── 📁 network/               # 网络层协议
│   │   ├── 📁 http/              # HTTP协议
│   │   │   ├── http-client.types.ts       # HTTP客户端协议
│   │   │   ├── http-request.types.ts      # HTTP请求协议
│   │   │   ├── http-response.types.ts     # HTTP响应协议
│   │   │   └── index.ts
│   │   │
│   │   ├── 📁 websocket/         # WebSocket协议
│   │   │   ├── ws-client.types.ts         # WebSocket客户端协议
│   │   │   ├── ws-message.types.ts        # WebSocket消息协议
│   │   │   └── index.ts
│   │   │
│   │   └── index.ts
│   │
│   └── index.ts                  # 前端协议总入口
│
├── 📁 backend/                   # 后端专用协议
│   ├── 📁 api/                   # API协议
│   │   ├── 📁 routes/            # 路由协议
│   │   │   ├── scene-routes.types.ts      # 场景路由协议
│   │   │   ├── player-routes.types.ts     # 玩家路由协议
│   │   │   ├── item-routes.types.ts       # 物品路由协议
│   │   │   └── index.ts
│   │   │
│   │   ├── 📁 middleware/        # 中间件协议
│   │   │   ├── auth.types.ts     # 认证中间件协议
│   │   │   ├── validation.types.ts        # 验证中间件协议
│   │   │   ├── logging.types.ts  # 日志中间件协议
│   │   │   └── index.ts
│   │   │
│   │   └── index.ts
│   │
│   ├── 📁 database/              # 数据库协议
│   │   ├── 📁 models/            # 数据模型协议
│   │   │   ├── player-model.types.ts      # 玩家模型协议
│   │   │   ├── scene-model.types.ts       # 场景模型协议
│   │   │   ├── item-model.types.ts        # 物品模型协议
│   │   │   └── index.ts
│   │   │
│   │   ├── 📁 queries/           # 查询协议
│   │   │   ├── player-queries.types.ts    # 玩家查询协议
│   │   │   ├── scene-queries.types.ts     # 场景查询协议
│   │   │   └── index.ts
│   │   │
│   │   └── index.ts
│   │
│   ├── 📁 services/              # 服务协议
│   │   ├── 📁 game-logic/        # 游戏逻辑服务
│   │   │   ├── scene-service.types.ts     # 场景服务协议
│   │   │   ├── battle-service.types.ts    # 战斗服务协议
│   │   │   └── index.ts
│   │   │
│   │   ├── 📁 external/          # 外部服务协议
│   │   │   ├── payment.types.ts  # 支付服务协议
│   │   │   ├── analytics.types.ts         # 分析服务协议
│   │   │   └── index.ts
│   │   │
│   │   └── index.ts
│   │
│   └── index.ts                  # 后端协议总入口
│
├── 📁 tools/                     # 协议工具
│   ├── generator/                # 协议生成器
│   ├── validator/                # 协议验证器
│   ├── converter/                # 协议转换器
│   └── index.ts
│
├── 📁 docs/                      # 协议文档
│   ├── api-reference.md          # API参考文档
│   ├── protocol-guide.md         # 协议使用指南
│   └── migration-guide.md        # 迁移指南
│
└── index.ts                      # 协议总入口
```

## 📝 文件命名规范

### 文件类型后缀
- `*.types.ts` - 类型定义文件
- `*.messages.ts` - 消息协议文件
- `*.events.ts` - 事件协议文件
- `*.handler.ts` - 处理器文件
- `*.adapter.ts` - 适配器文件
- `*.service.ts` - 服务协议文件
- `*.utils.ts` - 工具类文件
- `index.ts` - 统一导出文件

### 命名约定
- **文件名**: 小写字母 + 连字符 (kebab-case)
- **类型名**: 大驼峰命名 (PascalCase)
- **接口名**: 大驼峰命名 + Interface后缀
- **枚举名**: 大驼峰命名 + 描述性后缀
- **常量名**: 全大写 + 下划线分隔

## 🔗 导入导出规范

### 统一导出 (index.ts)
```typescript
// protocols/shared/scene/index.ts
export * from './scene.types';
export * from './scene.messages';
export * from './scene-switch.types';
```

### 绝对路径导入
```typescript
// 推荐使用绝对路径
import { SceneData } from '@protocols/shared/scene';
import { UIEventProtocol } from '@protocols/frontend/ui';

// 避免相对路径
import { SceneData } from '../../../shared/scene';
```

### 避免循环依赖
```typescript
// ❌ 错误：循环依赖
// player.types.ts 导入 scene.types.ts
// scene.types.ts 导入 player.types.ts

// ✅ 正确：提取共同依赖到 core
// core/common.types.ts 定义共同类型
// player.types.ts 和 scene.types.ts 都导入 core
```

## 🎯 协议分层原则

### 依赖方向
```
frontend/ ──→ shared/ ←── backend/
    ↓           ↓           ↓
  具体实现   通用协议    具体实现
```

### 层级关系
```
UI层协议 ──→ 业务层协议 ──→ 数据层协议 ──→ 网络层协议
   ↓            ↓            ↓            ↓
 组件交互    业务逻辑    数据操作    网络通信
```

## 🔧 协议扩展策略

### 新功能添加
1. 在 `shared/` 中定义通用类型
2. 在 `frontend/` 中实现前端特定协议
3. 在 `backend/` 中实现后端特定协议
4. 更新相应的 `index.ts` 文件

### 版本管理
```typescript
// 协议版本控制
export const PROTOCOL_VERSION = {
  SCENE: "1.0.0",
  PLAYER: "1.1.0",
  ITEM: "1.0.0"
} as const;
```

### 向后兼容
```typescript
// 协议迁移支持
export interface SceneDataV1 { /* 旧版本 */ }
export interface SceneDataV2 extends SceneDataV1 { /* 新版本 */ }
export type SceneData = SceneDataV2; // 当前版本
```

这个协议目录架构确保了：
- ✅ **清晰的职责分离**
- ✅ **便于维护和扩展**
- ✅ **避免代码重复**
- ✅ **支持团队协作**
- ✅ **便于版本管理**
