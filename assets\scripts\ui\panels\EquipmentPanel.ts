/**
 * 装备界面面板
 * 管理角色装备系统的UI交互
 */

import { _decorator, Component, Node, Sprite, Color, Label, Layout } from 'cc';
import { BaseUIPanel } from '../base/BaseUIPanel';
import { UIPanelType } from '../types/UITypes';

const { ccclass, property } = _decorator;

// 装备槽位类型
export enum EquipmentSlotType {
    Helmet = 'helmet',      // 头盔
    Weapon = 'weapon',      // 武器
    Armor = 'armor',        // 护甲
    Boots = 'boots',        // 靴子
    Ring = 'ring',          // 戒指
    Amulet = 'amulet'       // 护身符
}

// 装备数据接口
export interface IEquipmentItem {
    id: string;
    name: string;
    type: EquipmentSlotType;
    rarity: number;      // 稀有度 1-5
    level: number;       // 装备等级
    attack?: number;     // 攻击力
    defense?: number;    // 防御力
    speed?: number;      // 速度
    icon?: string;       // 图标路径
}

@ccclass('EquipmentPanel')
export class EquipmentPanel extends BaseUIPanel {
    
    @property({ type: Node, tooltip: '装备槽位容器' })
    equipmentSlotsContainer: Node = null!;
    
    @property({ type: Node, tooltip: '装备背包容器' })
    equipmentInventoryContainer: Node = null!;
    
    @property({ type: Node, tooltip: '装备属性显示面板' })
    equipmentStatsPanel: Node = null!;
    
    @property({ type: Label, tooltip: '装备名称标签' })
    equipmentNameLabel: Label = null!;
    
    @property({ type: Label, tooltip: '装备属性标签' })
    equipmentStatsLabel: Label = null!;
    
    // 装备槽位节点映射
    private equipmentSlots: Map<EquipmentSlotType, Node> = new Map();
    
    // 当前装备的物品
    private equippedItems: Map<EquipmentSlotType, IEquipmentItem> = new Map();
    
    // 背包中的装备物品
    private inventoryItems: IEquipmentItem[] = [];
    
    // 当前选中的装备
    private selectedItem: IEquipmentItem | null = null;

    protected onPanelLoad(): void {
        console.log('🛡️ 装备面板加载');
        this._panelType = UIPanelType.FullScreen;
    }

    protected onPanelEnable(): void {
        console.log('🛡️ 装备面板启用');
    }

    protected onPanelDisable(): void {
        console.log('🛡️ 装备面板禁用');
    }

    protected onPanelDestroy(): void {
        console.log('🛡️ 装备面板销毁');
    }

    protected async initializeUI(): Promise<void> {
        console.log('🛡️ 初始化装备界面UI');
        
        // 创建装备槽位
        await this.createEquipmentSlots();
        
        // 创建装备背包
        await this.createEquipmentInventory();
        
        // 初始化一些测试装备
        this.initializeTestEquipment();
    }

    protected bindEvents(): void {
        console.log('🛡️ 绑定装备面板事件');
        // 这里可以绑定装备相关的事件
    }

    protected unbindEvents(): void {
        console.log('🛡️ 解绑装备面板事件');
        // 这里解绑事件
    }

    protected async onInitialize(data?: any): Promise<void> {
        console.log('🛡️ 装备面板初始化完成');
    }

    protected async onBeforeShow(data?: any): Promise<void> {
        console.log('🛡️ 装备面板显示前处理');
        this.refreshEquipmentDisplay();
    }

    protected async onAfterShow(data?: any): Promise<void> {
        console.log('🛡️ 装备面板显示后处理');
    }

    protected async onBeforeHide(): Promise<void> {
        console.log('🛡️ 装备面板隐藏前处理');
    }

    protected async onAfterHide(): Promise<void> {
        console.log('🛡️ 装备面板隐藏后处理');
    }

    protected onRefresh(data?: any): void {
        console.log('🛡️ 刷新装备面板');
        this.refreshEquipmentDisplay();
    }

    /**
     * 创建装备槽位
     */
    private async createEquipmentSlots(): Promise<void> {
        if (!this.equipmentSlotsContainer) return;

        // 装备槽位配置
        const slotConfigs = [
            { type: EquipmentSlotType.Helmet, name: '头盔', x: 0, y: 200 },
            { type: EquipmentSlotType.Weapon, name: '武器', x: -150, y: 0 },
            { type: EquipmentSlotType.Armor, name: '护甲', x: 0, y: 0 },
            { type: EquipmentSlotType.Boots, name: '靴子', x: 0, y: -200 },
            { type: EquipmentSlotType.Ring, name: '戒指', x: 150, y: 100 },
            { type: EquipmentSlotType.Amulet, name: '护身符', x: 150, y: -100 }
        ];

        for (const config of slotConfigs) {
            const slotNode = this.createEquipmentSlot(config.type, config.name);
            slotNode.setPosition(config.x, config.y, 0);
            this.equipmentSlotsContainer.addChild(slotNode);
            this.equipmentSlots.set(config.type, slotNode);
        }
    }

    /**
     * 创建单个装备槽位
     */
    private createEquipmentSlot(slotType: EquipmentSlotType, slotName: string): Node {
        const slotNode = new Node(`EquipmentSlot_${slotType}`);
        
        // 添加背景精灵
        const sprite = slotNode.addComponent(Sprite);
        sprite.color = new Color(80, 80, 80, 255); // 深灰色背景
        
        // 设置槽位大小
        slotNode.getComponent('cc.UITransform')?.setContentSize(80, 80);
        
        // 添加槽位名称标签
        const labelNode = new Node('SlotLabel');
        const label = labelNode.addComponent(Label);
        label.string = slotName;
        label.fontSize = 12;
        label.color = new Color(255, 255, 255, 255);
        labelNode.setPosition(0, -50, 0);
        slotNode.addChild(labelNode);
        
        // 添加点击事件
        this.addSlotClickHandler(slotNode, slotType);
        
        return slotNode;
    }

    /**
     * 添加槽位点击处理
     */
    private addSlotClickHandler(slotNode: Node, slotType: EquipmentSlotType): void {
        slotNode.on(Node.EventType.TOUCH_END, () => {
            console.log(`🎯 点击装备槽位: ${slotType}`);
            this.onEquipmentSlotClicked(slotType);
        });
    }

    /**
     * 创建装备背包
     */
    private async createEquipmentInventory(): Promise<void> {
        if (!this.equipmentInventoryContainer) return;

        // 设置背包布局
        const layout = this.equipmentInventoryContainer.getComponent(Layout);
        if (layout) {
            layout.type = Layout.Type.GRID;
            layout.resizeMode = Layout.ResizeMode.CONTAINER;
            layout.cellSize.set(60, 60);
            layout.spacingX = 10;
            layout.spacingY = 10;
        }
    }

    /**
     * 初始化测试装备
     */
    private initializeTestEquipment(): void {
        // 添加一些测试装备到背包
        this.inventoryItems = [
            {
                id: 'helmet_001',
                name: '铁制头盔',
                type: EquipmentSlotType.Helmet,
                rarity: 1,
                level: 1,
                defense: 10
            },
            {
                id: 'weapon_001',
                name: '铁剑',
                type: EquipmentSlotType.Weapon,
                rarity: 1,
                level: 1,
                attack: 15
            },
            {
                id: 'armor_001',
                name: '皮甲',
                type: EquipmentSlotType.Armor,
                rarity: 1,
                level: 1,
                defense: 20
            },
            {
                id: 'boots_001',
                name: '布靴',
                type: EquipmentSlotType.Boots,
                rarity: 1,
                level: 1,
                speed: 5
            }
        ];
    }

    /**
     * 刷新装备显示
     */
    private refreshEquipmentDisplay(): void {
        this.refreshEquipmentSlots();
        this.refreshInventoryDisplay();
    }

    /**
     * 刷新装备槽位显示
     */
    private refreshEquipmentSlots(): void {
        for (const [slotType, slotNode] of this.equipmentSlots) {
            const equippedItem = this.equippedItems.get(slotType);
            const sprite = slotNode.getComponent(Sprite);
            
            if (equippedItem) {
                // 显示装备图标（这里用颜色代替）
                sprite.color = this.getItemColor(equippedItem.rarity);
            } else {
                // 显示空槽位
                sprite.color = new Color(80, 80, 80, 255);
            }
        }
    }

    /**
     * 刷新背包显示
     */
    private refreshInventoryDisplay(): void {
        if (!this.equipmentInventoryContainer) return;

        // 清除现有的背包物品显示
        this.equipmentInventoryContainer.removeAllChildren();

        // 创建背包物品显示
        for (const item of this.inventoryItems) {
            const itemNode = this.createInventoryItemNode(item);
            this.equipmentInventoryContainer.addChild(itemNode);
        }
    }

    /**
     * 创建背包物品节点
     */
    private createInventoryItemNode(item: IEquipmentItem): Node {
        const itemNode = new Node(`InventoryItem_${item.id}`);
        
        // 添加背景精灵
        const sprite = itemNode.addComponent(Sprite);
        sprite.color = this.getItemColor(item.rarity);
        
        // 设置物品大小
        itemNode.getComponent('cc.UITransform')?.setContentSize(50, 50);
        
        // 添加点击事件
        itemNode.on(Node.EventType.TOUCH_END, () => {
            console.log(`🎯 点击装备物品: ${item.name}`);
            this.onInventoryItemClicked(item);
        });
        
        return itemNode;
    }

    /**
     * 根据稀有度获取物品颜色
     */
    private getItemColor(rarity: number): Color {
        switch (rarity) {
            case 1: return new Color(128, 128, 128, 255); // 灰色 - 普通
            case 2: return new Color(255, 255, 255, 255); // 白色 - 优秀
            case 3: return new Color(0, 255, 0, 255);     // 绿色 - 稀有
            case 4: return new Color(0, 0, 255, 255);     // 蓝色 - 史诗
            case 5: return new Color(255, 0, 255, 255);   // 紫色 - 传说
            default: return new Color(128, 128, 128, 255);
        }
    }

    /**
     * 装备槽位点击处理
     */
    private onEquipmentSlotClicked(slotType: EquipmentSlotType): void {
        const equippedItem = this.equippedItems.get(slotType);
        
        if (equippedItem) {
            // 卸下装备
            this.unequipItem(slotType);
        } else if (this.selectedItem && this.selectedItem.type === slotType) {
            // 装备选中的物品
            this.equipItem(this.selectedItem);
        }
    }

    /**
     * 背包物品点击处理
     */
    private onInventoryItemClicked(item: IEquipmentItem): void {
        this.selectedItem = item;
        this.displayItemStats(item);
        console.log(`📋 选中装备: ${item.name}`);
    }

    /**
     * 装备物品
     */
    private equipItem(item: IEquipmentItem): void {
        // 如果该槽位已有装备，先卸下
        if (this.equippedItems.has(item.type)) {
            this.unequipItem(item.type);
        }
        
        // 装备新物品
        this.equippedItems.set(item.type, item);
        
        // 从背包中移除
        const index = this.inventoryItems.findIndex(i => i.id === item.id);
        if (index !== -1) {
            this.inventoryItems.splice(index, 1);
        }
        
        console.log(`⚔️ 装备: ${item.name}`);
        this.refreshEquipmentDisplay();
    }

    /**
     * 卸下装备
     */
    private unequipItem(slotType: EquipmentSlotType): void {
        const item = this.equippedItems.get(slotType);
        if (!item) return;
        
        // 从装备槽位移除
        this.equippedItems.delete(slotType);
        
        // 添加回背包
        this.inventoryItems.push(item);
        
        console.log(`🔓 卸下装备: ${item.name}`);
        this.refreshEquipmentDisplay();
    }

    /**
     * 显示装备属性
     */
    private displayItemStats(item: IEquipmentItem): void {
        if (this.equipmentNameLabel) {
            this.equipmentNameLabel.string = item.name;
        }
        
        if (this.equipmentStatsLabel) {
            let statsText = `等级: ${item.level}\n稀有度: ${item.rarity}`;
            
            if (item.attack) {
                statsText += `\n攻击力: +${item.attack}`;
            }
            if (item.defense) {
                statsText += `\n防御力: +${item.defense}`;
            }
            if (item.speed) {
                statsText += `\n速度: +${item.speed}`;
            }
            
            this.equipmentStatsLabel.string = statsText;
        }
        
        // 显示属性面板
        if (this.equipmentStatsPanel) {
            this.equipmentStatsPanel.active = true;
        }
    }

    /**
     * 获取当前装备的总属性
     */
    public getTotalStats(): { attack: number; defense: number; speed: number } {
        let totalAttack = 0;
        let totalDefense = 0;
        let totalSpeed = 0;
        
        for (const item of this.equippedItems.values()) {
            totalAttack += item.attack || 0;
            totalDefense += item.defense || 0;
            totalSpeed += item.speed || 0;
        }
        
        return {
            attack: totalAttack,
            defense: totalDefense,
            speed: totalSpeed
        };
    }
}