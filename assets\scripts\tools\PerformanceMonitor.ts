// Copyright (c) 2025 "放置". All rights reserved.
import { _decorator, Component, sys } from 'cc';

const { ccclass, property } = _decorator;

/**
 * 性能监控数据接口
 */
interface IPerformanceMetrics {
    timestamp: number;
    fps: number;
    memoryUsage: number;
    loadTime: number;
    bundleLoadTimes: { [bundleName: string]: number };
    sceneLoadTimes: { [sceneName: string]: number };
    networkLatency: number;
}

/**
 * 加载性能数据
 */
interface ILoadPerformance {
    startTime: number;
    endTime: number;
    duration: number;
    success: boolean;
    errorMessage?: string;
}

/**
 * 性能监控器
 * 监控分包加载性能、场景切换性能、内存使用等
 */
@ccclass('PerformanceMonitor')
export class PerformanceMonitor extends Component {
    private static _instance: PerformanceMonitor;

    /**
     * 性能数据历史记录
     */
    private _metricsHistory: IPerformanceMetrics[] = [];

    /**
     * 分包加载性能记录
     */
    private _bundleLoadPerformance: Map<string, ILoadPerformance[]> = new Map();

    /**
     * 场景加载性能记录
     */
    private _sceneLoadPerformance: Map<string, ILoadPerformance[]> = new Map();

    /**
     * 监控配置
     */
    private _config = {
        maxHistorySize: 100,
        sampleInterval: 1000, // 1秒采样一次
        enableAutoReport: true,
        reportInterval: 30000 // 30秒报告一次
    };

    /**
     * 当前监控状态
     */
    private _isMonitoring: boolean = false;
    private _monitoringTimer: number | null = null;
    private _reportTimer: number | null = null;

    /**
     * 获取单例实例
     */
    public static getInstance(): PerformanceMonitor {
        return PerformanceMonitor._instance;
    }

    protected onLoad(): void {
        PerformanceMonitor._instance = this;
        console.log('📊 性能监控器初始化');
    }

    protected start(): void {
        this.startMonitoring();
    }

    protected onDestroy(): void {
        this.stopMonitoring();
    }

    /**
     * 开始监控
     */
    public startMonitoring(): void {
        if (this._isMonitoring) {
            return;
        }

        this._isMonitoring = true;
        console.log('📊 开始性能监控');

        // 开始采样
        this._monitoringTimer = setInterval(() => {
            this.collectMetrics();
        }, this._config.sampleInterval);

        // 开始自动报告
        if (this._config.enableAutoReport) {
            this._reportTimer = setInterval(() => {
                this.generatePerformanceReport();
            }, this._config.reportInterval);
        }
    }

    /**
     * 停止监控
     */
    public stopMonitoring(): void {
        if (!this._isMonitoring) {
            return;
        }

        this._isMonitoring = false;
        console.log('📊 停止性能监控');

        if (this._monitoringTimer) {
            clearInterval(this._monitoringTimer);
            this._monitoringTimer = null;
        }

        if (this._reportTimer) {
            clearInterval(this._reportTimer);
            this._reportTimer = null;
        }
    }

    /**
     * 收集性能指标
     */
    private collectMetrics(): void {
        const metrics: IPerformanceMetrics = {
            timestamp: Date.now(),
            fps: this.getCurrentFPS(),
            memoryUsage: this.getMemoryUsage(),
            loadTime: 0, // 将在具体加载时更新
            bundleLoadTimes: this.getAverageBundleLoadTimes(),
            sceneLoadTimes: this.getAverageSceneLoadTimes(),
            networkLatency: 0 // 可以通过网络请求测试获得
        };

        this._metricsHistory.push(metrics);

        // 限制历史记录大小
        if (this._metricsHistory.length > this._config.maxHistorySize) {
            this._metricsHistory.shift();
        }
    }

    /**
     * 获取当前FPS
     */
    private getCurrentFPS(): number {
        // Cocos Creator中获取FPS的方法
        // 这里使用简化的实现，实际项目中可能需要更精确的计算
        return 60; // 默认值，实际应该从引擎获取
    }

    /**
     * 获取内存使用情况
     */
    private getMemoryUsage(): number {
        if (sys.platform === sys.Platform.WECHAT_GAME) {
            // 微信小游戏环境
            const performance = (globalThis as any).wx?.getPerformance?.();
            return performance?.usedJSHeapSize || 0;
        } else if (typeof performance !== 'undefined' && (performance as any).memory) {
            // Web环境
            return (performance as any).memory.usedJSHeapSize || 0;
        }
        return 0;
    }

    /**
     * 记录分包加载开始
     */
    public startBundleLoad(bundleName: string): string {
        const loadId = `${bundleName}_${Date.now()}`;
        const startTime = performance.now();
        
        if (!this._bundleLoadPerformance.has(bundleName)) {
            this._bundleLoadPerformance.set(bundleName, []);
        }

        console.log(`📦 开始加载分包: ${bundleName}`);
        return loadId;
    }

    /**
     * 记录分包加载结束
     */
    public endBundleLoad(bundleName: string, loadId: string, success: boolean, errorMessage?: string): void {
        const endTime = performance.now();
        const startTime = parseFloat(loadId.split('_')[1]);
        const duration = endTime - startTime;

        const loadPerformance: ILoadPerformance = {
            startTime,
            endTime,
            duration,
            success,
            errorMessage
        };

        const records = this._bundleLoadPerformance.get(bundleName);
        if (records) {
            records.push(loadPerformance);
            
            // 限制记录数量
            if (records.length > 20) {
                records.shift();
            }
        }

        console.log(`📦 分包加载完成: ${bundleName}, 耗时: ${duration.toFixed(2)}ms, 成功: ${success}`);
    }

    /**
     * 记录场景加载开始
     */
    public startSceneLoad(sceneName: string): string {
        const loadId = `${sceneName}_${Date.now()}`;
        const startTime = performance.now();
        
        if (!this._sceneLoadPerformance.has(sceneName)) {
            this._sceneLoadPerformance.set(sceneName, []);
        }

        console.log(`🎬 开始加载场景: ${sceneName}`);
        return loadId;
    }

    /**
     * 记录场景加载结束
     */
    public endSceneLoad(sceneName: string, loadId: string, success: boolean, errorMessage?: string): void {
        const endTime = performance.now();
        const startTime = parseFloat(loadId.split('_')[1]);
        const duration = endTime - startTime;

        const loadPerformance: ILoadPerformance = {
            startTime,
            endTime,
            duration,
            success,
            errorMessage
        };

        const records = this._sceneLoadPerformance.get(sceneName);
        if (records) {
            records.push(loadPerformance);
            
            // 限制记录数量
            if (records.length > 20) {
                records.shift();
            }
        }

        console.log(`🎬 场景加载完成: ${sceneName}, 耗时: ${duration.toFixed(2)}ms, 成功: ${success}`);
    }

    /**
     * 获取平均分包加载时间
     */
    private getAverageBundleLoadTimes(): { [bundleName: string]: number } {
        const averages: { [bundleName: string]: number } = {};

        for (const [bundleName, records] of this._bundleLoadPerformance) {
            const successfulRecords = records.filter(r => r.success);
            if (successfulRecords.length > 0) {
                const totalTime = successfulRecords.reduce((sum, r) => sum + r.duration, 0);
                averages[bundleName] = totalTime / successfulRecords.length;
            }
        }

        return averages;
    }

    /**
     * 获取平均场景加载时间
     */
    private getAverageSceneLoadTimes(): { [sceneName: string]: number } {
        const averages: { [sceneName: string]: number } = {};

        for (const [sceneName, records] of this._sceneLoadPerformance) {
            const successfulRecords = records.filter(r => r.success);
            if (successfulRecords.length > 0) {
                const totalTime = successfulRecords.reduce((sum, r) => sum + r.duration, 0);
                averages[sceneName] = totalTime / successfulRecords.length;
            }
        }

        return averages;
    }

    /**
     * 生成性能报告
     */
    public generatePerformanceReport(): void {
        if (this._metricsHistory.length === 0) {
            console.log('📊 暂无性能数据');
            return;
        }

        const latest = this._metricsHistory[this._metricsHistory.length - 1];
        const avgFPS = this._metricsHistory.reduce((sum, m) => sum + m.fps, 0) / this._metricsHistory.length;
        const avgMemory = this._metricsHistory.reduce((sum, m) => sum + m.memoryUsage, 0) / this._metricsHistory.length;

        console.log('📊 ========== 性能报告 ==========');
        console.log(`📈 当前FPS: ${latest.fps.toFixed(1)}, 平均FPS: ${avgFPS.toFixed(1)}`);
        console.log(`💾 当前内存: ${this.formatBytes(latest.memoryUsage)}, 平均内存: ${this.formatBytes(avgMemory)}`);
        
        console.log('📦 分包加载性能:');
        for (const [bundleName, avgTime] of Object.entries(latest.bundleLoadTimes)) {
            console.log(`  ${bundleName}: ${avgTime.toFixed(2)}ms`);
        }

        console.log('🎬 场景加载性能:');
        for (const [sceneName, avgTime] of Object.entries(latest.sceneLoadTimes)) {
            console.log(`  ${sceneName}: ${avgTime.toFixed(2)}ms`);
        }

        console.log('📊 ===============================');
    }

    /**
     * 格式化字节数
     */
    private formatBytes(bytes: number): string {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 获取性能统计信息
     */
    public getPerformanceStats(): any {
        return {
            metricsCount: this._metricsHistory.length,
            bundleLoadStats: this.getBundleLoadStats(),
            sceneLoadStats: this.getSceneLoadStats(),
            currentMetrics: this._metricsHistory[this._metricsHistory.length - 1] || null
        };
    }

    /**
     * 获取分包加载统计
     */
    private getBundleLoadStats(): any {
        const stats: any = {};
        for (const [bundleName, records] of this._bundleLoadPerformance) {
            const successCount = records.filter(r => r.success).length;
            const failCount = records.length - successCount;
            const avgTime = records.filter(r => r.success).reduce((sum, r) => sum + r.duration, 0) / successCount || 0;
            
            stats[bundleName] = {
                totalAttempts: records.length,
                successCount,
                failCount,
                successRate: records.length > 0 ? (successCount / records.length * 100).toFixed(1) + '%' : '0%',
                averageLoadTime: avgTime.toFixed(2) + 'ms'
            };
        }
        return stats;
    }

    /**
     * 获取场景加载统计
     */
    private getSceneLoadStats(): any {
        const stats: any = {};
        for (const [sceneName, records] of this._sceneLoadPerformance) {
            const successCount = records.filter(r => r.success).length;
            const failCount = records.length - successCount;
            const avgTime = records.filter(r => r.success).reduce((sum, r) => sum + r.duration, 0) / successCount || 0;
            
            stats[sceneName] = {
                totalAttempts: records.length,
                successCount,
                failCount,
                successRate: records.length > 0 ? (successCount / records.length * 100).toFixed(1) + '%' : '0%',
                averageLoadTime: avgTime.toFixed(2) + 'ms'
            };
        }
        return stats;
    }
}
