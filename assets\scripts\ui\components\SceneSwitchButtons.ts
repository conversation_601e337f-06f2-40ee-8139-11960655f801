import { _decorator, Component, Node, director, input, Input, EventKeyboard, KeyCode, find } from 'cc';
import { SceneManager } from '../../managers/SceneManager';
import { LocationConfigManager } from '../../managers/LocationConfigManager';

const { ccclass, property } = _decorator;

/**
 * 场景切换测试组件
 * 提供键盘快捷键切换场景功能
 */
@ccclass('SceneSwitchButtons')
export class SceneSwitchButtons extends Component {

    protected onLoad(): void {
        console.log('🎮 场景切换测试组件加载');
        this.initializeKeyboardInput();
        this.showInstructions();
    }

    protected start(): void {
        console.log('🎮 场景切换测试组件开始');
    }

    /**
     * 初始化键盘输入
     */
    private initializeKeyboardInput(): void {
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log('✅ 键盘输入初始化完成');
    }

    /**
     * 显示操作说明
     */
    private showInstructions(): void {
        const currentScene = director.getScene();
        const sceneName = currentScene ? currentScene.name : '未知';

        console.log('🎮 ========== 地点切换测试 ==========');
        console.log(`📍 当前场景: ${sceneName}`);
        console.log('⌨️ 键盘快捷键:');
        console.log('   按 L 键 - 切换到 Launch 场景');
        console.log('   按 M 键 - 切换到 Main 场景');
        console.log('   按 B 键 - 切换到 Battle 场景');
        console.log('   按 F 键 - 切换到森林地点（分包配置）');
        console.log('   按 D 键 - 切换到沙漠地点（分包配置）');
        console.log('   按 T 键 - 切换到山脉地点（分包配置）');
        console.log('   按 H 键 - 显示帮助信息');
        console.log('🗺️ 地点切换在LinearBranchMapContainer中进行');
        console.log('🎮 ===================================');
    }

    /**
     * 键盘按键处理
     */
    private onKeyDown(event: EventKeyboard): void {
        switch (event.keyCode) {
            case KeyCode.DIGIT_1:
                this.goToLaunch();
                break;
            case KeyCode.DIGIT_2:
                this.goToMain();
                break;
            case KeyCode.DIGIT_3:
                this.goToBattle();
                break;
            case KeyCode.KEY_F:
                this.goToForest();
                break;
            case KeyCode.KEY_D:
                this.goToDesert();
                break;
            case KeyCode.KEY_T:
                this.goToMountain();
                break;
            case KeyCode.KEY_H:
                this.showInstructions();
                break;
        }
    }

    /**
     * 切换到启动场景
     */
    private goToLaunch(): void {
        console.log('🚀 切换到启动场景');
        this.switchScene('Launch');
    }

    /**
     * 切换到主界面场景
     */
    private goToMain(): void {
        console.log('🏠 切换到主界面场景');
        this.switchScene('Main');
    }

    /**
     * 切换到战斗场景
     */
    private goToBattle(): void {
        console.log('⚔️ 切换到战斗场景');
        this.switchScene('Battle');
    }

    /**
     * 切换到森林地点
     */
    private goToForest(): void {
        console.log('🌲 切换到森林地点');
        this.switchLocationInContainer('Forest');
    }

    /**
     * 切换到沙漠地点
     */
    private goToDesert(): void {
        console.log('🏜️ 切换到沙漠地点');
        this.switchLocationInContainer('Desert');
    }

    /**
     * 切换到山脉地点
     */
    private goToMountain(): void {
        console.log('⛰️ 切换到山脉地点');
        this.switchLocationInContainer('Mountain');
    }

    /**
     * 通用场景切换方法（传统方式）
     */
    private switchScene(sceneName: string): void {
        try {
            console.log(`🔄 正在切换到场景: ${sceneName}`);
            director.loadScene(sceneName, (error) => {
                if (error) {
                    console.error(`❌ 场景切换失败: ${sceneName}`, error);
                } else {
                    console.log(`✅ 场景切换成功: ${sceneName}`);
                }
            });
        } catch (error) {
            console.error(`❌ 场景切换异常: ${sceneName}`, error);
        }
    }

    /**
     * 带配置的场景切换方法（支持分包加载）
     */
    private async switchSceneWithConfig(sceneName: string): Promise<void> {
        try {
            console.log(`🔄 正在切换到场景（带配置）: ${sceneName}`);

            const sceneManager = SceneManager.getInstance();
            await sceneManager.switchScene(sceneName);

            console.log(`✅ 场景切换成功（带配置）: ${sceneName}`);
        } catch (error) {
            console.error(`❌ 场景切换失败（带配置）: ${sceneName}`, error);
        }
    }

    /**
     * 在LinearBranchMapContainer中切换地点（扩展支持区域功能）
     */
    private async switchLocationInContainer(locationId: string): Promise<void> {
        try {
            console.log(`🗺️ 正在切换地点: ${locationId}`);

            // 查找LinearBranchMapContainer（更新路径：现在在BehaviorPanel下）
            const mapContainer = find('Canvas/MainUI/BehaviorPanel/LinearBranchMapContainer');
            if (!mapContainer) {
                console.error('❌ 未找到LinearBranchMapContainer');
                return;
            }

            // 获取地点配置管理器
            const locationConfigManager = LocationConfigManager.getInstance();

            // 使用扩展后的LocationConfigManager应用配置
            // 这个方法现在包含了解锁检查、行为加载、事件发送等功能
            const success = await locationConfigManager.applyLocationConfigToContainer(locationId, mapContainer);

            if (success) {
                console.log(`✅ 地点切换成功: ${locationId}`);

                // 显示地点行为信息
                await this.showLocationBehaviors(locationId);

                // 查找InteractiveMapController组件
                const mapController = mapContainer.getComponentInChildren('InteractiveMapController');
                if (mapController && mapController.resetMapState) {
                    mapController.resetMapState();
                }
            } else {
                console.log(`❌ 地点切换失败: ${locationId}`);
            }

            console.log(`✅ 地点切换成功: ${locationId}`);
        } catch (error) {
            console.error(`❌ 地点切换失败: ${locationId}`, error);
        }
    }

    /**
     * 应用地点配置到地图面板
     */
    private async applyLocationConfigToMapPanel(mapPanel: any, config: any): Promise<void> {
        try {
            console.log(`🎨 应用地点配置: ${config.locationId}`);

            // 应用地图配置
            if (config.mapConfig) {
                mapPanel.backgroundImagePath = config.mapConfig.backgroundImagePath;
                mapPanel.nodeSpacingX = config.mapConfig.nodeSpacing.x;
                mapPanel.nodeSpacingY = config.mapConfig.nodeSpacing.y;
                mapPanel.branchCount = config.mapConfig.branchCount;
                mapPanel.nodesPerBranch = config.mapConfig.nodesPerBranch;
            }

            // 重新生成地图
            if (mapPanel.regenerateMap) {
                mapPanel.regenerateMap();
            }

            // 应用环境设置
            if (config.environment) {
                await this.applyEnvironmentSettings(config.environment);
            }

            console.log(`✅ 地点配置应用完成: ${config.locationId}`);
        } catch (error) {
            console.error(`❌ 应用地点配置失败:`, error);
        }
    }

    /**
     * 应用环境设置
     */
    private async applyEnvironmentSettings(environment: any): Promise<void> {
        console.log('🌍 应用环境设置:', environment);

        // 设置背景音乐
        if (environment.backgroundMusic) {
            // TODO: 播放背景音乐
            console.log(`🎵 设置背景音乐: ${environment.backgroundMusic}`);
        }

        // 设置环境音效
        if (environment.ambientSounds) {
            // TODO: 播放环境音效
            console.log(`🔊 设置环境音效:`, environment.ambientSounds);
        }

        // 设置光照颜色
        if (environment.lightingColor) {
            // TODO: 设置场景光照
            console.log(`💡 设置光照颜色:`, environment.lightingColor);
        }
    }

    protected onEnable(): void {
        this.showInstructions();
    }

    protected onDestroy(): void {
        // 清理键盘输入事件
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);

        console.log('🎮 场景切换测试组件销毁');
    }

    // ==================== 新增：区域功能扩展方法 ====================

    /**
     * 显示地点行为信息
     */
    private async showLocationBehaviors(locationId: string): Promise<void> {
        try {
            const locationConfigManager = LocationConfigManager.getInstance();
            const behaviors = await locationConfigManager.getLocationBehaviors(locationId);

            if (behaviors.length > 0) {
                console.log(`🎭 地点${locationId}可用行为:`);
                behaviors.forEach(behavior => {
                    console.log(`   - ${behavior.name}: ${behavior.description} (${behavior.duration}秒)`);
                });
            } else {
                console.log(`📝 地点${locationId}暂无可用行为`);
            }
        } catch (error) {
            console.error('❌ 显示地点行为信息失败', error);
        }
    }

    /**
     * 检查地点解锁状态
     */
    public async checkLocationUnlockStatus(locationId: string): Promise<void> {
        try {
            const locationConfigManager = LocationConfigManager.getInstance();
            const unlockStatus = await locationConfigManager.getLocationUnlockStatus(locationId);

            if (unlockStatus) {
                console.log(`🔓 地点${locationId}解锁状态:`);
                console.log(`   解锁状态: ${unlockStatus.isUnlocked ? '已解锁' : '未解锁'}`);
                console.log(`   解锁进度: ${(unlockStatus.unlockProgress * 100).toFixed(1)}%`);

                if (!unlockStatus.isUnlocked && unlockStatus.unmetConditions.length > 0) {
                    console.log(`   未满足条件: ${unlockStatus.unmetConditions.join(', ')}`);
                }
            }
        } catch (error) {
            console.error('❌ 检查地点解锁状态失败', error);
        }
    }

    /**
     * 执行地点行为
     */
    public async executeLocationBehavior(locationId: string, behaviorId: string): Promise<void> {
        try {
            const locationConfigManager = LocationConfigManager.getInstance();
            const success = await locationConfigManager.executeLocationBehavior(locationId, behaviorId);

            if (success) {
                console.log(`✅ 地点行为执行成功: ${behaviorId}`);
            } else {
                console.log(`❌ 地点行为执行失败: ${behaviorId}`);
            }
        } catch (error) {
            console.error('❌ 执行地点行为异常', error);
        }
    }

    /**
     * 获取当前地点的第一个可用行为并执行
     */
    public async executeFirstAvailableBehavior(locationId: string): Promise<void> {
        try {
            const locationConfigManager = LocationConfigManager.getInstance();
            const behaviors = await locationConfigManager.getLocationBehaviors(locationId);

            if (behaviors.length > 0) {
                const firstBehavior = behaviors[0];
                await this.executeLocationBehavior(locationId, firstBehavior.id);
            } else {
                console.log(`⚠️ 地点${locationId}没有可用行为`);
            }
        } catch (error) {
            console.error('❌ 执行第一个可用行为失败', error);
        }
    }
}
