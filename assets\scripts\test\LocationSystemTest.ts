/**
 * Copyright (c) 2025 "放置". All rights reserved.
 * 地点系统测试（方案A实现）
 */

import { _decorator, Component, input, Input, KeyCode } from 'cc';
import { LocationConfigManager } from '../managers/LocationConfigManager';
import { SceneSwitchButtons } from '../ui/components/SceneSwitchButtons';

const { ccclass, property } = _decorator;

/**
 * 地点系统测试组件（基于扩展的LocationConfigManager）
 */
@ccclass('LocationSystemTest')
export class LocationSystemTest extends Component {

    @property({ tooltip: '是否启用键盘快捷键' })
    public enableKeyboardShortcuts: boolean = true;

    @property({ tooltip: '是否显示详细日志' })
    public enableDetailedLogging: boolean = true;

    // 管理器实例
    private _locationConfigManager: LocationConfigManager | null = null;
    private _sceneSwitchButtons: SceneSwitchButtons | null = null;

    protected onLoad(): void {
        console.log('🧪 LocationSystemTest: 地点系统测试组件加载');
        this.initializeManagers();
        
        if (this.enableKeyboardShortcuts) {
            this.setupKeyboardShortcuts();
        }
    }

    protected start(): void {
        // 延迟2秒开始测试，确保所有组件都已初始化
        this.scheduleOnce(() => {
            this.runBasicTest();
        }, 2.0);
    }

    protected onDestroy(): void {
        if (this.enableKeyboardShortcuts) {
            input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        }
    }

    /**
     * 初始化管理器实例
     */
    private initializeManagers(): void {
        this._locationConfigManager = LocationConfigManager.getInstance();
        
        // 查找SceneSwitchButtons组件
        const sceneSwitchNode = this.node.getComponentInChildren(SceneSwitchButtons);
        if (sceneSwitchNode) {
            this._sceneSwitchButtons = sceneSwitchNode;
        }

        if (this.enableDetailedLogging) {
            console.log('🧪 LocationSystemTest: 管理器实例获取状态:', {
                locationConfigManager: !!this._locationConfigManager,
                sceneSwitchButtons: !!this._sceneSwitchButtons
            });
        }
    }

    /**
     * 设置键盘快捷键
     */
    private setupKeyboardShortcuts(): void {
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        
        console.log('🧪 LocationSystemTest: 键盘快捷键已启用');
        console.log('快捷键说明:');
        console.log('  T - 运行基础测试');
        console.log('  1 - 切换到鱼塘');
        console.log('  2 - 切换到农场');
        console.log('  3 - 切换到森林');
        console.log('  U - 检查所有地点解锁状态');
        console.log('  B - 执行当前地点第一个行为');
        console.log('  L - 显示所有地点行为列表');
    }

    /**
     * 键盘按键处理
     */
    private onKeyDown(event: any): void {
        switch (event.keyCode) {
            case KeyCode.KEY_T:
                this.runBasicTest();
                break;
            case KeyCode.DIGIT_1:
                this.testLocationSwitch('fishpond');
                break;
            case KeyCode.DIGIT_2:
                this.testLocationSwitch('farm');
                break;
            case KeyCode.DIGIT_3:
                this.testLocationSwitch('forest');
                break;
            case KeyCode.KEY_U:
                this.testAllLocationUnlockStatus();
                break;
            case KeyCode.KEY_B:
                this.testExecuteFirstBehavior();
                break;
            case KeyCode.KEY_L:
                this.testShowAllLocationBehaviors();
                break;
        }
    }

    /**
     * 运行基础测试
     */
    public async runBasicTest(): Promise<void> {
        console.log('🧪 LocationSystemTest: 开始基础测试');

        try {
            // 测试1: 管理器初始化检查
            await this.testManagerInitialization();

            // 测试2: 地点配置加载测试
            await this.testLocationConfigLoading();

            // 测试3: 地点解锁状态测试
            await this.testLocationUnlockStatus();

            // 测试4: 地点行为加载测试
            await this.testLocationBehaviorLoading();

            console.log('✅ LocationSystemTest: 基础测试完成');

        } catch (error) {
            console.error('❌ LocationSystemTest: 基础测试失败', error);
        }
    }

    /**
     * 测试管理器初始化
     */
    private async testManagerInitialization(): Promise<void> {
        console.log('🧪 测试1: 管理器初始化检查');

        const managerOk = this._locationConfigManager !== null;
        console.log(`   LocationConfigManager: ${managerOk ? '✅' : '❌'}`);

        if (!managerOk) {
            throw new Error('LocationConfigManager初始化失败');
        }
    }

    /**
     * 测试地点配置加载
     */
    private async testLocationConfigLoading(): Promise<void> {
        console.log('🧪 测试2: 地点配置加载测试');

        if (!this._locationConfigManager) {
            throw new Error('LocationConfigManager不可用');
        }

        // 测试加载鱼塘配置
        const fishpondConfig = await this._locationConfigManager.getLocationConfig('fishpond');
        const fishpondOk = fishpondConfig !== null;
        console.log(`   鱼塘配置加载: ${fishpondOk ? '✅' : '❌'}`);

        if (fishpondOk && this.enableDetailedLogging) {
            console.log(`   鱼塘配置详情:`, {
                name: fishpondConfig!.name,
                type: fishpondConfig!.type,
                level: fishpondConfig!.level,
                behaviorsCount: fishpondConfig!.behaviors?.length || 0
            });
        }
    }

    /**
     * 测试地点解锁状态
     */
    private async testLocationUnlockStatus(): Promise<void> {
        console.log('🧪 测试3: 地点解锁状态测试');

        if (!this._locationConfigManager) {
            throw new Error('LocationConfigManager不可用');
        }

        const locations = ['fishpond', 'farm', 'forest'];
        
        for (const locationId of locations) {
            const unlockStatus = await this._locationConfigManager.getLocationUnlockStatus(locationId);
            
            if (unlockStatus) {
                console.log(`   ${locationId}: ${unlockStatus.isUnlocked ? '✅已解锁' : '❌未解锁'} (${(unlockStatus.unlockProgress * 100).toFixed(1)}%)`);
                
                if (!unlockStatus.isUnlocked && this.enableDetailedLogging) {
                    console.log(`     未满足条件: ${unlockStatus.unmetConditions.join(', ')}`);
                }
            } else {
                console.log(`   ${locationId}: ❌配置不存在`);
            }
        }
    }

    /**
     * 测试地点行为加载
     */
    private async testLocationBehaviorLoading(): Promise<void> {
        console.log('🧪 测试4: 地点行为加载测试');

        if (!this._locationConfigManager) {
            throw new Error('LocationConfigManager不可用');
        }

        const locations = ['fishpond', 'farm', 'forest'];
        
        for (const locationId of locations) {
            const behaviors = await this._locationConfigManager.getLocationBehaviors(locationId);
            console.log(`   ${locationId}: ${behaviors.length}个行为`);
            
            if (this.enableDetailedLogging && behaviors.length > 0) {
                behaviors.forEach(behavior => {
                    console.log(`     - ${behavior.name} (${behavior.type}, ${behavior.duration}秒)`);
                });
            }
        }
    }

    /**
     * 测试地点切换
     */
    public async testLocationSwitch(locationId: string): Promise<void> {
        console.log(`🧪 LocationSystemTest: 测试切换到地点 - ${locationId}`);

        if (!this._locationConfigManager) {
            console.error('❌ LocationConfigManager不可用');
            return;
        }

        try {
            // 检查解锁状态
            const unlockStatus = await this._locationConfigManager.getLocationUnlockStatus(locationId);
            if (unlockStatus && !unlockStatus.isUnlocked) {
                console.log(`⚠️ 地点${locationId}未解锁，无法切换`);
                console.log(`   解锁进度: ${(unlockStatus.unlockProgress * 100).toFixed(1)}%`);
                console.log(`   未满足条件: ${unlockStatus.unmetConditions.join(', ')}`);
                return;
            }

            // 使用SceneSwitchButtons进行切换
            if (this._sceneSwitchButtons) {
                // 这里需要调用SceneSwitchButtons的私有方法，暂时直接调用LocationConfigManager
                const mapContainer = { /* 模拟容器 */ };
                const success = await this._locationConfigManager.applyLocationConfigToContainer(locationId, mapContainer);
                
                if (success) {
                    console.log(`✅ 地点切换成功: ${locationId}`);
                } else {
                    console.log(`❌ 地点切换失败: ${locationId}`);
                }
            } else {
                console.warn('⚠️ SceneSwitchButtons不可用，直接使用LocationConfigManager测试');
                
                const config = await this._locationConfigManager.getLocationConfig(locationId);
                if (config) {
                    console.log(`✅ 地点配置获取成功: ${config.name}`);
                } else {
                    console.log(`❌ 地点配置获取失败: ${locationId}`);
                }
            }

        } catch (error) {
            console.error(`❌ 地点切换测试异常: ${locationId}`, error);
        }
    }

    /**
     * 测试所有地点解锁状态
     */
    public async testAllLocationUnlockStatus(): Promise<void> {
        console.log('🧪 LocationSystemTest: 检查所有地点解锁状态');

        if (this._sceneSwitchButtons && this._sceneSwitchButtons.checkLocationUnlockStatus) {
            const locations = ['fishpond', 'farm', 'forest'];
            
            for (const locationId of locations) {
                await this._sceneSwitchButtons.checkLocationUnlockStatus(locationId);
            }
        } else {
            console.warn('⚠️ SceneSwitchButtons不可用，使用LocationConfigManager检查');
            await this.testLocationUnlockStatus();
        }
    }

    /**
     * 测试执行第一个行为
     */
    public async testExecuteFirstBehavior(): Promise<void> {
        console.log('🧪 LocationSystemTest: 测试执行第一个行为');

        // 默认在鱼塘执行第一个行为
        const locationId = 'fishpond';
        
        if (this._sceneSwitchButtons && this._sceneSwitchButtons.executeFirstAvailableBehavior) {
            await this._sceneSwitchButtons.executeFirstAvailableBehavior(locationId);
        } else {
            console.warn('⚠️ SceneSwitchButtons不可用');
        }
    }

    /**
     * 测试显示所有地点行为列表
     */
    public async testShowAllLocationBehaviors(): Promise<void> {
        console.log('🧪 LocationSystemTest: 显示所有地点行为列表');

        if (!this._locationConfigManager) {
            console.error('❌ LocationConfigManager不可用');
            return;
        }

        const locations = ['fishpond', 'farm', 'forest'];
        
        for (const locationId of locations) {
            console.log(`📋 ${locationId}的行为列表:`);
            const behaviors = await this._locationConfigManager.getLocationBehaviors(locationId);
            
            if (behaviors.length > 0) {
                behaviors.forEach((behavior, index) => {
                    console.log(`   ${index + 1}. ${behavior.name}`);
                    console.log(`      类型: ${behavior.type}`);
                    console.log(`      时长: ${behavior.duration}秒`);
                    console.log(`      冷却: ${behavior.cooldown}秒`);
                    console.log(`      能量: ${behavior.energyCost}`);
                    console.log(`      描述: ${behavior.description}`);
                });
            } else {
                console.log('   暂无可用行为');
            }
        }
    }
}
