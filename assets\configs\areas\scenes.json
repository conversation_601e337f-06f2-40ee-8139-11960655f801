{"version": "1.0.0", "config_type": "scenes", "last_updated": "2025-08-10T07:52:42.086Z", "scenes": {"fishpond": {"locationId": "fishpond", "name": "鱼塘", "description": "宁静的鱼塘，适合钓鱼放松", "type": "fishing", "level": 1, "unlockConditions": {"playerLevel": 1, "requiredItems": [], "completedQuests": [], "requiredAreas": []}, "mapConfig": {"backgroundImagePath": "areas/fishpond/background", "mapSize": {"width": 1600, "height": 1200}, "nodeSpacing": {"x": 200, "y": 150}, "startPosition": {"x": -600, "y": 0}, "branchCount": 3, "nodesPerBranch": 5}, "environment": {"weather": "sunny", "timeOfDay": "morning", "backgroundMusic": "audio/bgm/peaceful_pond", "ambientSounds": ["water_ripple", "bird_chirping"], "lightingColor": {"r": 255, "g": 248, "b": 220, "a": 255}}, "rewards": {"baseExp": 10, "baseGold": 5, "dropTables": ["fishing_drops"]}, "behaviors": {"fishing_basic": {"behaviorId": "fishing_basic", "name": "基础钓鱼", "description": "使用基础鱼竿进行钓鱼", "type": "fishing", "duration": 30, "cooldown": 5, "energyCost": 10, "requirements": {"items": ["fishing_rod_basic"], "skills": [], "level": 1}, "rewards": {"baseExp": 10, "items": [{"id": "fish_common", "probability": 0.7, "minQuantity": 1, "maxQuantity": 1}, {"id": "fish_rare", "probability": 0.2, "minQuantity": 1, "maxQuantity": 1}, {"id": "treasure_box", "probability": 0.1, "minQuantity": 1, "maxQuantity": 1}], "currency": {"gold": {"min": 5, "max": 15}}}}, "fishing_advanced": {"behaviorId": "fishing_advanced", "name": "高级钓鱼", "description": "使用高级鱼竿进行钓鱼，获得更好的收益", "type": "fishing", "duration": 60, "cooldown": 10, "energyCost": 20, "requirements": {"items": ["fishing_rod_advanced"], "skills": [], "level": 10}, "rewards": {"baseExp": 25, "items": [{"id": "fish_rare", "probability": 0.5, "minQuantity": 1, "maxQuantity": 1}, {"id": "fish_epic", "probability": 0.3, "minQuantity": 1, "maxQuantity": 1}, {"id": "treasure_box_rare", "probability": 0.2, "minQuantity": 1, "maxQuantity": 1}], "currency": {"gold": {"min": 15, "max": 35}}}}}, "uiLayout": {"behaviorButtonsPosition": {"x": 0, "y": -300}, "infoDisplayPosition": {"x": -400, "y": 250}, "customElements": [{"type": "weather_display", "position": {"x": 350, "y": 250}, "config": {"showTemperature": "true", "showWind": "false"}}]}}, "farm": {"locationId": "farm", "name": "农场", "description": "肥沃的农场，适合种植各种作物", "type": "farming", "level": 2, "unlockConditions": {"playerLevel": 5, "requiredItems": [], "completedQuests": ["tutorial_fishing"], "requiredAreas": ["fishpond"]}, "mapConfig": {"backgroundImagePath": "areas/farm/background", "mapSize": {"width": 1800, "height": 1400}, "nodeSpacing": {"x": 180, "y": 160}, "startPosition": {"x": -700, "y": 0}, "branchCount": 4, "nodesPerBranch": 6}, "environment": {"weather": "sunny", "timeOfDay": "afternoon", "backgroundMusic": "audio/bgm/farm_life", "ambientSounds": ["wind_through_crops", "farm_animals"], "lightingColor": {"r": 255, "g": 235, "b": 200, "a": 255}}, "rewards": {"baseExp": 15, "baseGold": 8, "dropTables": ["farming_drops"]}, "behaviors": {"planting_basic": {"behaviorId": "planting_basic", "name": "基础种植", "description": "种植基础作物", "type": "farming", "duration": 45, "cooldown": 0, "energyCost": 15, "requirements": {"items": ["seeds_basic"], "skills": [], "level": 5}, "rewards": {"baseExp": 15, "items": [{"id": "crop_wheat", "probability": 0.8, "minQuantity": 1, "maxQuantity": 1}, {"id": "crop_corn", "probability": 0.6, "minQuantity": 1, "maxQuantity": 1}], "currency": {"gold": {"min": 8, "max": 20}}}}}, "uiLayout": {"behaviorButtonsPosition": {"x": 0, "y": -320}, "infoDisplayPosition": {"x": -420, "y": 280}, "customElements": [{"type": "crop_status_display", "position": {"x": 380, "y": 280}, "config": {"showGrowthTime": "true", "showYield": "true"}}]}}}}