# Copyright (c) 2025 "放置". All rights reserved.

# 策划XML配置指南

## 🎯 XML配置方案优势

老板，您选择XML配置格式非常明智！相比Excel，XML具有以下优势：

✅ **结构化数据**：支持复杂的嵌套结构，适合游戏配置
✅ **验证机制**：XSD Schema确保数据格式正确
✅ **注释支持**：可以在配置中添加详细说明
✅ **版本控制友好**：文本格式，Git管理方便
✅ **工具丰富**：有专业的XML编辑器支持
✅ **自动转换**：一键转换为游戏可用的JSON格式

## 📁 工作目录结构

```
tools/planning-configs/
├── 📊 xml-configs/           # XML配置文件工作区
│   ├── scenes.xml           # 场景配置 ✅ 已创建
│   ├── monsters.xml         # 怪物配置
│   ├── items.xml            # 物品配置
│   ├── skills.xml           # 技能配置
│   └── balance.xml          # 数值平衡配置
├── 🔧 conversion-tools/      # 转换工具
│   ├── xml-to-json.js       # XML转JSON工具 ✅ 已创建
│   ├── convert-xml.bat      # 一键转换脚本 ✅ 已创建
│   └── validate-xml.js      # XML验证工具
├── 📋 xml-schemas/           # XML Schema定义
│   ├── scenes.xsd           # 场景Schema ✅ 已创建
│   ├── monsters.xsd         # 怪物Schema
│   └── items.xsd            # 物品Schema
└── 📖 docs/                  # 说明文档
    └── 策划XML配置指南.md   # 本文档
```

## 🚀 策划工作流程

### 第一步：编辑XML配置
在 `xml-configs/` 目录中编辑对应的XML文件

### 第二步：验证XML格式
使用XML编辑器或验证工具检查格式

### 第三步：一键转换
双击运行 `conversion-tools/convert-xml.bat`

### 第四步：测试验证
在游戏中测试新配置的效果

## 📊 场景配置示例详解

### XML结构说明
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!-- 场景配置表 - 策划专用XML格式 -->
<GameScenes version="1.0.0" lastUpdated="2025-01-10T00:00:00Z">
  
  <!-- 单个场景配置 -->
  <Scene id="fishpond">
    <!-- 基础信息 -->
    <BasicInfo>
      <Name>鱼塘</Name>                    <!-- 场景显示名称 -->
      <Description>宁静的鱼塘</Description>  <!-- 场景描述 -->
      <Type>fishing</Type>                 <!-- 场景类型 -->
      <Level>1</Level>                     <!-- 推荐等级 -->
    </BasicInfo>
    
    <!-- 解锁条件 -->
    <UnlockConditions>
      <PlayerLevel>1</PlayerLevel>         <!-- 玩家等级要求 -->
      <RequiredItems></RequiredItems>      <!-- 需要的物品 -->
      <CompletedQuests></CompletedQuests>  <!-- 需要完成的任务 -->
      <RequiredAreas></RequiredAreas>      <!-- 前置场景 -->
    </UnlockConditions>
    
    <!-- 地图配置 -->
    <MapConfig>
      <BackgroundImagePath>areas/fishpond/background</BackgroundImagePath>
      <MapSize width="1600" height="1200" />
      <NodeSpacing x="200" y="150" />
      <StartPosition x="-600" y="0" />
      <BranchCount>3</BranchCount>
      <NodesPerBranch>5</NodesPerBranch>
    </MapConfig>
    
    <!-- 环境设置 -->
    <Environment>
      <Weather>sunny</Weather>             <!-- 天气：sunny/cloudy/rainy -->
      <TimeOfDay>morning</TimeOfDay>       <!-- 时间：morning/afternoon/evening/night -->
      <BackgroundMusic>audio/bgm/peaceful_pond</BackgroundMusic>
      <AmbientSounds>
        <Sound>water_ripple</Sound>        <!-- 环境音效 -->
        <Sound>bird_chirping</Sound>
      </AmbientSounds>
      <LightingColor r="255" g="248" b="220" a="255" />
    </Environment>
    
    <!-- 基础奖励 -->
    <Rewards>
      <BaseExp>10</BaseExp>                <!-- 基础经验值 -->
      <BaseGold>5</BaseGold>               <!-- 基础金币 -->
      <DropTables>
        <DropTable>fishing_drops</DropTable>
      </DropTables>
    </Rewards>
    
    <!-- 行为配置 -->
    <Behaviors>
      <Behavior id="fishing_basic">
        <Name>基础钓鱼</Name>
        <Description>使用基础鱼竿进行钓鱼</Description>
        <Type>fishing</Type>
        <Duration>30</Duration>            <!-- 持续时间（秒） -->
        <Cooldown>5</Cooldown>             <!-- 冷却时间（秒） -->
        <EnergyCost>10</EnergyCost>        <!-- 消耗体力 -->
        
        <!-- 需求条件 -->
        <Requirements>
          <Items>
            <Item>fishing_rod_basic</Item>  <!-- 需要的物品 -->
          </Items>
          <Skills></Skills>                <!-- 需要的技能 -->
          <Level>1</Level>                 <!-- 需要的等级 -->
        </Requirements>
        
        <!-- 行为奖励 -->
        <Rewards>
          <BaseExp>10</BaseExp>
          <Items>
            <!-- 物品奖励：ID、概率、最小数量、最大数量 -->
            <Item id="fish_common" probability="0.7" minQuantity="1" maxQuantity="3" />
            <Item id="fish_rare" probability="0.2" minQuantity="1" maxQuantity="1" />
            <Item id="treasure_box" probability="0.1" minQuantity="1" maxQuantity="1" />
          </Items>
          <Currency>
            <Gold min="5" max="15" />      <!-- 金币奖励范围 -->
          </Currency>
        </Rewards>
      </Behavior>
    </Behaviors>
    
    <!-- UI布局 -->
    <UILayout>
      <BehaviorButtonsPosition x="0" y="-300" />
      <InfoDisplayPosition x="-400" y="250" />
      <CustomElements>
        <Element type="weather_display" x="350" y="250">
          <Config showTemperature="true" showWind="false" />
        </Element>
      </CustomElements>
    </UILayout>
  </Scene>
  
</GameScenes>
```

## 🔧 配置字段说明

### 基础信息 (BasicInfo)
- **Name**: 场景在游戏中显示的名称
- **Description**: 场景描述，用于UI提示
- **Type**: 场景类型（fishing/farming/mining/combat等）
- **Level**: 推荐玩家等级

### 解锁条件 (UnlockConditions)
- **PlayerLevel**: 玩家等级要求
- **RequiredItems**: 需要拥有的物品列表
- **CompletedQuests**: 需要完成的任务列表
- **RequiredAreas**: 需要先解锁的场景列表

### 地图配置 (MapConfig)
- **BackgroundImagePath**: 背景图片路径
- **MapSize**: 地图尺寸（width, height）
- **NodeSpacing**: 节点间距（x, y）
- **StartPosition**: 起始位置（x, y）
- **BranchCount**: 分支数量
- **NodesPerBranch**: 每个分支的节点数

### 环境设置 (Environment)
- **Weather**: 天气效果（sunny/cloudy/rainy/snowy）
- **TimeOfDay**: 时间段（morning/afternoon/evening/night）
- **BackgroundMusic**: 背景音乐路径
- **AmbientSounds**: 环境音效列表
- **LightingColor**: 光照颜色（RGBA值）

### 行为配置 (Behaviors)
- **Duration**: 行为持续时间（秒）
- **Cooldown**: 冷却时间（秒）
- **EnergyCost**: 消耗的体力值
- **Requirements**: 执行条件（物品、技能、等级）
- **Rewards**: 奖励配置（经验、物品、货币）

## ⚡ 一键转换使用

### 方法1：使用批处理脚本
1. 双击 `conversion-tools/convert-xml.bat`
2. 脚本会自动转换所有XML文件
3. 生成的JSON文件保存到 `assets/configs/` 对应目录

### 方法2：命令行转换
```bash
# 转换单个文件
node xml-to-json.js ../xml-configs/scenes.xml ../../../assets/configs/areas/

# 批量转换整个目录
node xml-to-json.js ../xml-configs/ ../../../assets/configs/
```

## ✅ 转换验证结果

我们已经成功测试了XML到JSON的转换：

```
🔄 转换文件: tools/planning-configs/xml-configs/scenes.xml
✅ 转换成功: scenes.xml → scenes.json
```

生成的JSON文件结构完整，包含：
- ✅ 场景基础信息
- ✅ 解锁条件配置
- ✅ 地图和环境设置
- ✅ 行为和奖励配置
- ✅ UI布局信息

## 🎯 策划工作建议

### 1. XML编辑工具推荐
- **Visual Studio Code**: 免费，支持XML语法高亮和验证
- **XMLSpy**: 专业XML编辑器，功能强大
- **Notepad++**: 轻量级，支持XML格式化

### 2. 配置编辑流程
1. **复制模板**: 从现有配置复制作为模板
2. **修改数值**: 根据策划需求调整参数
3. **添加注释**: 在重要配置处添加说明
4. **验证格式**: 确保XML格式正确
5. **转换测试**: 运行转换工具并在游戏中测试

### 3. 注意事项
- ⚠️ **保持格式**: 严格按照XML格式编写
- ⚠️ **数值范围**: 注意数值的合理范围
- ⚠️ **ID唯一性**: 确保所有ID在同类型中唯一
- ⚠️ **依赖关系**: 注意配置间的依赖关系

## 🚨 常见问题解决

### Q: XML格式错误怎么办？
A: 使用XML编辑器的验证功能，或查看转换工具的错误提示

### Q: 转换后的JSON格式不对？
A: 检查XML结构是否符合Schema定义

### Q: 游戏中配置不生效？
A: 确认JSON文件已正确生成到assets/configs/目录

### Q: 如何添加新的配置字段？
A: 先在XSD Schema中定义，然后在转换工具中添加处理逻辑

## 🎉 总结

老板，XML配置方案已经完全就绪！

✅ **XML配置文件**: 结构化、可验证、易维护
✅ **自动转换工具**: 一键转换XML到JSON
✅ **完整工作流程**: 从编辑到部署的完整流程
✅ **安全保障**: 配置文件不会影响构建打包

策划同事们现在可以：
1. 在 `tools/planning-configs/xml-configs/` 中编辑XML配置
2. 使用一键转换工具生成JSON
3. 在游戏中立即看到配置效果

这个方案比Excel更专业，比直接编辑JSON更安全！
