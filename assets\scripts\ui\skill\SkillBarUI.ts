/**
 * 技能栏UI组件
 * 负责显示和管理玩家的技能栏界面
 */

import { _decorator, Node, Layout, instantiate, Prefab, resources } from 'cc';
import { BaseUIComponent } from '../base/BaseUIComponent';
import { SkillSlot } from './SkillSlot';
import { SkillManager } from '../../managers/SkillManager';
import { ConfigManager } from '../../managers/ConfigManager';
import { EventManager } from '../../managers/EventManager';
import { IPlayerSkill, ISkillData } from '../../config/interfaces/ISkillData';

const { ccclass, property } = _decorator;

/**
 * 技能栏配置接口
 */
export interface ISkillBarConfig {
    /** 技能槽数量 */
    slotCount: number;
    
    /** 技能槽间距 */
    slotSpacing: number;
    
    /** 是否显示快捷键 */
    showHotkeys: boolean;
    
    /** 是否启用拖拽 */
    enableDrag: boolean;
    
    /** 冷却时间显示模式 */
    cooldownDisplayMode: 'text' | 'progress' | 'both';
}

@ccclass('SkillBarUI')
export class SkillBarUI extends BaseUIComponent {
    
    @property({ type: Node, tooltip: '技能槽容器' })
    public skillSlotsContainer: Node = null!;
    
    @property({ type: Prefab, tooltip: '技能槽预制体' })
    public skillSlotPrefab: Prefab = null!;
    
    @property({ tooltip: '技能槽数量' })
    public slotCount: number = 8;
    
    @property({ tooltip: '技能槽间距' })
    public slotSpacing: number = 10;
    
    @property({ tooltip: '是否显示快捷键' })
    public showHotkeys: boolean = true;
    
    @property({ tooltip: '是否启用拖拽' })
    public enableDrag: boolean = true;
    
    @property({ tooltip: '冷却时间显示模式' })
    public cooldownDisplayMode: string = 'both';
    
    // 技能槽组件列表
    private _skillSlots: SkillSlot[] = [];
    
    // 技能管理器引用
    private _skillManager: SkillManager = null!;
    
    // 配置管理器引用
    private _configManager: ConfigManager = null!;
    
    // 当前技能槽配置
    private _currentSkillSlots: (string | null)[] = [];
    
    // 配置
    private _config: ISkillBarConfig = {
        slotCount: 8,
        slotSpacing: 10,
        showHotkeys: true,
        enableDrag: true,
        cooldownDisplayMode: 'both'
    };

    protected onComponentLoad(): void {
        console.log('⚔️ SkillBarUI: 组件加载');
        
        // 获取管理器引用
        this._skillManager = SkillManager.getInstance();
        this._configManager = ConfigManager.getInstance();
        
        // 更新配置
        this.updateConfig();
    }

    protected onComponentEnable(): void {
        console.log('⚔️ SkillBarUI: 组件启用');
        
        // 初始化技能槽
        this.initializeSkillSlots();
        
        // 加载当前技能配置
        this.loadCurrentSkillSlots();
    }

    protected bindEvents(): void {
        const eventManager = EventManager.getInstance();
        
        // 监听技能槽变化
        eventManager.on('skill_slot_changed', this.onSkillSlotChanged, this);
        
        // 监听技能冷却更新
        eventManager.on('skill_cooldowns_update', this.onCooldownsUpdate, this);
        eventManager.on('skill_cooldown_end', this.onCooldownEnd, this);
        
        // 监听技能学习
        eventManager.on('skill_learned', this.onSkillLearned, this);
        
        // 监听技能升级
        eventManager.on('skill_level_up', this.onSkillLevelUp, this);
    }

    protected unbindEvents(): void {
        const eventManager = EventManager.getInstance();
        
        eventManager.off('skill_slot_changed', this.onSkillSlotChanged, this);
        eventManager.off('skill_cooldowns_update', this.onCooldownsUpdate, this);
        eventManager.off('skill_cooldown_end', this.onCooldownEnd, this);
        eventManager.off('skill_learned', this.onSkillLearned, this);
        eventManager.off('skill_level_up', this.onSkillLevelUp, this);
    }

    /**
     * 更新配置
     */
    private updateConfig(): void {
        this._config = {
            slotCount: this.slotCount,
            slotSpacing: this.slotSpacing,
            showHotkeys: this.showHotkeys,
            enableDrag: this.enableDrag,
            cooldownDisplayMode: this.cooldownDisplayMode as any
        };
    }

    /**
     * 初始化技能槽
     */
    private async initializeSkillSlots(): Promise<void> {
        console.log(`⚔️ 初始化 ${this._config.slotCount} 个技能槽`);
        
        try {
            // 清理现有技能槽
            this.clearSkillSlots();
            
            // 设置容器布局
            this.setupContainer();
            
            // 创建技能槽
            for (let i = 0; i < this._config.slotCount; i++) {
                const skillSlot = await this.createSkillSlot(i);
                this._skillSlots.push(skillSlot);
            }
            
            console.log(`✅ 技能槽初始化完成: ${this._skillSlots.length} 个`);
            
        } catch (error) {
            console.error('❌ 技能槽初始化失败:', error);
        }
    }

    /**
     * 设置容器布局
     */
    private setupContainer(): void {
        if (!this.skillSlotsContainer) {
            console.error('❌ 技能槽容器未设置');
            return;
        }
        
        // 设置水平布局
        let layout = this.skillSlotsContainer.getComponent(Layout);
        if (!layout) {
            layout = this.skillSlotsContainer.addComponent(Layout);
        }
        
        layout.type = Layout.Type.HORIZONTAL;
        layout.spacingX = this._config.slotSpacing;
        layout.horizontalDirection = Layout.HorizontalDirection.LEFT_TO_RIGHT;
        layout.verticalDirection = Layout.VerticalDirection.CENTER;
    }

    /**
     * 创建技能槽
     */
    private async createSkillSlot(slotIndex: number): Promise<SkillSlot> {
        // 如果没有预制体，尝试加载默认预制体
        if (!this.skillSlotPrefab) {
            this.skillSlotPrefab = await this.loadSkillSlotPrefab();
        }
        
        // 实例化技能槽节点
        const slotNode = instantiate(this.skillSlotPrefab);
        slotNode.name = `SkillSlot_${slotIndex}`;
        slotNode.setParent(this.skillSlotsContainer);
        
        // 获取技能槽组件
        const skillSlot = slotNode.getComponent(SkillSlot);
        if (!skillSlot) {
            throw new Error(`技能槽预制体缺少SkillSlot组件: ${slotIndex}`);
        }
        
        // 初始化技能槽
        await skillSlot.initialize({
            slotIndex: slotIndex,
            showHotkey: this._config.showHotkeys,
            enableDrag: this._config.enableDrag,
            cooldownDisplayMode: this._config.cooldownDisplayMode,
            hotkey: this.getHotkeyForSlot(slotIndex)
        });
        
        // 绑定点击事件
        skillSlot.onSlotClick = (index: number) => {
            this.onSkillSlotClick(index);
        };
        
        // 绑定拖拽事件
        if (this._config.enableDrag) {
            skillSlot.onSlotDragStart = (index: number) => {
                this.onSkillSlotDragStart(index);
            };
            
            skillSlot.onSlotDragEnd = (fromIndex: number, toIndex: number) => {
                this.onSkillSlotDragEnd(fromIndex, toIndex);
            };
        }
        
        return skillSlot;
    }

    /**
     * 加载技能槽预制体
     */
    private async loadSkillSlotPrefab(): Promise<Prefab> {
        return new Promise((resolve, reject) => {
            resources.load('ui/skill/SkillSlot', Prefab, (error, prefab) => {
                if (error) {
                    reject(new Error(`加载技能槽预制体失败: ${error.message}`));
                    return;
                }
                resolve(prefab);
            });
        });
    }

    /**
     * 获取技能槽快捷键
     */
    private getHotkeyForSlot(slotIndex: number): string {
        const hotkeys = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'];
        return slotIndex < hotkeys.length ? hotkeys[slotIndex] : '';
    }

    /**
     * 清理技能槽
     */
    private clearSkillSlots(): void {
        for (const skillSlot of this._skillSlots) {
            if (skillSlot && skillSlot.node && skillSlot.node.isValid) {
                skillSlot.node.destroy();
            }
        }
        this._skillSlots = [];
    }

    /**
     * 加载当前技能槽配置
     */
    private loadCurrentSkillSlots(): void {
        this._currentSkillSlots = this._skillManager.getSkillSlots();
        this.updateAllSkillSlots();
    }

    /**
     * 更新所有技能槽显示
     */
    private updateAllSkillSlots(): void {
        for (let i = 0; i < this._skillSlots.length; i++) {
            this.updateSkillSlot(i);
        }
    }

    /**
     * 更新指定技能槽
     */
    private updateSkillSlot(slotIndex: number): void {
        if (slotIndex < 0 || slotIndex >= this._skillSlots.length) {
            return;
        }
        
        const skillSlot = this._skillSlots[slotIndex];
        const skillId = this._currentSkillSlots[slotIndex];
        
        if (skillId) {
            // 获取技能数据
            const skillData = this._configManager.getSkillData(skillId);
            const playerSkill = this._skillManager.getPlayerSkill(skillId);
            
            if (skillData && playerSkill) {
                skillSlot.setSkill(skillData, playerSkill);
                
                // 更新冷却时间
                const cooldown = this._skillManager.getSkillCooldown(skillId);
                skillSlot.updateCooldown(cooldown);
            }
        } else {
            // 清空技能槽
            skillSlot.clearSkill();
        }
    }

    // ==================== 事件处理 ====================

    /**
     * 技能槽变化事件处理
     */
    private onSkillSlotChanged(eventData: any): void {
        this._currentSkillSlots = eventData.skillSlots;
        this.updateSkillSlot(eventData.slotIndex);
    }

    /**
     * 冷却时间更新事件处理
     */
    private onCooldownsUpdate(cooldowns: { [skillId: string]: number }): void {
        for (let i = 0; i < this._skillSlots.length; i++) {
            const skillId = this._currentSkillSlots[i];
            if (skillId && cooldowns[skillId] !== undefined) {
                this._skillSlots[i].updateCooldown(cooldowns[skillId]);
            }
        }
    }

    /**
     * 冷却结束事件处理
     */
    private onCooldownEnd(eventData: any): void {
        const skillId = eventData.skillId;
        const slotIndex = this._currentSkillSlots.indexOf(skillId);
        
        if (slotIndex !== -1) {
            this._skillSlots[slotIndex].updateCooldown(0);
        }
    }

    /**
     * 技能学习事件处理
     */
    private onSkillLearned(eventData: any): void {
        // 刷新所有技能槽显示
        this.updateAllSkillSlots();
    }

    /**
     * 技能升级事件处理
     */
    private onSkillLevelUp(eventData: any): void {
        const skillId = eventData.skillId;
        const slotIndex = this._currentSkillSlots.indexOf(skillId);
        
        if (slotIndex !== -1) {
            this.updateSkillSlot(slotIndex);
        }
    }

    /**
     * 技能槽点击事件处理
     */
    private onSkillSlotClick(slotIndex: number): void {
        const skillId = this._currentSkillSlots[slotIndex];
        
        if (skillId) {
            console.log(`⚔️ 点击技能槽 ${slotIndex}: ${skillId}`);
            
            // 使用技能
            this._skillManager.useSkill(skillId).then(result => {
                if (!result.success) {
                    console.warn(`⚠️ 技能使用失败: ${result.error}`);
                    // TODO: 显示错误提示
                }
            });
        } else {
            console.log(`⚔️ 点击空技能槽 ${slotIndex}`);
            // TODO: 打开技能选择界面
            this.openSkillSelection(slotIndex);
        }
    }

    /**
     * 技能槽拖拽开始事件处理
     */
    private onSkillSlotDragStart(slotIndex: number): void {
        console.log(`⚔️ 开始拖拽技能槽 ${slotIndex}`);
        // TODO: 实现拖拽逻辑
    }

    /**
     * 技能槽拖拽结束事件处理
     */
    private onSkillSlotDragEnd(fromIndex: number, toIndex: number): void {
        console.log(`⚔️ 拖拽技能槽: ${fromIndex} → ${toIndex}`);
        
        // 交换技能槽
        const fromSkillId = this._currentSkillSlots[fromIndex];
        const toSkillId = this._currentSkillSlots[toIndex];
        
        this._skillManager.setSkillSlot(fromIndex, toSkillId);
        this._skillManager.setSkillSlot(toIndex, fromSkillId);
    }

    // ==================== 公共API ====================

    /**
     * 设置玩家技能到技能槽
     */
    public setPlayerSkills(skills: IPlayerSkill[]): void {
        console.log(`⚔️ 设置玩家技能: ${skills.length} 个`);
        
        // 更新技能槽显示
        this.updateAllSkillSlots();
    }

    /**
     * 打开技能选择界面
     */
    public openSkillSelection(slotIndex: number): void {
        console.log(`⚔️ 打开技能选择界面: 槽位 ${slotIndex}`);
        
        // 发送打开技能选择事件
        EventManager.getInstance().emit('open_skill_selection', {
            slotIndex: slotIndex,
            currentSkillId: this._currentSkillSlots[slotIndex]
        });
    }

    /**
     * 更新冷却时间显示
     */
    public updateCooldowns(): void {
        const cooldowns = this._skillManager.getAllCooldowns();
        this.onCooldownsUpdate(cooldowns);
    }

    /**
     * 获取技能栏配置
     */
    public getConfig(): ISkillBarConfig {
        return { ...this._config };
    }

    /**
     * 更新技能栏配置
     */
    public updateSkillBarConfig(config: Partial<ISkillBarConfig>): void {
        this._config = { ...this._config, ...config };
        
        // 重新初始化技能槽
        this.initializeSkillSlots();
    }
}
