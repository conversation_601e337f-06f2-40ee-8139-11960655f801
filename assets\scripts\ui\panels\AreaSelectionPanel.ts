import { _decorator, Component, Node, Button, Label } from 'cc';
import { BaseUIPanel } from '../base/BaseUIPanel';
import { EventManager } from '../../managers/EventManager';

const { ccclass, property } = _decorator;

/**
 * 区域选择面板
 * 管理左侧的区域选择按钮
 */
@ccclass('AreaSelectionPanel')
export class AreaSelectionPanel extends BaseUIPanel {
    
    @property(Node)
    battleButton: Node = null!;
    
    @property(Node)
    mengXingButton: Node = null!;
    
    @property(Node)
    goblinForestButton: Node = null!;
    
    @property(Node)
    darkCaveButton: Node = null!;
    
    @property(Node)
    bossArenaButton: Node = null!;
    
    @property(Node)
    trainingGroundButton: Node = null!;

    // 当前选中的区域
    private _currentSelectedArea: string = '';

    protected onLoad(): void {
        super.onLoad();
        this.initializeButtons();
    }

    /**
     * 初始化按钮事件
     */
    private initializeButtons(): void {
        this.setupButtonEvent(this.battleButton, 'battle', '战斗');
        this.setupButtonEvent(this.mengXingButton, 'mengxing', '蒙性');
        this.setupButtonEvent(this.goblinForestButton, 'goblin_forest', '哥布林森林');
        this.setupButtonEvent(this.darkCaveButton, 'dark_cave', '黑暗洞穴');
        this.setupButtonEvent(this.bossArenaButton, 'boss_arena', '首领竞技场');
        this.setupButtonEvent(this.trainingGroundButton, 'training_ground', '训练场');
    }

    /**
     * 设置按钮事件
     */
    private setupButtonEvent(buttonNode: Node, areaId: string, areaName: string): void {
        if (!buttonNode) return;
        
        const button = buttonNode.getComponent(Button);
        if (button) {
            button.node.on(Button.EventType.CLICK, () => {
                this.onAreaSelected(areaId, areaName);
            }, this);
        }
    }

    /**
     * 区域选择事件处理
     */
    private onAreaSelected(areaId: string, areaName: string): void {
        console.log(`选择区域: ${areaName} (${areaId})`);
        
        // 更新当前选中区域
        this._currentSelectedArea = areaId;
        
        // 更新按钮状态
        this.updateButtonStates(areaId);
        
        // 发送区域选择事件
        EventManager.getInstance().emit('area-selected', {
            areaId: areaId,
            areaName: areaName
        });
    }

    /**
     * 更新按钮状态
     */
    private updateButtonStates(selectedAreaId: string): void {
        const buttons = [
            { node: this.battleButton, id: 'battle' },
            { node: this.mengXingButton, id: 'mengxing' },
            { node: this.goblinForestButton, id: 'goblin_forest' },
            { node: this.darkCaveButton, id: 'dark_cave' },
            { node: this.bossArenaButton, id: 'boss_arena' },
            { node: this.trainingGroundButton, id: 'training_ground' }
        ];

        buttons.forEach(buttonInfo => {
            if (buttonInfo.node) {
                const button = buttonInfo.node.getComponent(Button);
                if (button) {
                    // 这里可以添加选中状态的视觉效果
                    // 例如改变颜色、缩放等
                    if (buttonInfo.id === selectedAreaId) {
                        buttonInfo.node.setScale(1.1, 1.1, 1.0);
                    } else {
                        buttonInfo.node.setScale(1.0, 1.0, 1.0);
                    }
                }
            }
        });
    }

    /**
     * 获取当前选中的区域
     */
    public getCurrentSelectedArea(): string {
        return this._currentSelectedArea;
    }

    /**
     * 程序化选择区域
     */
    public selectArea(areaId: string): void {
        const areaNames: { [key: string]: string } = {
            'battle': '战斗',
            'mengxing': '蒙性',
            'goblin_forest': '哥布林森林',
            'dark_cave': '黑暗洞穴',
            'boss_arena': '首领竞技场',
            'training_ground': '训练场'
        };

        const areaName = areaNames[areaId];
        if (areaName) {
            this.onAreaSelected(areaId, areaName);
        }
    }

    protected onShow(): void {
        super.onShow();
        // 默认选择第一个区域
        if (!this._currentSelectedArea) {
            this.selectArea('battle');
        }
    }

    protected onHide(): void {
        super.onHide();
    }

    // 实现BaseUIPanel的抽象方法
    protected onPanelLoad(): void {
        // 面板加载时的初始化逻辑
        console.log('AreaSelectionPanel: 面板加载');
    }

    protected onPanelEnable(): void {
        // 面板启用时的逻辑
        console.log('AreaSelectionPanel: 面板启用');
    }

    protected onPanelDisable(): void {
        // 面板禁用时的逻辑
        console.log('AreaSelectionPanel: 面板禁用');
    }

    protected onPanelDestroy(): void {
        // 面板销毁时的清理逻辑
        console.log('AreaSelectionPanel: 面板销毁');
    }

    protected onPanelDestroy(): void {
        // 面板销毁时的清理逻辑
        console.log('AreaSelectionPanel: 面板销毁');

        // 清理事件监听
        const buttons = [
            this.battleButton, this.mengXingButton, this.goblinForestButton,
            this.darkCaveButton, this.bossArenaButton, this.trainingGroundButton
        ];

        buttons.forEach(buttonNode => {
            if (buttonNode) {
                const button = buttonNode.getComponent(Button);
                if (button) {
                    button.node.off(Button.EventType.CLICK);
                }
            }
        });
    }
}
