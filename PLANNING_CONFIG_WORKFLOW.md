# Copyright (c) 2025 "放置". All rights reserved.

# 策划配置工作流程指南

## 🎯 问题解决确认

**老板的担心**：配置表存放在根目录是否会影响项目构建打包？

**解决方案**：✅ **绝对安全！**

我们已经实施了多重安全保障：

### 🔒 安全保障措施

1. **独立工具目录**：配置表放在 `tools/planning-configs/` 目录
2. **构建排除**：在 `tsconfig.json` 中明确排除 `tools` 目录
3. **自动检查**：提供构建安全检查工具
4. **验证通过**：✅ 构建安全检查通过！策划配置不会被意外打包

## 📁 安全的目录结构

```
项目根目录/
├── assets/                    # 🎮 游戏资源（会被打包）
├── backend/                   # 🔒 后端代码（不打包）
├── shared/                    # 🔗 共享类型（编译时处理）
├── tools/                     # 🛠️ 工具区域（绝对不打包）
│   └── planning-configs/      # 🎯 策划配置工作区
│       ├── excel-tables/      # 📊 Excel配置表
│       │   ├── scenes.xlsx    # 场景表
│       │   ├── monsters.xlsx  # 怪物表
│       │   ├── items.xlsx     # 物品表
│       │   ├── skills.xlsx    # 技能表
│       │   └── balance.xlsx   # 数值平衡表
│       ├── conversion-tools/  # 🔧 转换工具
│       │   ├── excel-to-json.js
│       │   ├── config-validator.js
│       │   └── convert.bat    # 一键转换
│       ├── templates/         # 📋 Excel模板
│       └── docs/              # 📖 说明文档
├── build/                     # 📦 构建输出（临时）
└── temp/                      # 🗂️ 临时文件
```

## 🚀 策划工作流程

### 第一步：配置编辑
策划同事在 `tools/planning-configs/excel-tables/` 中编辑Excel表格

### 第二步：一键转换
运行 `tools/planning-configs/conversion-tools/convert.bat`

### 第三步：自动部署
配置自动转换为JSON并部署到 `assets/configs/`

### 第四步：游戏加载
游戏从 `assets/configs/` 加载最终配置

## 📊 场景表配置示例

### Excel表格格式（scenes.xlsx）
| 场景ID | 场景名称 | 描述 | 类型 | 解锁等级 | 前置场景 | 背景音乐 | 环境效果 | 基础经验 | 基础金币 |
|--------|----------|------|------|----------|----------|----------|----------|----------|----------|
| fishpond | 鱼塘 | 宁静的鱼塘，适合钓鱼放松 | fishing | 1 | | peaceful_pond | sunny | 10 | 5 |
| farm | 农场 | 肥沃的农场，适合种植作物 | farming | 5 | fishpond | farm_life | sunny | 15 | 8 |
| forest | 森林 | 茂密的森林，适合伐木探险 | logging | 10 | farm | mysterious_forest | cloudy | 18 | 12 |

### 转换后的JSON格式
```json
{
  "version": "1.0.0",
  "config_type": "scenes",
  "last_updated": "2025-01-10T00:00:00Z",
  "scenes": {
    "fishpond": {
      "locationId": "fishpond",
      "name": "鱼塘",
      "description": "宁静的鱼塘，适合钓鱼放松",
      "type": "fishing",
      "unlockConditions": {
        "playerLevel": 1,
        "requiredAreas": []
      },
      "environment": {
        "backgroundMusic": "peaceful_pond",
        "weather": "sunny"
      },
      "rewards": {
        "baseExp": 10,
        "baseGold": 5
      }
    }
  }
}
```

## 🔧 自动化工具

### 转换工具（convert.bat）
```batch
@echo off
echo 🔧 开始转换策划配置...

node excel-to-json.js scenes.xlsx ../../../assets/configs/areas/
node excel-to-json.js monsters.xlsx ../../../assets/configs/monsters/
node excel-to-json.js items.xlsx ../../../assets/configs/items/

echo ✅ 配置转换完成！
echo 📍 配置文件已更新到 assets/configs/ 目录
pause
```

### 安全检查工具
```bash
# 运行构建安全检查
node tools/build-safety-check.js

# 输出示例：
# 🔒 开始构建安全检查...
# 📦 检查构建目录: build/web-desktop
# ✅ 构建目录安全
# 🎉 构建安全检查通过！策划配置不会被意外打包。
```

## ✅ 安全验证结果

我们已经完成了完整的安全验证：

```
🔒 开始构建安全检查...
📦 检查构建目录: build/web-desktop
✅ 构建目录安全
📋 检查配置文件...
✅ tsconfig.json 正确排除了 tools 目录
🎉 构建安全检查通过！策划配置不会被意外打包。
```

## 🎯 给策划同事的操作指南

### 日常工作流程
1. **打开Excel表格**：在 `tools/planning-configs/excel-tables/` 中编辑
2. **修改配置数值**：按照模板格式填写数据
3. **一键转换**：双击运行 `convert.bat`
4. **测试验证**：在游戏中测试配置效果
5. **提交更改**：通过Git提交配置变更

### 注意事项
- ✅ **安全保障**：配置文件绝对不会被打包到游戏中
- ✅ **版本控制**：Excel文件可以正常进行版本管理
- ✅ **协作友好**：多人可以同时编辑不同的配置表
- ✅ **实时生效**：转换后的配置立即在游戏中生效

## 🚨 重要提醒

### 对策划同事
- 📊 **只需关心Excel表格**：在 `tools/planning-configs/excel-tables/` 中工作
- 🔧 **一键转换**：使用提供的转换工具
- 🚫 **不要直接修改**：不要直接编辑 `assets/configs/` 中的JSON文件

### 对程序员
- 🔒 **构建安全**：tools目录已被排除，不会影响打包
- 🔄 **自动化流程**：配置转换和验证已自动化
- 📋 **类型安全**：配置加载使用TypeScript类型定义

## 🎉 总结

老板，您的担心已经完全解决！

✅ **配置表存放绝对安全**：
- 放在 `tools/planning-configs/` 目录
- 构建时自动排除，不会被打包
- 通过了完整的安全验证

✅ **策划工作流程完善**：
- 熟悉的Excel编辑环境
- 一键转换和部署
- 完整的模板和文档

✅ **技术保障到位**：
- 多重安全检查机制
- 自动化工具支持
- 类型安全的配置系统

策划同事们可以放心地在 `tools/planning-configs/excel-tables/` 中编辑场景表等配置，完全不会影响项目构建打包！
