# Copyright (c) 2025 "放置". All rights reserved.

# 前端配置表结构说明

## 目录结构
```
assets/configs/
├── ui/                 # UI配置
│   ├── layouts/        # 界面布局配置
│   ├── animations/     # 动画配置
│   ├── effects/        # 特效配置
│   └── themes/         # 主题配置
├── resources/          # 资源配置
│   ├── images/         # 图片资源配置
│   ├── audio/          # 音频资源配置
│   └── fonts/          # 字体资源配置
├── localization/       # 本地化配置
│   ├── zh-CN/          # 中文配置
│   ├── en-US/          # 英文配置
│   └── common/         # 通用文本配置
└── shared/             # 前后端共享配置（本地缓存）
    ├── base/           # 基础配置
    ├── version/        # 版本控制
    └── unlock/         # 解锁条件
```

## 配置表设计原则
1. **职责分离**：前端只负责展示相关配置
2. **资源管理**：统一管理UI资源路径和多媒体资源
3. **本地化支持**：完整的多语言配置体系
4. **版本控制**：支持配置热更新和版本管理
5. **缓存优化**：利用Asset Bundle进行资源分包

## 使用说明
- 所有前端配置表都应该放在对应的子目录中
- 配置文件统一使用JSON格式
- 文件命名采用小写+下划线格式
- 每个配置文件都应包含版本号字段
