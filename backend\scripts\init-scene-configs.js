/**
 * 初始化地点配置数据到云数据库
 * 运行方式: node backend/scripts/init-scene-configs.js
 */

const fs = require('fs');
const path = require('path');

// 模拟云数据库操作（实际项目中需要连接真实的云数据库）
class MockCloudDatabase {
    constructor() {
        this.collections = new Map();
    }

    collection(name) {
        if (!this.collections.has(name)) {
            this.collections.set(name, []);
        }
        return {
            add: (data) => {
                const collection = this.collections.get(name);
                const id = `${name}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
                const record = { _id: id, ...data };
                collection.push(record);
                console.log(`✅ 添加记录到 ${name}:`, id);
                return { id };
            },
            get: () => {
                const collection = this.collections.get(name);
                return { data: collection };
            },
            where: (condition) => {
                const collection = this.collections.get(name);
                const filtered = collection.filter(item => {
                    for (const [key, value] of Object.entries(condition)) {
                        if (item[key] !== value) return false;
                    }
                    return true;
                });
                return {
                    get: () => ({ data: filtered }),
                    orderBy: (field, order) => ({
                        limit: (count) => ({
                            get: () => {
                                const sorted = filtered.sort((a, b) => {
                                    if (order === 'desc') {
                                        return b[field] > a[field] ? 1 : -1;
                                    }
                                    return a[field] > b[field] ? 1 : -1;
                                });
                                return { data: sorted.slice(0, count) };
                            }
                        })
                    })
                };
            }
        };
    }

    // 导出数据（用于调试）
    exportData() {
        const data = {};
        for (const [name, collection] of this.collections) {
            data[name] = collection;
        }
        return data;
    }
}

async function initLocationConfigs() {
    console.log('🚀 开始初始化地点配置数据...');

    // 创建模拟数据库实例
    const db = new MockCloudDatabase();

    try {
        // 读取地点配置文件
        const locationFiles = [
            'forest-location-config.json',
            'desert-location-config.json',
            'mountain-location-config.json'
        ];

        console.log(`📋 准备读取 ${locationFiles.length} 个地点配置文件`);

        // 初始化地点配置集合
        const locationConfigCollection = db.collection('location_configs');

        // 为每个地点创建配置记录
        for (const fileName of locationFiles) {
            const configPath = path.join(__dirname, '../../assets/bundles/locations', fileName);

            if (!fs.existsSync(configPath)) {
                console.warn(`⚠️ 配置文件不存在: ${fileName}`);
                continue;
            }

            const configData = JSON.parse(fs.readFileSync(configPath, 'utf8'));

            const configRecord = {
                locationId: configData.locationId,
                version: 'v1.0.0',
                data: configData,
                description: `${configData.name} 地点配置`,
                createdAt: new Date(),
                updatedAt: new Date()
            };

            await locationConfigCollection.add(configRecord);
            console.log(`✅ 地点配置已添加: ${configData.locationId} (${configData.name})`);
        }

        // 创建一些额外的版本用于测试
        console.log('📝 创建测试版本配置...');
        
        // 为森林场景创建一个更新版本
        const forestV2 = {
            ...configData.scenes.Forest,
            resources: {
                ...configData.scenes.Forest.resources,
                monsters: [
                    ...configData.scenes.Forest.resources.monsters,
                    {
                        id: "forest_dragon",
                        name: "森林龙",
                        level: 8,
                        spawnRate: 0.02,
                        maxCount: 1
                    }
                ]
            },
            rewards: {
                ...configData.scenes.Forest.rewards,
                baseExp: 15,
                baseGold: 8
            }
        };

        await sceneConfigCollection.add({
            sceneId: 'Forest',
            version: 'v1.1.0',
            data: forestV2,
            description: '森林场景配置 - 增加森林龙',
            createdAt: new Date(),
            updatedAt: new Date()
        });

        console.log('✅ 森林场景 v1.1.0 配置已添加');

        // 验证数据
        console.log('\n🔍 验证配置数据...');
        const allConfigs = sceneConfigCollection.get();
        console.log(`总共创建了 ${allConfigs.data.length} 个配置记录`);

        // 按场景分组统计
        const sceneStats = {};
        allConfigs.data.forEach(config => {
            if (!sceneStats[config.sceneId]) {
                sceneStats[config.sceneId] = 0;
            }
            sceneStats[config.sceneId]++;
        });

        console.log('📊 场景配置统计:');
        for (const [sceneId, count] of Object.entries(sceneStats)) {
            console.log(`  ${sceneId}: ${count} 个版本`);
        }

        // 导出数据到文件（用于调试）
        const exportPath = path.join(__dirname, '../data/scene_configs_export.json');
        fs.writeFileSync(exportPath, JSON.stringify(db.exportData(), null, 2));
        console.log(`📁 数据已导出到: ${exportPath}`);

        console.log('\n✅ 场景配置数据初始化完成！');

        // 提供API测试示例
        console.log('\n🧪 API测试示例:');
        console.log('获取森林场景最新配置:');
        console.log('GET /api/v1/scenes/config/Forest?version=latest');
        console.log('\n获取特定版本配置:');
        console.log('GET /api/v1/scenes/config/Forest?version=v1.1.0');
        console.log('\n批量获取配置:');
        console.log('POST /api/v1/scenes/config/batch');
        console.log('Body: { "sceneIds": ["Forest", "Desert", "Mountain"], "version": "latest" }');

    } catch (error) {
        console.error('❌ 初始化场景配置失败:', error);
        process.exit(1);
    }
}

// 运行初始化
if (require.main === module) {
    initLocationConfigs().catch(console.error);
}

module.exports = { initLocationConfigs };
