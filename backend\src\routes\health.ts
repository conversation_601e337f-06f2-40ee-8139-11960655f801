import express = require('express');
import { Logger } from '../utils/logger';
import { CacheManager } from '../utils/cache';
import { CacheMonitor } from '../utils/cacheMonitor';

const router = express.Router();

/**
 * 健康检查路由模块
 * 
 * 功能：
 * - 基础健康检查
 * - 详细系统状态
 * - 依赖服务检查
 * - 性能指标
 */

/**
 * @swagger
 * /health:
 *   get:
 *     tags: [Health]
 *     summary: 基础健康检查
 *     description: 检查服务基础状态
 *     responses:
 *       200:
 *         description: 服务正常
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "healthy"
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                 uptime:
 *                   type: number
 *                   description: 服务运行时间（秒）
 *                 version:
 *                   type: string
 *                   example: "1.0.0"
 *       503:
 *         description: 服务不可用
 */
router.get('/', async (req, res) => {
  try {
    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env['APP_VERSION'] || '1.0.0',
      environment: process.env['NODE_ENV'] || 'development',
      pid: process.pid,
    };

    Logger.debug('健康检查请求', healthStatus);
    res.json(healthStatus);
  } catch (error) {
    Logger.error('健康检查失败', error);
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: '服务不可用',
    });
  }
});

/**
 * @swagger
 * /health/detailed:
 *   get:
 *     tags: [Health]
 *     summary: 详细健康检查
 *     description: 检查服务和依赖的详细状态
 *     responses:
 *       200:
 *         description: 详细健康状态
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                 timestamp:
 *                   type: string
 *                 uptime:
 *                   type: number
 *                 memory:
 *                   type: object
 *                 dependencies:
 *                   type: object
 *                 performance:
 *                   type: object
 */
router.get('/detailed', async (req, res) => {
  try {
    const startTime = Date.now();
    
    // 检查依赖服务
    const dependencies = await checkDependencies();
    
    // 获取内存使用情况
    const memoryUsage = process.memoryUsage();
    
    // 获取CPU使用情况
    const cpuUsage = process.cpuUsage();
    
    // 计算检查耗时
    const checkDuration = Date.now() - startTime;
    
    const detailedStatus = {
      status: dependencies.allHealthy ? 'healthy' : 'degraded',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env['APP_VERSION'] || '1.0.0',
      environment: process.env['NODE_ENV'] || 'development',
      pid: process.pid,
      memory: {
        rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
        external: Math.round(memoryUsage.external / 1024 / 1024), // MB
        arrayBuffers: Math.round(memoryUsage.arrayBuffers / 1024 / 1024), // MB
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
      },
      dependencies: dependencies.services,
      performance: {
        checkDuration,
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
      },
    };

    const statusCode = dependencies.allHealthy ? 200 : 503;
    Logger.debug('详细健康检查完成', { 
      status: detailedStatus.status, 
      duration: checkDuration,
      dependencies: dependencies.services,
    });
    
    res.status(statusCode).json(detailedStatus);
  } catch (error) {
    Logger.error('详细健康检查失败', error);
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: '详细健康检查失败',
    });
  }
});

/**
 * @swagger
 * /health/readiness:
 *   get:
 *     tags: [Health]
 *     summary: 就绪检查
 *     description: 检查服务是否准备好接收请求
 *     responses:
 *       200:
 *         description: 服务就绪
 *       503:
 *         description: 服务未就绪
 */
router.get('/readiness', async (req, res) => {
  try {
    // 检查关键依赖
    const cacheManager = CacheManager.getInstance();
    const cacheHealthy = await cacheManager.healthCheck();
    
    const isReady = cacheHealthy;
    
    if (isReady) {
      res.json({
        status: 'ready',
        timestamp: new Date().toISOString(),
        checks: {
          cache: cacheHealthy,
        },
      });
    } else {
      res.status(503).json({
        status: 'not_ready',
        timestamp: new Date().toISOString(),
        checks: {
          cache: cacheHealthy,
        },
      });
    }
  } catch (error) {
    Logger.error('就绪检查失败', error);
    res.status(503).json({
      status: 'not_ready',
      timestamp: new Date().toISOString(),
      error: '就绪检查失败',
    });
  }
});

/**
 * @swagger
 * /health/liveness:
 *   get:
 *     tags: [Health]
 *     summary: 存活检查
 *     description: 检查服务是否存活
 *     responses:
 *       200:
 *         description: 服务存活
 *       503:
 *         description: 服务不存活
 */
router.get('/liveness', async (req, res) => {
  try {
    // 简单的存活检查
    const isAlive = process.uptime() > 0;
    
    if (isAlive) {
      res.json({
        status: 'alive',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
      });
    } else {
      res.status(503).json({
        status: 'dead',
        timestamp: new Date().toISOString(),
      });
    }
  } catch (error) {
    Logger.error('存活检查失败', error);
    res.status(503).json({
      status: 'dead',
      timestamp: new Date().toISOString(),
      error: '存活检查失败',
    });
  }
});

/**
 * 检查依赖服务
 */
async function checkDependencies(): Promise<{ allHealthy: boolean; services: Record<string, any> }> {
  const services: Record<string, any> = {};
  let allHealthy = true;

  try {
    // 检查缓存服务
    const cacheManager = CacheManager.getInstance();
    const cacheHealthy = await cacheManager.healthCheck();
    services.cache = {
      status: cacheHealthy ? 'healthy' : 'unhealthy',
      responseTime: 0, // 可以添加响应时间测量
    };
    if (!cacheHealthy) allHealthy = false;

    // 检查缓存监控
    const cacheMonitor = CacheMonitor.getInstance();
    const monitorHealthy = await cacheMonitor.healthCheck();
    services.cacheMonitor = {
      status: monitorHealthy ? 'healthy' : 'unhealthy',
      responseTime: 0,
    };
    if (!monitorHealthy) allHealthy = false;

    // 检查日志系统
    const loggerHealthy = await Logger.healthCheck();
    services.logger = {
      status: loggerHealthy ? 'healthy' : 'unhealthy',
      responseTime: 0,
    };
    if (!loggerHealthy) allHealthy = false;

  } catch (error) {
    Logger.error('依赖检查失败', error);
    allHealthy = false;
  }

  return { allHealthy, services };
}

export default router;
