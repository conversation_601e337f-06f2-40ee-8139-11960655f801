{"// Copyright (c) 2025 \"放置\". All rights reserved.": "", "version": "1.0.0", "config_type": "feature_unlock", "last_updated": "2025-01-10T00:00:00Z", "checksum": "pqr678stu901", "unlock_conditions": {"equipment_system": {"type": "level", "requirement": 3, "description": "角色达到3级解锁装备系统"}, "skill_system": {"type": "level", "requirement": 5, "description": "角色达到5级解锁技能系统"}, "adventure_mode": {"type": "level", "requirement": 10, "description": "角色达到10级解锁冒险模式"}, "guild_system": {"type": "level", "requirement": 15, "description": "角色达到15级解锁公会系统"}, "pvp_arena": {"type": "level", "requirement": 20, "description": "角色达到20级解锁竞技场"}, "auto_battle": {"type": "quest", "requirement": "complete_tutorial", "description": "完成新手教程解锁自动战斗"}, "offline_rewards": {"type": "time_played", "requirement": 3600, "description": "游戏时间达到1小时解锁离线奖励"}, "daily_quests": {"type": "level", "requirement": 8, "description": "角色达到8级解锁每日任务"}, "achievement_system": {"type": "level", "requirement": 12, "description": "角色达到12级解锁成就系统"}, "shop_system": {"type": "level", "requirement": 6, "description": "角色达到6级解锁商店系统"}}, "content_unlock": {"locations": {"forest": {"type": "level", "requirement": 1, "description": "初始区域"}, "cave": {"type": "level", "requirement": 15, "description": "角色达到15级解锁洞穴"}, "mountain": {"type": "level", "requirement": 25, "description": "角色达到25级解锁山脉"}, "desert": {"type": "level", "requirement": 35, "description": "角色达到35级解锁沙漠"}, "dungeon": {"type": "quest", "requirement": "clear_mountain_boss", "description": "击败山脉BOSS解锁地下城"}}, "equipment_tiers": {"tier_1": {"type": "level", "requirement": 1, "description": "初始装备等级"}, "tier_2": {"type": "level", "requirement": 10, "description": "角色达到10级解锁二阶装备"}, "tier_3": {"type": "level", "requirement": 20, "description": "角色达到20级解锁三阶装备"}, "tier_4": {"type": "level", "requirement": 35, "description": "角色达到35级解锁四阶装备"}, "tier_5": {"type": "level", "requirement": 50, "description": "角色达到50级解锁五阶装备"}}}, "progression_gates": {"tutorial_complete": {"required_steps": ["create_character", "first_battle", "level_up", "equip_item", "learn_skill"]}, "beginner_complete": {"requirements": {"level": 10, "quests_completed": 5, "items_equipped": 3}}, "intermediate_complete": {"requirements": {"level": 25, "locations_cleared": 3, "skills_learned": 5}}}}