/**
 * Copyright (c) 2025 "放置". All rights reserved.
 * 地图面板控制器 - 管理MapPanel中的地图按钮点击事件（重构版本）
 */

import { _decorator, Component, Node, Button, find, Label, Sprite, Color } from 'cc';
import { LocationConfigManager } from '../../managers/LocationConfigManager';
import { EventManager } from '../../managers/EventManager';

const { ccclass, property } = _decorator;

/**
 * 地图按钮数据接口
 */
export interface IMapButtonData {
    buttonName: string;    // 按钮节点名称（如：map-001）
    locationId: string;    // 对应的地点ID（如：fishpond）
    displayName: string;   // 显示名称（如：鱼塘）
    isUnlocked: boolean;   // 是否已解锁
}

/**
 * 地图面板控制器
 * 负责处理MapPanel中地图按钮的点击事件，触发场景切换
 */
@ccclass('MapPanelController')
export class MapPanelController extends Component {

    @property({ tooltip: '是否启用调试日志' })
    public enableDebugLog: boolean = true;

    /**
     * 地图按钮配置
     */
    private _mapButtonConfigs: IMapButtonData[] = [
        { buttonName: 'map', locationId: 'fishpond', displayName: '鱼塘', isUnlocked: true },
        { buttonName: 'map-001', locationId: 'farm', displayName: '农场', isUnlocked: true },
        { buttonName: 'map-002', locationId: 'forest', displayName: '森林', isUnlocked: true }
    ];

    /**
     * 地点配置管理器
     */
    private _locationConfigManager: LocationConfigManager | null = null;

    /**
     * 当前选中的地点ID
     */
    private _currentLocationId: string = 'fishpond';

    /**
     * 协议系统是否启用（预留）
     */
    private _protocolEnabled: boolean = false;

    protected onLoad(): void {
        console.log('🗺️ MapPanelController: 地图面板控制器开始初始化');

        try {
            // 获取LocationConfigManager实例
            this._locationConfigManager = LocationConfigManager.getInstance();
            console.log('🗺️ LocationConfigManager实例获取:', this._locationConfigManager ? '成功' : '失败');

            // 延迟初始化地图按钮，确保节点结构完全加载
            (this as any).scheduleOnce(() => {
                this.initializeMapButtons();
            }, 1.0); // 增加延迟时间到1秒

        } catch (error) {
            console.error('❌ MapPanelController: onLoad初始化失败', error);
        }
    }

    protected start(): void {
        // 默认加载第一个解锁的地点
        this.scheduleOnce(() => {
            this.switchToLocation(this._currentLocationId);
        }, 0.5);
    }





    /**
     * 更新当前地点显示
     */
    private updateCurrentLocationDisplay(locationId: string): void {
        // 更新所有按钮的选中状态
        this._mapButtonConfigs.forEach(config => {
            const buttonNode = this.findButtonNode(config.buttonName);
            if (buttonNode) {
                const isSelected = config.locationId === locationId;
                this.updateButtonSelectedState(buttonNode, isSelected);
            }
        });
    }

    /**
     * 更新按钮选中状态
     */
    private updateButtonSelectedState(buttonNode: Node, isSelected: boolean): void {
        // 可以在这里添加选中状态的视觉效果
        // 比如改变按钮颜色、添加边框等
        const sprite = buttonNode.getComponent(Sprite);
        if (sprite) {
            sprite.color = isSelected ? new Color(255, 255, 0, 255) : new Color(255, 255, 255, 255);
        }
    }

    /**
     * 播放解锁动画
     */
    private playUnlockAnimation(locationId: string): void {
        console.log(`🎬 播放解锁动画: ${locationId}`);
        // 在这里添加解锁动画逻辑
    }

    /**
     * 初始化地图按钮
     */
    private initializeMapButtons(): void {
        try {
            console.log('🗺️ MapPanelController: 开始初始化地图按钮');
            const node = (this as any).node as Node; // 类型断言解决IDE问题
            console.log('🗺️ 当前节点名称:', node.name);
            console.log('🗺️ 当前节点子节点数量:', node.children.length);

            // 打印所有子节点名称
            node.children.forEach((child, index) => {
                console.log(`🗺️ 子节点${index}: ${child.name}`);
            });

            // 查找Maplist节点
            const maplistNode = node.getChildByName('Maplist');
            if (!maplistNode) {
                console.error('❌ MapPanelController: 未找到Maplist节点');
                console.error('❌ 可用的子节点:', node.children.map(child => child.name));

                // 尝试查找其他可能的节点名称
                const possibleNames = ['MapList', 'maplist', 'List', 'ButtonList'];
                for (const name of possibleNames) {
                    const testNode = node.getChildByName(name);
                    if (testNode) {
                        console.log(`🔍 找到可能的节点: ${name}`);
                    }
                }
                return;
            }

            console.log('✅ 找到Maplist节点，子节点数量:', maplistNode.children.length);
            maplistNode.children.forEach((child, index) => {
                console.log(`🗺️ Maplist子节点${index}: ${child.name}`);
            });

            // 尝试查找list节点，如果没有则使用Maplist的第一个子节点
            let listNode = maplistNode.getChildByName('list');
            if (!listNode && maplistNode.children.length > 0) {
                console.log('🔄 未找到list节点，使用第一个子节点:', maplistNode.children[0].name);
                listNode = maplistNode.children[0];
            }

            if (!listNode) {
                console.error('❌ MapPanelController: 未找到list节点或任何子节点');
                console.error('❌ Maplist可用的子节点:', maplistNode.children.map(child => child.name));

                // 尝试直接使用Maplist作为按钮容器
                console.log('🔄 尝试直接使用Maplist作为按钮容器');
                this.initializeButtonsFromContainer(maplistNode);
                return;
            }

            console.log('✅ 找到list节点，子节点数量:', listNode.children.length);
            listNode.children.forEach((child, index) => {
                console.log(`🗺️ list子节点${index}: ${child.name}`);
            });

            // 初始化按钮
            this.initializeButtonsFromContainer(listNode);

        } catch (error) {
            console.error('❌ MapPanelController: 初始化地图按钮失败', error);
        }
    }

    /**
     * 从指定容器初始化按钮
     */
    private initializeButtonsFromContainer(containerNode: Node): void {
        try {
            console.log('🔧 开始从容器初始化按钮，容器名称:', containerNode.name);

            // 为每个地图按钮添加点击事件
            for (const buttonConfig of this._mapButtonConfigs) {
                const buttonNode = containerNode.getChildByName(buttonConfig.buttonName);
                if (buttonNode) {
                    const buttonComponent = buttonNode.getComponent(Button);
                    if (buttonComponent) {
                        // 添加点击事件
                        buttonComponent.node.on(Button.EventType.CLICK, () => {
                            this.onMapButtonClick(buttonConfig);
                        }, this);

                        // 更新按钮显示
                        this.updateButtonDisplay(buttonNode, buttonConfig);

                        if (this.enableDebugLog) {
                            console.log(`✅ 地图按钮初始化成功: ${buttonConfig.buttonName} -> ${buttonConfig.locationId}`);
                        }
                    } else {
                        console.warn(`⚠️ 按钮${buttonConfig.buttonName}没有Button组件`);
                    }
                } else {
                    console.warn(`⚠️ 未找到按钮节点: ${buttonConfig.buttonName}`);
                }
            }

            console.log(`✅ MapPanelController: 初始化了${this._mapButtonConfigs.length}个地图按钮`);

        } catch (error) {
            console.error('❌ MapPanelController: 初始化地图按钮失败', error);
        }
    }

    /**
     * 地图按钮点击事件处理
     */
    private async onMapButtonClick(buttonConfig: IMapButtonData): Promise<void> {
        if (this.enableDebugLog) {
            console.log(`🗺️ MapPanelController: 点击地图按钮 ${buttonConfig.displayName} (${buttonConfig.locationId})`);
        }

        try {
            // 检查是否已解锁
            if (!buttonConfig.isUnlocked) {
                // 检查实时解锁状态
                const unlockStatus = await this.checkLocationUnlockStatus(buttonConfig.locationId);
                if (!unlockStatus) {
                    console.log(`⚠️ 地点${buttonConfig.displayName}尚未解锁`);
                    return;
                }

                // 更新解锁状态
                buttonConfig.isUnlocked = true;
                this.updateButtonDisplay(this.findButtonNode(buttonConfig.buttonName), buttonConfig);
            }

            // 切换到指定地点
            await this.switchToLocation(buttonConfig.locationId);

        } catch (error) {
            console.error(`❌ MapPanelController: 处理按钮点击失败 ${buttonConfig.locationId}`, error);
        }
    }

    /**
     * 切换到指定地点
     */
    public async switchToLocation(locationId: string): Promise<boolean> {
        if (!this._locationConfigManager) {
            console.error('❌ MapPanelController: LocationConfigManager不可用');
            return false;
        }

        try {
            if (this.enableDebugLog) {
                console.log(`🗺️ MapPanelController: 开始切换到地点 ${locationId}`);
            }

            // 查找LinearBranchMapContainer
            const mapContainer = find('Canvas/MainUI/BehaviorPanel/LinearBranchMapContainer');
            if (!mapContainer) {
                console.error('❌ MapPanelController: 未找到LinearBranchMapContainer');
                return false;
            }

            // 使用LocationConfigManager应用地点配置
            const success = await this._locationConfigManager.applyLocationConfigToContainer(locationId, mapContainer);
            
            if (success) {
                this._currentLocationId = locationId;
                
                // 发送地点切换成功事件
                EventManager.getInstance().emit('map_panel:location_switched', {
                    locationId,
                    timestamp: Date.now()
                });

                if (this.enableDebugLog) {
                    console.log(`✅ MapPanelController: 地点切换成功 ${locationId}`);
                }
                
                return true;
            } else {
                console.log(`❌ MapPanelController: 地点切换失败 ${locationId}`);
                return false;
            }

        } catch (error) {
            console.error(`❌ MapPanelController: 地点切换异常 ${locationId}`, error);
            return false;
        }
    }

    /**
     * 检查地点解锁状态
     */
    private async checkLocationUnlockStatus(locationId: string): Promise<boolean> {
        if (!this._locationConfigManager) {
            return false;
        }

        try {
            const unlockStatus = await this._locationConfigManager.getLocationUnlockStatus(locationId);
            return unlockStatus ? unlockStatus.isUnlocked : false;
        } catch (error) {
            console.error(`❌ MapPanelController: 检查解锁状态失败 ${locationId}`, error);
            return false;
        }
    }

    /**
     * 更新按钮显示状态
     */
    private updateButtonDisplay(buttonNode: Node | null, buttonConfig: IMapButtonData): void {
        if (!buttonNode) return;

        try {
            // 更新Label显示
            const labelNode = buttonNode.getChildByName('Label');
            if (labelNode) {
                const labelComponent = labelNode.getComponent(Label);
                if (labelComponent) {
                    labelComponent.string = buttonConfig.displayName;
                }
            }

            // 更新按钮状态（可以根据解锁状态改变颜色等）
            const buttonComponent = buttonNode.getComponent(Button);
            if (buttonComponent) {
                buttonComponent.interactable = buttonConfig.isUnlocked;
            }

            // 更新Sprite显示（可以根据解锁状态改变图片）
            const spriteNode = buttonNode.getChildByName('Sprite');
            if (spriteNode) {
                const spriteComponent = spriteNode.getComponent(Sprite);
                if (spriteComponent) {
                    // 这里可以根据解锁状态设置不同的图片
                    spriteComponent.color = buttonConfig.isUnlocked ?
                        new Color(255, 255, 255, 255) :
                        new Color(128, 128, 128, 255);
                }
            }

        } catch (error) {
            console.error('❌ MapPanelController: 更新按钮显示失败', error);
        }
    }

    /**
     * 查找按钮节点
     */
    private findButtonNode(buttonName: string): Node | null {
        try {
            const node = (this as any).node as Node; // 类型断言解决IDE问题
            const maplistNode = node.getChildByName('Maplist');
            if (maplistNode) {
                const listNode = maplistNode.getChildByName('list');
                if (listNode) {
                    return listNode.getChildByName(buttonName);
                }
            }
        } catch (error) {
            console.error('❌ MapPanelController: 查找按钮节点失败', error);
        }
        return null;
    }

    /**
     * 刷新所有按钮状态
     */
    public async refreshAllButtonStates(): Promise<void> {
        if (this.enableDebugLog) {
            console.log('🔄 MapPanelController: 刷新所有按钮状态');
        }

        for (const buttonConfig of this._mapButtonConfigs) {
            const isUnlocked = await this.checkLocationUnlockStatus(buttonConfig.locationId);
            if (isUnlocked !== buttonConfig.isUnlocked) {
                buttonConfig.isUnlocked = isUnlocked;
                const buttonNode = this.findButtonNode(buttonConfig.buttonName);
                this.updateButtonDisplay(buttonNode, buttonConfig);
            }
        }
    }

    /**
     * 获取当前地点ID
     */
    public getCurrentLocationId(): string {
        return this._currentLocationId;
    }

    /**
     * 获取地图按钮配置
     */
    public getMapButtonConfigs(): IMapButtonData[] {
        return [...this._mapButtonConfigs];
    }
}
