import mongoose, { Document, Schema } from 'mongoose';
import bcrypt from 'bcryptjs';
import { Logger } from '../utils/logger';

/**
 * 用户角色枚举
 */
export enum UserRole {
  USER = 'user',
  ADMIN = 'admin',
  MODERATOR = 'moderator',
}

/**
 * 用户状态枚举
 */
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  BANNED = 'banned',
  PENDING = 'pending',
}

/**
 * 用户接口
 */
export interface IUser extends Document {
  _id: mongoose.Types.ObjectId;
  username: string;
  email: string;
  password: string;
  role: 'user' | 'admin' | 'moderator';
  isActive: boolean;
  isEmailVerified: boolean;
  profile: {
    nickname?: string;
    avatar?: string;
    bio?: string;
    level: number;
    experience: number;
    totalPlayTime: number; // 总游戏时间（分钟）
    lastLoginAt?: Date;
    loginCount: number;
    preferences: {
      language: string;
      timezone: string;
      notifications: {
        email: boolean;
        push: boolean;
        inGame: boolean;
      };
      privacy: {
        showOnlineStatus: boolean;
        allowFriendRequests: boolean;
        showProfile: boolean;
      };
    };
  };
  gameData: {
    currentCharacterId?: mongoose.Types.ObjectId;
    charactersCreated: number;
    totalGoldEarned: number;
    totalBattlesWon: number;
    totalBattlesLost: number;
    achievements: string[];
    statistics: {
      monstersKilled: number;
      itemsCollected: number;
      skillsLearned: number;
      questsCompleted: number;
    };
  };
  security: {
    passwordResetToken?: string;
    passwordResetExpires?: Date;
    emailVerificationToken?: string;
    emailVerificationExpires?: Date;
    twoFactorEnabled: boolean;
    twoFactorSecret?: string;
    loginAttempts: number;
    lockUntil?: Date;
  };
  createdAt: Date;
  updatedAt: Date;

  // 方法
  comparePassword(candidatePassword: string): Promise<boolean>;
  generatePasswordResetToken(): string;
  generateEmailVerificationToken(): string;
  incrementLoginAttempts(): Promise<void>;
  resetLoginAttempts(): Promise<void>;
  isLocked(): boolean;
  toPublicJSON(): any;
}

/**
 * 用户Schema
 */
const UserSchema = new Schema<IUser>({
  username: {
    type: String,
    required: [true, '用户名是必填项'],
    unique: true,
    trim: true,
    minlength: [3, '用户名至少3个字符'],
    maxlength: [20, '用户名最多20个字符'],
    match: [/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线'],
    index: true,
  },
  email: {
    type: String,
    required: [true, '邮箱是必填项'],
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, '请输入有效的邮箱地址'],
    index: true,
  },
  password: {
    type: String,
    required: [true, '密码是必填项'],
    minlength: [6, '密码至少6个字符'],
    select: false, // 默认不返回密码字段
  },
  role: {
    type: String,
    enum: ['user', 'admin', 'moderator'],
    default: 'user',
    index: true,
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true,
  },
  isEmailVerified: {
    type: Boolean,
    default: false,
  },
  profile: {
    nickname: {
      type: String,
      trim: true,
      maxlength: [30, '昵称最多30个字符'],
    },
    avatar: {
      type: String,
      default: '',
    },
    bio: {
      type: String,
      maxlength: [200, '个人简介最多200个字符'],
    },
    level: {
      type: Number,
      default: 1,
      min: [1, '等级不能小于1'],
    },
    experience: {
      type: Number,
      default: 0,
      min: [0, '经验值不能为负数'],
    },
    totalPlayTime: {
      type: Number,
      default: 0,
      min: [0, '游戏时间不能为负数'],
    },
    lastLoginAt: {
      type: Date,
    },
    loginCount: {
      type: Number,
      default: 0,
      min: [0, '登录次数不能为负数'],
    },
    preferences: {
      language: {
        type: String,
        default: 'zh-CN',
        enum: ['zh-CN', 'en-US'],
      },
      timezone: {
        type: String,
        default: 'Asia/Shanghai',
      },
      notifications: {
        email: {
          type: Boolean,
          default: true,
        },
        push: {
          type: Boolean,
          default: true,
        },
        inGame: {
          type: Boolean,
          default: true,
        },
      },
      privacy: {
        showOnlineStatus: {
          type: Boolean,
          default: true,
        },
        allowFriendRequests: {
          type: Boolean,
          default: true,
        },
        showProfile: {
          type: Boolean,
          default: true,
        },
      },
    },
  },
  gameData: {
    currentCharacterId: {
      type: Schema.Types.ObjectId,
      ref: 'Character',
    },
    charactersCreated: {
      type: Number,
      default: 0,
      min: [0, '角色创建数量不能为负数'],
    },
    totalGoldEarned: {
      type: Number,
      default: 0,
      min: [0, '金币数量不能为负数'],
    },
    totalBattlesWon: {
      type: Number,
      default: 0,
      min: [0, '胜利次数不能为负数'],
    },
    totalBattlesLost: {
      type: Number,
      default: 0,
      min: [0, '失败次数不能为负数'],
    },
    achievements: [{
      type: String,
    }],
    statistics: {
      monstersKilled: {
        type: Number,
        default: 0,
        min: [0, '击杀数量不能为负数'],
      },
      itemsCollected: {
        type: Number,
        default: 0,
        min: [0, '物品收集数量不能为负数'],
      },
      skillsLearned: {
        type: Number,
        default: 0,
        min: [0, '技能学习数量不能为负数'],
      },
      questsCompleted: {
        type: Number,
        default: 0,
        min: [0, '任务完成数量不能为负数'],
      },
    },
  },
  security: {
    passwordResetToken: {
      type: String,
      select: false,
    },
    passwordResetExpires: {
      type: Date,
      select: false,
    },
    emailVerificationToken: {
      type: String,
      select: false,
    },
    emailVerificationExpires: {
      type: Date,
      select: false,
    },
    twoFactorEnabled: {
      type: Boolean,
      default: false,
    },
    twoFactorSecret: {
      type: String,
      select: false,
    },
    loginAttempts: {
      type: Number,
      default: 0,
    },
    lockUntil: {
      type: Date,
    },
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// 索引
UserSchema.index({ username: 1 });
UserSchema.index({ email: 1 });
UserSchema.index({ role: 1 });
UserSchema.index({ isActive: 1 });
UserSchema.index({ 'profile.level': -1 });
UserSchema.index({ 'gameData.totalGoldEarned': -1 });
UserSchema.index({ createdAt: -1 });

// 虚拟字段（isLocked已作为方法实现，避免冲突）

UserSchema.virtual('profile.winRate').get(function() {
  const totalBattles = this.gameData.totalBattlesWon + this.gameData.totalBattlesLost;
  return totalBattles > 0 ? (this.gameData.totalBattlesWon / totalBattles * 100).toFixed(2) : 0;
});

// 中间件：保存前加密密码
UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();

  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error as any);
  }
});

// 中间件：更新时间戳
UserSchema.pre('save', function(next) {
  if (this.isNew) {
    this.profile.preferences = this.profile.preferences || {
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      notifications: {
        email: true,
        push: true,
        inGame: true,
      },
      privacy: {
        showOnlineStatus: true,
        allowFriendRequests: true,
        showProfile: true,
      },
    };
  }
  next();
});

// 实例方法：比较密码
UserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  try {
    return await bcrypt.compare(candidatePassword, this.password);
  } catch (error) {
    Logger.error('密码比较失败', { userId: this._id, error });
    return false;
  }
};

// 实例方法：生成密码重置令牌
UserSchema.methods.generatePasswordResetToken = function(): string {
  const crypto = require('crypto');
  const resetToken = crypto.randomBytes(32).toString('hex');
  
  this.security.passwordResetToken = crypto.createHash('sha256').update(resetToken).digest('hex');
  this.security.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000); // 10分钟
  
  return resetToken;
};

// 实例方法：生成邮箱验证令牌
UserSchema.methods.generateEmailVerificationToken = function(): string {
  const crypto = require('crypto');
  const verificationToken = crypto.randomBytes(32).toString('hex');
  
  this.security.emailVerificationToken = crypto.createHash('sha256').update(verificationToken).digest('hex');
  this.security.emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24小时
  
  return verificationToken;
};

// 实例方法：增加登录尝试次数
UserSchema.methods.incrementLoginAttempts = async function(): Promise<void> {
  // 如果之前有锁定且已过期，重置
  if (this.security.lockUntil && this.security.lockUntil < new Date()) {
    return this.updateOne({
      $unset: { 'security.lockUntil': 1 },
      $set: { 'security.loginAttempts': 1 }
    });
  }
  
  const updates: any = { $inc: { 'security.loginAttempts': 1 } };
  
  // 如果达到最大尝试次数且未锁定，则锁定账户
  if (this.security.loginAttempts + 1 >= 5 && !this.isLocked()) {
    updates.$set = { 'security.lockUntil': new Date(Date.now() + 2 * 60 * 60 * 1000) }; // 锁定2小时
  }
  
  return this.updateOne(updates);
};

// 实例方法：重置登录尝试次数
UserSchema.methods.resetLoginAttempts = async function(): Promise<void> {
  return this.updateOne({
    $unset: {
      'security.loginAttempts': 1,
      'security.lockUntil': 1
    }
  });
};

// 实例方法：检查是否被锁定
UserSchema.methods.isLocked = function(): boolean {
  return !!(this.security.lockUntil && this.security.lockUntil > new Date());
};

// 实例方法：转换为公开JSON
UserSchema.methods.toPublicJSON = function(): any {
  const userObject = this.toObject();
  
  // 删除敏感信息
  delete userObject.password;
  delete userObject.security;
  delete userObject.__v;
  
  return userObject;
};

// 静态方法：根据用户名或邮箱查找用户
UserSchema.statics.findByUsernameOrEmail = function(identifier: string) {
  return this.findOne({
    $or: [
      { username: identifier },
      { email: identifier.toLowerCase() }
    ]
  }).select('+password');
};

export const User = mongoose.model<IUser>('User', UserSchema);
