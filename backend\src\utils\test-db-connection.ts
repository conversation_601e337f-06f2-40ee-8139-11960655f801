import dotenv = require('dotenv');
import { DatabaseConfig } from '../config/database';
import { Logger } from './logger';

// 加载环境变量
dotenv.config();

/**
 * 测试数据库连接
 */
export async function testDatabaseConnection(): Promise<boolean> {
  try {
    Logger.info('开始测试数据库连接...');
    
    const dbConfig = DatabaseConfig.getInstance();
    await dbConfig.connect();
    
    const isHealthy = await dbConfig.healthCheck();
    if (isHealthy) {
      Logger.info('数据库连接测试成功！');
      return true;
    } else {
      Logger.error('数据库健康检查失败');
      return false;
    }
  } catch (error) {
    Logger.error('数据库连接测试失败', error);
    return false;
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  testDatabaseConnection()
    .then((success) => {
      if (success) {
        console.log('✅ 数据库连接测试通过');
        process.exit(0);
      } else {
        console.log('❌ 数据库连接测试失败');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('❌ 数据库连接测试出错:', error);
      process.exit(1);
    });
}
