{"// Copyright (c) 2025 \"放置\". All rights reserved.": "", "version": "1.0.0", "config_type": "location_configs", "last_updated": "2025-01-10T00:00:00Z", "checksum": "location_config_v1_0_0", "locations": {"fishpond": {"locationId": "fishpond", "name": "鱼塘", "description": "宁静的鱼塘，适合钓鱼放松", "type": "fishing", "level": 1, "unlockConditions": {"playerLevel": 1, "requiredItems": [], "completedQuests": [], "requiredAreas": [], "customConditions": []}, "mapConfig": {"backgroundImagePath": "areas/fishpond/background", "mapSize": {"width": 1600, "height": 1200}, "nodeSpacing": {"x": 200, "y": 150}, "startPosition": {"x": -600, "y": 0}, "branchCount": 3, "nodesPerBranch": 5}, "environment": {"weather": "sunny", "timeOfDay": "morning", "backgroundMusic": "audio/bgm/peaceful_pond", "ambientSounds": ["water_ripple", "bird_chirping"], "lightingColor": {"r": 255, "g": 248, "b": 220, "a": 255}}, "resources": {"monsters": [], "collectibles": []}, "rewards": {"baseExp": 10, "baseGold": 5, "dropTables": ["fishing_drops"]}, "uiElements": [], "nodeData": [], "behaviors": [{"id": "fishing_basic", "name": "基础钓鱼", "description": "使用基础鱼竿进行钓鱼", "type": "fishing", "duration": 30, "cooldown": 5, "energyCost": 10, "requirements": {"items": ["fishing_rod_basic"], "skills": [], "level": 1}, "rewards": {"baseExp": 10, "items": [{"id": "fish_common", "probability": 0.7, "quantity": [1, 3]}, {"id": "fish_rare", "probability": 0.2, "quantity": [1, 1]}, {"id": "treasure_box", "probability": 0.1, "quantity": [1, 1]}], "currency": {"gold": [5, 15]}}}, {"id": "fishing_advanced", "name": "高级钓鱼", "description": "使用高级鱼竿进行钓鱼，获得更好的收益", "type": "fishing", "duration": 60, "cooldown": 10, "energyCost": 20, "requirements": {"items": ["fishing_rod_advanced"], "skills": ["fishing_skill"], "level": 10}, "rewards": {"baseExp": 25, "items": [{"id": "fish_rare", "probability": 0.5, "quantity": [1, 2]}, {"id": "fish_epic", "probability": 0.3, "quantity": [1, 1]}, {"id": "treasure_box_rare", "probability": 0.2, "quantity": [1, 1]}], "currency": {"gold": [15, 35]}}}], "uiLayout": {"behaviorButtonsPosition": {"x": 0, "y": -300}, "infoDisplayPosition": {"x": -400, "y": 250}, "customElements": [{"type": "weather_display", "position": {"x": 350, "y": 250}, "config": {"showTemperature": true, "showWind": false}}]}}, "farm": {"locationId": "farm", "name": "农场", "description": "肥沃的农场，适合种植各种作物", "type": "farming", "level": 2, "unlockConditions": {"playerLevel": 5, "requiredItems": [], "completedQuests": ["tutorial_fishing"], "requiredAreas": ["fishpond"], "customConditions": []}, "mapConfig": {"backgroundImagePath": "areas/farm/background", "mapSize": {"width": 1800, "height": 1400}, "nodeSpacing": {"x": 180, "y": 160}, "startPosition": {"x": -700, "y": 0}, "branchCount": 4, "nodesPerBranch": 6}, "environment": {"weather": "sunny", "timeOfDay": "afternoon", "backgroundMusic": "audio/bgm/farm_life", "ambientSounds": ["wind_through_crops", "farm_animals"], "lightingColor": {"r": 255, "g": 235, "b": 200, "a": 255}}, "resources": {"monsters": [], "collectibles": []}, "rewards": {"baseExp": 15, "baseGold": 8, "dropTables": ["farming_drops"]}, "uiElements": [], "nodeData": [], "behaviors": [{"id": "planting_basic", "name": "基础种植", "description": "种植基础作物", "type": "farming", "duration": 45, "cooldown": 0, "energyCost": 15, "requirements": {"items": ["seeds_basic"], "skills": [], "level": 5}, "rewards": {"baseExp": 15, "items": [{"id": "crop_wheat", "probability": 0.8, "quantity": [2, 4]}, {"id": "crop_corn", "probability": 0.6, "quantity": [1, 3]}], "currency": {"gold": [8, 20]}}}, {"id": "harvesting", "name": "收获作物", "description": "收获成熟的作物", "type": "farming", "duration": 20, "cooldown": 0, "energyCost": 5, "requirements": {"items": ["harvesting_tool"], "skills": [], "level": 5}, "rewards": {"baseExp": 8, "items": [{"id": "crop_wheat", "probability": 1.0, "quantity": [3, 6]}, {"id": "crop_corn", "probability": 1.0, "quantity": [2, 5]}], "currency": {"gold": [10, 25]}}}], "uiLayout": {"behaviorButtonsPosition": {"x": 0, "y": -320}, "infoDisplayPosition": {"x": -420, "y": 280}, "customElements": [{"type": "crop_status_display", "position": {"x": 380, "y": 280}, "config": {"showGrowthTime": true, "showYield": true}}]}}, "forest": {"locationId": "forest", "name": "森林", "description": "茂密的森林，适合伐木和探险", "type": "logging", "level": 3, "unlockConditions": {"playerLevel": 10, "requiredItems": ["axe_basic"], "completedQuests": ["farm_tutorial"], "requiredAreas": ["farm"], "customConditions": []}, "mapConfig": {"backgroundImagePath": "areas/forest/background", "mapSize": {"width": 2000, "height": 1500}, "nodeSpacing": {"x": 220, "y": 180}, "startPosition": {"x": -800, "y": 0}, "branchCount": 5, "nodesPerBranch": 7}, "environment": {"weather": "cloudy", "timeOfDay": "evening", "backgroundMusic": "audio/bgm/mysterious_forest", "ambientSounds": ["leaves_rustling", "owl_hooting", "distant_howl"], "lightingColor": {"r": 200, "g": 220, "b": 180, "a": 255}}, "resources": {"monsters": [], "collectibles": []}, "rewards": {"baseExp": 18, "baseGold": 12, "dropTables": ["logging_drops"]}, "uiElements": [], "nodeData": [], "behaviors": [{"id": "logging_basic", "name": "基础伐木", "description": "砍伐普通树木获得木材", "type": "logging", "duration": 40, "cooldown": 8, "energyCost": 18, "requirements": {"items": ["axe_basic"], "skills": [], "level": 10}, "rewards": {"baseExp": 18, "items": [{"id": "wood_common", "probability": 0.9, "quantity": [2, 5]}, {"id": "wood_rare", "probability": 0.3, "quantity": [1, 2]}, {"id": "tree_sap", "probability": 0.4, "quantity": [1, 1]}], "currency": {"gold": [12, 28]}}}, {"id": "exploration", "name": "森林探险", "description": "在森林中探险，寻找宝藏和稀有资源", "type": "exploration", "duration": 90, "cooldown": 30, "energyCost": 25, "requirements": {"items": ["exploration_gear"], "skills": ["survival"], "level": 15}, "rewards": {"baseExp": 35, "items": [{"id": "herb_rare", "probability": 0.6, "quantity": [1, 3]}, {"id": "gem_small", "probability": 0.4, "quantity": [1, 1]}, {"id": "ancient_artifact", "probability": 0.1, "quantity": [1, 1]}], "currency": {"gold": [20, 50]}}}], "uiLayout": {"behaviorButtonsPosition": {"x": 0, "y": -350}, "infoDisplayPosition": {"x": -450, "y": 300}, "customElements": [{"type": "danger_level_display", "position": {"x": 400, "y": 300}, "config": {"showMonsterLevel": true, "showSafetyTips": true}}]}}}}