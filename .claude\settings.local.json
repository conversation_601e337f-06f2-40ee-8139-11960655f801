{"$schema": "https://json.schemastore.org/claude-code-settings.json", "mcpServers": {"cocos-creator": {"type": "http", "url": "http://127.0.0.1:3000/mcp"}}, "permissions": {"allow": ["mcp__cocos-creator__scene_scene_management", "mcp__cocos-creator__scene_scene_hierarchy", "mcp__cocos-creator__node_node_query", "mcp__cocos-creator__node_node_lifecycle", "mcp__cocos-creator__component_component_manage", "mcp__cocos-creator__node_node_transform", "mcp__cocos-creator__component_set_component_property", "mcp__cocos-creator__component_component_query", "mcp__cocos-creator__prefab_prefab_lifecycle", "mcp__cocos-creator__prefab_prefab_edit", "mcp__cocos-creator__assetAdvanced_asset_query", "mcp__cocos-creator__scene_scene_query_system", "mcp__cocos-creator__component_configure_click_event"], "deny": []}}