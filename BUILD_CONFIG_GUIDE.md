# Copyright (c) 2025 "放置". All rights reserved.

# 构建配置指南 - 确保配置文件正确读取

## 🎯 问题解决方案

### 问题1: shared文件夹构建读取
**解决方案**: 已修改tsconfig.json，将shared文件夹包含在编译路径中

### 问题2: 分包策略优化
**解决方案**: 重新设计Asset Bundle分包策略，支持更精细的按需加载

## 📁 配置文件架构调整

### 1. TypeScript编译配置
```json
// tsconfig.json 已更新
{
  "include": [
    "assets/client/scripts/**/*.ts",
    "shared/**/*.ts"  // ✅ 新增shared文件夹
  ],
  "paths": {
    "@shared/*": ["shared/*"],
    "@shared/types/*": ["shared/types/*"],
    "@shared/configs/*": ["shared/configs/*"]
  }
}
```

### 2. 配置文件分层
```
项目根目录/
├── assets/configs/              # 🎨 客户端安全配置 (构建时打包)
│   ├── display/                 # 显示配置
│   ├── audio/                   # 音频配置
│   ├── localization/            # 本地化配置
│   └── cache/                   # 客户端缓存
│
├── shared/configs/              # 🔗 类型定义 (编译时处理)
│   ├── types/                   # TypeScript类型定义
│   │   ├── config-types.ts     # ✅ 已创建
│   │   ├── game-types.ts       # ✅ 已创建
│   │   └── api-types.ts        # ✅ 已创建
│   ├── interfaces/              # API接口定义
│   └── constants/               # 常量定义
│
└── backend/src/configs/         # 🔒 服务器配置 (不打包)
    ├── game/                    # 游戏逻辑配置
    ├── security/                # 安全配置
    └── database/                # 数据库配置
```

## 🚀 构建时配置处理

### 1. 编译阶段
```typescript
// shared文件夹中的TypeScript文件会被编译到JavaScript
// 类型定义在编译时被处理，运行时不需要读取文件

// 示例：使用shared类型
import { ConfigSecurityLevel } from '@shared/configs/types/config-types';
import { PlayerInfo } from '@shared/configs/types/game-types';
```

### 2. 打包阶段
```
主包 (4MB以内):
├── 启动场景
├── 核心管理器
├── 基础UI组件
└── 必要配置文件

子包 (按需加载):
├── core (核心功能)
├── ui-basic (基础UI)
├── features (游戏功能)
├── locations (地点配置)
├── ui-advanced (高级UI)
└── audio (音频资源)

远程包 (服务器加载):
├── configs (配置数据)
└── remote-resources (可选资源)
```

### 3. 运行时配置加载
```typescript
// 使用EnhancedConfigManager加载配置
const configManager = EnhancedConfigManager.getInstance();

// 从Bundle加载配置
const locationConfig = await configManager.loadConfigFromBundle<ClientDisplayConfig[]>(
  'configs',
  'display/locations',
  {
    securityLevel: ConfigSecurityLevel.PUBLIC,
    type: ConfigType.STATIC
  }
);
```

## 🔧 构建配置优化

### 1. Bundle配置
```json
// assets/bundles/configs.meta
{
  "bundle": {
    "name": "configs",
    "priority": 5,
    "target": "wechatgame",
    "compressionType": "merge_all_json",  // 合并JSON文件
    "isRemoteBundle": true                // 远程加载
  }
}
```

### 2. 构建脚本优化
```json
// build-config.json
{
  "optimization": {
    "mergeStartScene": true,
    "optimizeHotUpdate": true,
    "md5Cache": true,
    "compressTexture": true,
    "compressAudio": true
  }
}
```

## 📊 分包策略详解

### 1. 主包内容 (≤4MB)
- **启动场景**: Launch.scene
- **核心管理器**: GameManager, ConfigManager
- **基础UI**: 启动界面、加载界面
- **必要配置**: 基础游戏配置

### 2. 核心子包 (core)
- **优先级**: 1 (最高)
- **内容**: 核心游戏逻辑、数据管理
- **加载时机**: 游戏启动后立即加载

### 3. UI子包 (ui-basic, ui-advanced)
- **ui-basic**: 基础游戏界面
- **ui-advanced**: 高级功能界面
- **加载时机**: 按需加载

### 4. 功能子包 (features, locations)
- **features**: 特定游戏功能
- **locations**: 地点相关资源
- **加载时机**: 用户访问时加载

### 5. 远程包 (configs, remote-resources)
- **configs**: 可热更新的配置
- **remote-resources**: 可选资源
- **加载时机**: 从服务器按需加载

## 🛠️ 实施步骤

### 阶段1: 配置文件整理 ✅
1. ✅ 修改tsconfig.json包含shared文件夹
2. ✅ 创建shared类型定义文件
3. ✅ 验证TypeScript编译

### 阶段2: Bundle配置优化 ✅
1. ✅ 创建新的Bundle目录
2. ✅ 配置Bundle元数据
3. ✅ 优化分包策略

### 阶段3: 配置管理器增强 ✅
1. ✅ 创建EnhancedConfigManager
2. ✅ 支持Bundle配置加载
3. ✅ 实现缓存和降级策略

### 阶段4: 构建测试 (进行中)
1. 🔄 执行完整构建测试
2. 🔄 验证配置加载功能
3. 🔄 测试分包效果

## ✅ 验证清单

### 编译验证
- [x] shared文件夹TypeScript编译通过
- [x] 类型定义正确导入
- [x] 路径映射配置正确

### 构建验证
- [ ] 主包大小控制在4MB以内
- [ ] 子包正确分离
- [ ] 远程包配置正确
- [ ] 配置文件正确打包

### 运行时验证
- [ ] 配置正确加载
- [ ] Bundle按需加载正常
- [ ] 缓存机制工作正常
- [ ] 降级方案可用

## 🚨 注意事项

### 1. 构建时
- shared文件夹中的.ts文件会被编译，不需要运行时读取
- assets/configs中的配置文件会被打包到对应Bundle
- backend/src/configs不会被打包到客户端

### 2. 运行时
- 使用EnhancedConfigManager管理配置加载
- 优先从Bundle加载，失败时使用降级方案
- 配置缓存提升性能

### 3. 分包策略
- 核心功能放在高优先级子包
- 可选功能放在低优先级子包
- 配置数据使用远程包支持热更新

这个配置架构调整确保了：
1. shared文件夹在构建时正确编译
2. 配置文件在运行时正确读取
3. 分包策略支持后续扩展
4. 性能和用户体验得到优化
