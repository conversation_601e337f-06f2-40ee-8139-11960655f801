# 后端部署指南

## 🎯 部署架构总结

您现在拥有一个**统一的后端架构**，支持本地开发和远程部署的无缝切换：

```
统一后端架构
├── 本地开发环境 (localhost:3000)
├── 远程服务器环境 (云服务器:3000)
└── 相同的代码逻辑和API接口
```

## ✅ 已完成的优化

### 1. 统一的地点配置API
- ✅ **路由整合**: `/api/v1/locations/*` 
- ✅ **缓存机制**: 内存缓存 + 30分钟过期
- ✅ **配置管理**: 支持Forest、Desert、Mountain地点
- ✅ **API接口**: 单个获取、批量获取、列表查询

### 2. 环境配置管理
- ✅ **智能配置**: 根据DEPLOYMENT_ENV自动切换
- ✅ **配置验证**: 启动时验证配置完整性
- ✅ **环境隔离**: 本地/远程环境完全隔离

### 3. 代码结构优化
- ✅ **删除冗余**: 移除test-server.js等重复代码
- ✅ **统一入口**: 使用src/server.ts作为唯一入口
- ✅ **类型安全**: 完整的TypeScript类型支持

## 🚀 使用方法

### 本地开发
```bash
# 方法1: 使用环境切换工具
npm run env:local
npm run dev

# 方法2: 直接指定环境
npm run dev:local

# 方法3: 手动设置环境变量
DEPLOYMENT_ENV=local npm run dev
```

### 远程部署测试
```bash
# 切换到远程环境配置
npm run env:remote

# 更新远程服务器地址
# 编辑 .env.remote 文件中的 API_BASE_URL

# 启动连接远程服务器
npm run dev:remote
```

### 生产环境部署
```bash
# 构建项目
npm run deploy:build

# 生产环境启动
npm run start:prod
```

## 🔧 环境切换工具

### 快速切换命令
```bash
# 切换到本地环境
npm run env:local

# 切换到远程环境  
npm run env:remote

# 查看当前环境状态
npm run env:status
```

### 手动切换
```bash
# 使用切换脚本
node scripts/switch-env.js switch local
node scripts/switch-env.js switch remote
node scripts/switch-env.js status
```

## 📊 API接口列表

### 地点配置API
```
GET  /api/v1/locations/config/:locationId     # 获取单个地点配置
POST /api/v1/locations/config/batch           # 批量获取地点配置  
GET  /api/v1/locations/list                   # 获取地点列表
POST /api/v1/locations/cache/refresh          # 刷新配置缓存
GET  /api/v1/locations/cache/status           # 获取缓存状态
```

### 系统API
```
GET  /health                                   # 健康检查
GET  /api/info                                 # API文档
```

## 🌐 环境配置说明

### 本地环境 (.env.local)
```
DEPLOYMENT_ENV=local
API_BASE_URL=http://localhost:3000
MONGODB_URI=mongodb://localhost:27017/idlegame_dev
```

### 远程环境 (.env.remote)
```
DEPLOYMENT_ENV=remote  
API_BASE_URL=https://your-aliyun-server.com
MONGODB_URI=mongodb://localhost:27017/idlegame_prod
```

## 🔄 部署流程

### 第一步：本地开发
```bash
# 1. 切换到本地环境
npm run env:local

# 2. 启动本地服务器
npm run dev

# 3. 测试API接口
curl http://localhost:3000/health
curl http://localhost:3000/api/v1/locations/list
```

### 第二步：远程部署
```bash
# 1. 更新远程服务器地址
# 编辑 backend/.env.remote 中的 API_BASE_URL

# 2. 构建项目
npm run deploy:build

# 3. 上传到服务器
scp -r dist/ root@your-server:/opt/idlegame/
scp package.json root@your-server:/opt/idlegame/
scp .env.remote root@your-server:/opt/idlegame/.env

# 4. 服务器上启动
ssh root@your-server
cd /opt/idlegame
npm install --production
npm run start:prod
```

### 第三步：前端配置
```bash
# 1. 更新前端服务器配置
# 编辑 assets/scripts/config/ServerConfig.ts

# 2. 启用阿里云服务器
ServerConfig.setServerEnabled('阿里云生产服务器', true)
ServerConfig.setServerUrl('阿里云生产服务器', 'https://your-actual-server.com')

# 3. 测试前端连接
# 点击游戏中的地图按钮，观察控制台输出
```

## 📋 配置检查清单

### 后端配置
- [ ] 更新 `.env.remote` 中的服务器地址
- [ ] 配置生产环境数据库连接
- [ ] 设置强密码的JWT_SECRET
- [ ] 验证CORS配置

### 前端配置  
- [ ] 更新 `ServerConfig.ts` 中的服务器地址
- [ ] 启用对应的服务器配置
- [ ] 测试网络请求是否正常

### 服务器配置
- [ ] 安装Node.js和npm
- [ ] 配置MongoDB数据库
- [ ] 设置防火墙规则 (开放3000端口)
- [ ] 配置进程管理 (PM2)

## 🎯 测试验证

### 本地测试
```bash
# 健康检查
curl http://localhost:3000/health

# 地点配置
curl http://localhost:3000/api/v1/locations/list
curl http://localhost:3000/api/v1/locations/config/Forest
```

### 远程测试
```bash
# 替换为实际服务器地址
curl https://your-server.com/health
curl https://your-server.com/api/v1/locations/list
```

### 前端测试
1. 启动游戏
2. 点击地图按钮
3. 观察控制台输出
4. 验证地点切换功能

## 🔍 故障排除

### 常见问题
1. **端口冲突**: 检查3000端口是否被占用
2. **数据库连接失败**: 验证MongoDB是否启动
3. **CORS错误**: 检查CORS配置是否正确
4. **配置文件错误**: 使用 `npm run env:status` 检查

### 调试命令
```bash
# 查看环境状态
npm run env:status

# 查看服务器日志
npm run dev  # 开发环境
pm2 logs     # 生产环境

# 测试数据库连接
node -e "require('./dist/config/database').DatabaseConfig.getInstance().connect()"
```

## 🎊 总结

您现在拥有：

1. **✅ 统一的后端架构** - 一套代码支持本地和远程
2. **✅ 灵活的环境切换** - 一键切换开发/生产环境  
3. **✅ 完整的API接口** - 地点配置管理功能完备
4. **✅ 智能配置管理** - 自动适配不同环境
5. **✅ 便捷的部署工具** - 自动化脚本和命令

**下一步**: 
1. 更新 `.env.remote` 中的服务器地址
2. 运行 `npm run env:remote` 切换环境
3. 部署到云服务器并测试

您的后端已经完全准备好支持本地开发和远程部署了！🚀
