import { CacheManager, CacheOptions } from '../utils/cache';
import { Logger } from '../utils/logger';

/**
 * 缓存策略枚举
 */
export enum CacheStrategy {
  WRITE_THROUGH = 'write_through',     // 写穿透
  WRITE_BACK = 'write_back',           // 写回
  WRITE_AROUND = 'write_around',       // 写绕过
  READ_THROUGH = 'read_through',       // 读穿透
  CACHE_ASIDE = 'cache_aside',         // 缓存旁路
}

/**
 * 缓存策略配置接口
 */
export interface CacheStrategyConfig {
  strategy: CacheStrategy;
  ttl: number;
  prefix: string;
  maxSize?: number;
  refreshThreshold?: number; // 刷新阈值（剩余TTL百分比）
  lockTimeout?: number; // 锁超时时间
  retryCount?: number; // 重试次数
  fallbackTtl?: number; // 降级TTL
}

/**
 * 缓存策略管理器
 */
export class CacheStrategyManager {
  private static instance: CacheStrategyManager;
  private cacheManager: CacheManager;
  private strategies: Map<string, CacheStrategyConfig> = new Map();

  private constructor() {
    this.cacheManager = CacheManager.getInstance();
    this.initializeStrategies();
  }

  public static getInstance(): CacheStrategyManager {
    if (!CacheStrategyManager.instance) {
      CacheStrategyManager.instance = new CacheStrategyManager();
    }
    return CacheStrategyManager.instance;
  }

  /**
   * 初始化缓存策略
   */
  private initializeStrategies(): void {
    // 用户数据缓存策略
    this.strategies.set('user', {
      strategy: CacheStrategy.CACHE_ASIDE,
      ttl: 3600, // 1小时
      prefix: 'user:',
      refreshThreshold: 0.2, // 剩余20%时刷新
      lockTimeout: 10,
      retryCount: 3,
      fallbackTtl: 300, // 5分钟降级TTL
    });

    // 用户会话缓存策略
    this.strategies.set('session', {
      strategy: CacheStrategy.WRITE_THROUGH,
      ttl: 1800, // 30分钟
      prefix: 'session:',
      refreshThreshold: 0.3,
      lockTimeout: 5,
      retryCount: 2,
      fallbackTtl: 600, // 10分钟降级TTL
    });

    // 游戏配置缓存策略
    this.strategies.set('gameConfig', {
      strategy: CacheStrategy.READ_THROUGH,
      ttl: 86400, // 24小时
      prefix: 'config:',
      refreshThreshold: 0.1, // 剩余10%时刷新
      lockTimeout: 30,
      retryCount: 5,
      fallbackTtl: 3600, // 1小时降级TTL
    });

    // 排行榜缓存策略
    this.strategies.set('leaderboard', {
      strategy: CacheStrategy.WRITE_BACK,
      ttl: 300, // 5分钟
      prefix: 'leaderboard:',
      maxSize: 1000, // 最多缓存1000条记录
      refreshThreshold: 0.5,
      lockTimeout: 15,
      retryCount: 3,
      fallbackTtl: 60, // 1分钟降级TTL
    });

    // 角色数据缓存策略
    this.strategies.set('character', {
      strategy: CacheStrategy.CACHE_ASIDE,
      ttl: 7200, // 2小时
      prefix: 'character:',
      refreshThreshold: 0.25,
      lockTimeout: 10,
      retryCount: 3,
      fallbackTtl: 600, // 10分钟降级TTL
    });

    // 物品数据缓存策略
    this.strategies.set('item', {
      strategy: CacheStrategy.READ_THROUGH,
      ttl: 43200, // 12小时
      prefix: 'item:',
      refreshThreshold: 0.15,
      lockTimeout: 20,
      retryCount: 4,
      fallbackTtl: 1800, // 30分钟降级TTL
    });

    // 公会数据缓存策略
    this.strategies.set('guild', {
      strategy: CacheStrategy.CACHE_ASIDE,
      ttl: 1800, // 30分钟
      prefix: 'guild:',
      refreshThreshold: 0.3,
      lockTimeout: 15,
      retryCount: 3,
      fallbackTtl: 300, // 5分钟降级TTL
    });

    // 战斗记录缓存策略
    this.strategies.set('battle', {
      strategy: CacheStrategy.WRITE_AROUND,
      ttl: 600, // 10分钟
      prefix: 'battle:',
      maxSize: 500,
      refreshThreshold: 0.4,
      lockTimeout: 5,
      retryCount: 2,
      fallbackTtl: 120, // 2分钟降级TTL
    });

    // 聊天消息缓存策略
    this.strategies.set('chat', {
      strategy: CacheStrategy.WRITE_THROUGH,
      ttl: 3600, // 1小时
      prefix: 'chat:',
      maxSize: 200, // 最多缓存200条消息
      refreshThreshold: 0.5,
      lockTimeout: 3,
      retryCount: 1,
      fallbackTtl: 300, // 5分钟降级TTL
    });

    // 统计数据缓存策略
    this.strategies.set('stats', {
      strategy: CacheStrategy.WRITE_BACK,
      ttl: 1800, // 30分钟
      prefix: 'stats:',
      refreshThreshold: 0.2,
      lockTimeout: 10,
      retryCount: 3,
      fallbackTtl: 300, // 5分钟降级TTL
    });

    Logger.info('缓存策略初始化完成', {
      strategiesCount: this.strategies.size,
      strategies: Array.from(this.strategies.keys()),
    });
  }

  /**
   * 获取缓存策略配置
   */
  public getStrategy(type: string): CacheStrategyConfig | null {
    return this.strategies.get(type) || null;
  }

  /**
   * 设置缓存策略配置
   */
  public setStrategy(type: string, config: CacheStrategyConfig): void {
    this.strategies.set(type, config);
    Logger.info('缓存策略已更新', { type, config });
  }

  /**
   * 获取所有缓存策略
   */
  public getAllStrategies(): Record<string, CacheStrategyConfig> {
    const result: Record<string, CacheStrategyConfig> = {};
    for (const [type, config] of this.strategies.entries()) {
      result[type] = config;
    }
    return result;
  }

  /**
   * 根据策略获取缓存
   */
  public async get<T>(type: string, key: string, fetcher?: () => Promise<T>): Promise<T | null> {
    const strategy = this.getStrategy(type);
    if (!strategy) {
      throw new Error(`未找到缓存策略: ${type}`);
    }

    const cacheKey = `${strategy.prefix}${key}`;
    const options: CacheOptions = {
      ttl: strategy.ttl,
      prefix: '',
    };

    try {
      switch (strategy.strategy) {
        case CacheStrategy.CACHE_ASIDE:
          return await this.cacheAsideGet(cacheKey, fetcher, options, strategy);
        
        case CacheStrategy.READ_THROUGH:
          return await this.readThroughGet(cacheKey, fetcher, options, strategy);
        
        case CacheStrategy.WRITE_THROUGH:
        case CacheStrategy.WRITE_BACK:
        case CacheStrategy.WRITE_AROUND:
          // 这些策略主要影响写操作，读操作直接从缓存获取
          return await this.cacheManager.get<T>(cacheKey, options);
        
        default:
          return await this.cacheManager.get<T>(cacheKey, options);
      }
    } catch (error) {
      Logger.error('缓存获取失败', { type, key, error });
      
      // 降级处理
      if (fetcher) {
        try {
          const value = await fetcher();
          // 使用降级TTL缓存
          if (value !== null && strategy.fallbackTtl) {
            await this.cacheManager.set(cacheKey, value, { 
              ...options, 
              ttl: strategy.fallbackTtl 
            });
          }
          return value;
        } catch (fetchError) {
          Logger.error('数据获取失败', { type, key, error: fetchError });
          return null;
        }
      }
      
      return null;
    }
  }

  /**
   * 根据策略设置缓存
   */
  public async set(type: string, key: string, value: any, persistFn?: (value: any) => Promise<void>): Promise<void> {
    const strategy = this.getStrategy(type);
    if (!strategy) {
      throw new Error(`未找到缓存策略: ${type}`);
    }

    const cacheKey = `${strategy.prefix}${key}`;
    const options: CacheOptions = {
      ttl: strategy.ttl,
      prefix: '',
    };

    try {
      switch (strategy.strategy) {
        case CacheStrategy.WRITE_THROUGH:
          await this.writeThroughSet(cacheKey, value, persistFn, options, strategy);
          break;
        
        case CacheStrategy.WRITE_BACK:
          await this.writeBackSet(cacheKey, value, persistFn, options, strategy);
          break;
        
        case CacheStrategy.WRITE_AROUND:
          await this.writeAroundSet(cacheKey, value, persistFn, options, strategy);
          break;
        
        case CacheStrategy.CACHE_ASIDE:
        case CacheStrategy.READ_THROUGH:
        default:
          // 直接设置缓存
          await this.cacheManager.set(cacheKey, value, options);
          break;
      }
    } catch (error) {
      Logger.error('缓存设置失败', { type, key, error });
      throw error;
    }
  }

  /**
   * 根据策略删除缓存
   */
  public async del(type: string, key: string): Promise<void> {
    const strategy = this.getStrategy(type);
    if (!strategy) {
      throw new Error(`未找到缓存策略: ${type}`);
    }

    const cacheKey = `${strategy.prefix}${key}`;
    const options: CacheOptions = { prefix: '' };

    try {
      await this.cacheManager.del(cacheKey, options);
      Logger.debug('缓存删除成功', { type, key: cacheKey });
    } catch (error) {
      Logger.error('缓存删除失败', { type, key, error });
      throw error;
    }
  }

  /**
   * Cache Aside 模式获取
   */
  private async cacheAsideGet<T>(
    key: string,
    fetcher?: () => Promise<T>,
    options?: CacheOptions,
    strategy?: CacheStrategyConfig
  ): Promise<T | null> {
    // 先从缓存获取
    let cached = await this.cacheManager.get<T>(key, options);

    if (cached !== null) {
      // 检查是否需要刷新
      if (strategy?.refreshThreshold && fetcher) {
        const ttl = await this.cacheManager.ttl(key, options);
        const refreshTime = (strategy.ttl * strategy.refreshThreshold);

        if (ttl > 0 && ttl < refreshTime) {
          // 异步刷新缓存
          this.refreshCacheAsync(key, fetcher, options, strategy);
        }
      }
      return cached;
    }

    // 缓存未命中，从数据源获取
    if (fetcher) {
      const lockKey = `refresh:${key}`;
      const lockValue = await this.cacheManager.lock(lockKey, strategy?.lockTimeout || 10);

      if (lockValue) {
        try {
          // 双重检查
          cached = await this.cacheManager.get<T>(key, options);
          if (cached !== null) {
            return cached;
          }

          const value = await fetcher();
          if (value !== null) {
            await this.cacheManager.set(key, value, options);
          }
          return value;
        } finally {
          await this.cacheManager.unlock(lockKey, lockValue);
        }
      } else {
        // 获取锁失败，等待一段时间后重试
        await this.sleep(100);
        return await this.cacheManager.get<T>(key, options);
      }
    }

    return null;
  }

  /**
   * Read Through 模式获取
   */
  private async readThroughGet<T>(
    key: string,
    fetcher?: () => Promise<T>,
    options?: CacheOptions,
    strategy?: CacheStrategyConfig
  ): Promise<T | null> {
    if (!fetcher) {
      return await this.cacheManager.get<T>(key, options);
    }

    return await this.cacheManager.getOrSet<T>(key, fetcher, options);
  }

  /**
   * Write Through 模式设置
   */
  private async writeThroughSet(
    key: string,
    value: any,
    persistFn?: (value: any) => Promise<void>,
    options?: CacheOptions,
    strategy?: CacheStrategyConfig
  ): Promise<void> {
    // 同时写入缓存和持久化存储
    const promises: Promise<void>[] = [
      this.cacheManager.set(key, value, options)
    ];

    if (persistFn) {
      promises.push(persistFn(value));
    }

    await Promise.all(promises);
  }

  /**
   * Write Back 模式设置
   */
  private async writeBackSet(
    key: string,
    value: any,
    persistFn?: (value: any) => Promise<void>,
    options?: CacheOptions,
    strategy?: CacheStrategyConfig
  ): Promise<void> {
    // 先写入缓存
    await this.cacheManager.set(key, value, options);

    // 标记为脏数据，稍后异步写入持久化存储
    if (persistFn) {
      const dirtyKey = `dirty:${key}`;
      await this.cacheManager.set(dirtyKey, { value, timestamp: Date.now() }, { ttl: strategy?.ttl });

      // 异步持久化
      this.persistDirtyDataAsync(key, value, persistFn, dirtyKey);
    }
  }

  /**
   * Write Around 模式设置
   */
  private async writeAroundSet(
    key: string,
    value: any,
    persistFn?: (value: any) => Promise<void>,
    options?: CacheOptions,
    strategy?: CacheStrategyConfig
  ): Promise<void> {
    // 只写入持久化存储，不写入缓存
    if (persistFn) {
      await persistFn(value);
    }

    // 可选：删除现有缓存以保持一致性
    try {
      await this.cacheManager.del(key, { prefix: '' });
    } catch (error) {
      // 忽略删除错误
    }
  }

  /**
   * 异步刷新缓存
   */
  private async refreshCacheAsync<T>(
    key: string,
    fetcher: () => Promise<T>,
    options?: CacheOptions,
    strategy?: CacheStrategyConfig
  ): Promise<void> {
    try {
      const value = await fetcher();
      if (value !== null) {
        await this.cacheManager.set(key, value, options);
        Logger.debug('缓存异步刷新成功', { key });
      }
    } catch (error) {
      Logger.error('缓存异步刷新失败', { key, error });
    }
  }

  /**
   * 异步持久化脏数据
   */
  private async persistDirtyDataAsync(
    key: string,
    value: any,
    persistFn: (value: any) => Promise<void>,
    dirtyKey: string
  ): Promise<void> {
    try {
      await persistFn(value);
      await this.cacheManager.del(dirtyKey, { prefix: '' });
      Logger.debug('脏数据持久化成功', { key });
    } catch (error) {
      Logger.error('脏数据持久化失败', { key, error });
    }
  }

  /**
   * 批量获取缓存
   */
  public async mget<T>(type: string, keys: string[]): Promise<(T | null)[]> {
    const strategy = this.getStrategy(type);
    if (!strategy) {
      throw new Error(`未找到缓存策略: ${type}`);
    }

    const cacheKeys = keys.map(key => `${strategy.prefix}${key}`);
    const options: CacheOptions = { prefix: '' };

    try {
      // 转换为新的mget格式
      const keyObjects = cacheKeys.map(key => ({ key, prefix: options.prefix }));
      return await this.cacheManager.mget<T>(keyObjects);
    } catch (error) {
      Logger.error('批量获取缓存失败', { type, keys, error });
      return keys.map(() => null);
    }
  }

  /**
   * 批量设置缓存
   */
  public async mset(type: string, keyValues: Record<string, any>): Promise<void> {
    const strategy = this.getStrategy(type);
    if (!strategy) {
      throw new Error(`未找到缓存策略: ${type}`);
    }

    const cacheKeyValues: Record<string, any> = {};
    for (const [key, value] of Object.entries(keyValues)) {
      cacheKeyValues[`${strategy.prefix}${key}`] = value;
    }

    const options: CacheOptions = {
      ttl: strategy.ttl,
      prefix: '',
    };

    try {
      // 安全验证：检查缓存类型和数据
      if (!this.validateCacheType(type)) {
        throw new Error(`无效的缓存类型: ${type}`);
      }

      // 数据安全检查
      const sanitizedKeyValues = this.sanitizeCacheData(keyValues);

      // 转换为新的mset格式，添加安全标识
      const keyValuePairs = Object.entries(sanitizedKeyValues).map(([key, value]) => ({
        key: this.generateSecureKey(key, type),
        value: this.encryptSensitiveData(value, type),
        ttl: this.getOptimalTTL(type, options.ttl),
        prefix: options.prefix,
        metadata: {
          type,
          timestamp: Date.now(),
          checksum: this.calculateChecksum(value)
        }
      }));

      await this.cacheManager.mset(keyValuePairs);

      // 记录缓存操作日志
      Logger.info('批量缓存设置成功', {
        type,
        count: keyValuePairs.length,
        totalSize: this.calculateDataSize(keyValuePairs)
      });

    } catch (error) {
      Logger.error('批量设置缓存失败', { type, keyValues, error });
      throw error;
    }
  }

  /**
   * 清理过期缓存
   */
  public async cleanup(): Promise<void> {
    try {
      Logger.info('开始清理过期缓存');

      for (const [type, strategy] of this.strategies.entries()) {
        try {
          const pattern = `${strategy.prefix}*`;
          const keys = await this.cacheManager.keys(pattern, { prefix: '' });

          let expiredCount = 0;
          for (const key of keys) {
            const ttl = await this.cacheManager.ttl(key, { prefix: '' });
            if (ttl === -1) { // 没有过期时间的键
              await this.cacheManager.expire(key, strategy.ttl, { prefix: '' });
            } else if (ttl === -2) { // 已过期的键
              expiredCount++;
            }
          }

          Logger.debug('缓存策略清理完成', {
            type,
            totalKeys: keys.length,
            expiredCount,
          });

        } catch (error) {
          Logger.error('缓存策略清理失败', { type, error });
        }
      }

      Logger.info('过期缓存清理完成');

    } catch (error) {
      Logger.error('缓存清理失败', error);
      throw error;
    }
  }

  /**
   * 获取缓存统计信息
   */
  public async getStats(): Promise<any> {
    try {
      const stats = this.cacheManager.getStats();
      const strategiesStats: Record<string, any> = {};

      for (const [type, strategy] of this.strategies.entries()) {
        const pattern = `${strategy.prefix}*`;
        const keys = await this.cacheManager.keys(pattern, { prefix: '' });

        strategiesStats[type] = {
          strategy: strategy.strategy,
          ttl: strategy.ttl,
          keyCount: keys.length,
          prefix: strategy.prefix,
        };
      }

      return {
        overall: stats,
        strategies: strategiesStats,
        totalStrategies: this.strategies.size,
      };

    } catch (error) {
      Logger.error('获取缓存统计失败', error);
      throw error;
    }
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
