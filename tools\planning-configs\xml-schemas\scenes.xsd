<?xml version="1.0" encoding="UTF-8"?>
<!-- Copyright (c) 2025 "放置". All rights reserved. -->
<!-- 场景配置XML Schema定义 -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">

  <!-- 根元素：游戏场景集合 -->
  <xs:element name="GameScenes">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="Scene" type="SceneType" maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attribute name="version" type="xs:string" use="required"/>
      <xs:attribute name="lastUpdated" type="xs:dateTime" use="required"/>
    </xs:complexType>
  </xs:element>

  <!-- 场景类型定义 -->
  <xs:complexType name="SceneType">
    <xs:sequence>
      <xs:element name="BasicInfo" type="BasicInfoType"/>
      <xs:element name="UnlockConditions" type="UnlockConditionsType"/>
      <xs:element name="MapConfig" type="MapConfigType"/>
      <xs:element name="Environment" type="EnvironmentType"/>
      <xs:element name="Rewards" type="RewardsType"/>
      <xs:element name="Behaviors" type="BehaviorsType"/>
      <xs:element name="UILayout" type="UILayoutType"/>
    </xs:sequence>
    <xs:attribute name="id" type="xs:string" use="required"/>
  </xs:complexType>

  <!-- 基础信息类型 -->
  <xs:complexType name="BasicInfoType">
    <xs:sequence>
      <xs:element name="Name" type="xs:string"/>
      <xs:element name="Description" type="xs:string"/>
      <xs:element name="Type" type="xs:string"/>
      <xs:element name="Level" type="xs:positiveInteger"/>
    </xs:sequence>
  </xs:complexType>

  <!-- 解锁条件类型 -->
  <xs:complexType name="UnlockConditionsType">
    <xs:sequence>
      <xs:element name="PlayerLevel" type="xs:positiveInteger"/>
      <xs:element name="RequiredItems" type="StringListType"/>
      <xs:element name="CompletedQuests" type="QuestListType"/>
      <xs:element name="RequiredAreas" type="AreaListType"/>
    </xs:sequence>
  </xs:complexType>

  <!-- 地图配置类型 -->
  <xs:complexType name="MapConfigType">
    <xs:sequence>
      <xs:element name="BackgroundImagePath" type="xs:string"/>
      <xs:element name="MapSize" type="SizeType"/>
      <xs:element name="NodeSpacing" type="PositionType"/>
      <xs:element name="StartPosition" type="PositionType"/>
      <xs:element name="BranchCount" type="xs:positiveInteger"/>
      <xs:element name="NodesPerBranch" type="xs:positiveInteger"/>
    </xs:sequence>
  </xs:complexType>

  <!-- 环境配置类型 -->
  <xs:complexType name="EnvironmentType">
    <xs:sequence>
      <xs:element name="Weather" type="xs:string"/>
      <xs:element name="TimeOfDay" type="xs:string"/>
      <xs:element name="BackgroundMusic" type="xs:string"/>
      <xs:element name="AmbientSounds" type="SoundListType"/>
      <xs:element name="LightingColor" type="ColorType"/>
    </xs:sequence>
  </xs:complexType>

  <!-- 奖励配置类型 -->
  <xs:complexType name="RewardsType">
    <xs:sequence>
      <xs:element name="BaseExp" type="xs:nonNegativeInteger"/>
      <xs:element name="BaseGold" type="xs:nonNegativeInteger"/>
      <xs:element name="DropTables" type="DropTableListType"/>
    </xs:sequence>
  </xs:complexType>

  <!-- 行为集合类型 -->
  <xs:complexType name="BehaviorsType">
    <xs:sequence>
      <xs:element name="Behavior" type="BehaviorType" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <!-- 行为类型 -->
  <xs:complexType name="BehaviorType">
    <xs:sequence>
      <xs:element name="Name" type="xs:string"/>
      <xs:element name="Description" type="xs:string"/>
      <xs:element name="Type" type="xs:string"/>
      <xs:element name="Duration" type="xs:positiveInteger"/>
      <xs:element name="Cooldown" type="xs:nonNegativeInteger"/>
      <xs:element name="EnergyCost" type="xs:nonNegativeInteger"/>
      <xs:element name="Requirements" type="RequirementsType"/>
      <xs:element name="Rewards" type="BehaviorRewardsType"/>
    </xs:sequence>
    <xs:attribute name="id" type="xs:string" use="required"/>
  </xs:complexType>

  <!-- 需求类型 -->
  <xs:complexType name="RequirementsType">
    <xs:sequence>
      <xs:element name="Items" type="StringListType"/>
      <xs:element name="Skills" type="StringListType"/>
      <xs:element name="Level" type="xs:positiveInteger"/>
    </xs:sequence>
  </xs:complexType>

  <!-- 行为奖励类型 -->
  <xs:complexType name="BehaviorRewardsType">
    <xs:sequence>
      <xs:element name="BaseExp" type="xs:nonNegativeInteger"/>
      <xs:element name="Items" type="ItemRewardListType"/>
      <xs:element name="Currency" type="CurrencyType"/>
    </xs:sequence>
  </xs:complexType>

  <!-- UI布局类型 -->
  <xs:complexType name="UILayoutType">
    <xs:sequence>
      <xs:element name="BehaviorButtonsPosition" type="PositionType"/>
      <xs:element name="InfoDisplayPosition" type="PositionType"/>
      <xs:element name="CustomElements" type="CustomElementListType"/>
    </xs:sequence>
  </xs:complexType>

  <!-- 基础数据类型定义 -->
  <xs:complexType name="SizeType">
    <xs:attribute name="width" type="xs:positiveInteger" use="required"/>
    <xs:attribute name="height" type="xs:positiveInteger" use="required"/>
  </xs:complexType>

  <xs:complexType name="PositionType">
    <xs:attribute name="x" type="xs:integer" use="required"/>
    <xs:attribute name="y" type="xs:integer" use="required"/>
  </xs:complexType>

  <xs:complexType name="ColorType">
    <xs:attribute name="r" type="xs:unsignedByte" use="required"/>
    <xs:attribute name="g" type="xs:unsignedByte" use="required"/>
    <xs:attribute name="b" type="xs:unsignedByte" use="required"/>
    <xs:attribute name="a" type="xs:unsignedByte" use="required"/>
  </xs:complexType>

  <!-- 列表类型定义 -->
  <xs:complexType name="StringListType">
    <xs:sequence>
      <xs:element name="Item" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="QuestListType">
    <xs:sequence>
      <xs:element name="Quest" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="AreaListType">
    <xs:sequence>
      <xs:element name="Area" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="SoundListType">
    <xs:sequence>
      <xs:element name="Sound" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="DropTableListType">
    <xs:sequence>
      <xs:element name="DropTable" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="ItemRewardListType">
    <xs:sequence>
      <xs:element name="Item" minOccurs="0" maxOccurs="unbounded">
        <xs:complexType>
          <xs:attribute name="id" type="xs:string" use="required"/>
          <xs:attribute name="probability" type="xs:decimal" use="required"/>
          <xs:attribute name="minQuantity" type="xs:positiveInteger" use="required"/>
          <xs:attribute name="maxQuantity" type="xs:positiveInteger" use="required"/>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="CurrencyType">
    <xs:sequence>
      <xs:element name="Gold" minOccurs="0">
        <xs:complexType>
          <xs:attribute name="min" type="xs:nonNegativeInteger" use="required"/>
          <xs:attribute name="max" type="xs:nonNegativeInteger" use="required"/>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="CustomElementListType">
    <xs:sequence>
      <xs:element name="Element" minOccurs="0" maxOccurs="unbounded">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Config" minOccurs="0">
              <xs:complexType>
                <xs:anyAttribute processContents="skip"/>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
          <xs:attribute name="type" type="xs:string" use="required"/>
          <xs:attribute name="x" type="xs:integer" use="required"/>
          <xs:attribute name="y" type="xs:integer" use="required"/>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>

</xs:schema>
