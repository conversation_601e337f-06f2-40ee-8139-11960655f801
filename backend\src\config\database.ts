import mongoose = require('mongoose');
import { Logger } from '../utils/logger';
import { envConfig } from './environment';

/**
 * 数据库连接状态枚举
 */
export enum DatabaseConnectionState {
  DISCONNECTED = 0,
  CONNECTED = 1,
  CONNECTING = 2,
  DISCONNECTING = 3,
}

/**
 * 数据库配置接口
 */
export interface DatabaseConfigOptions {
  uri: string;
  maxPoolSize?: number;
  minPoolSize?: number;        // 添加最小连接池大小配置
  serverSelectionTimeoutMS?: number;
  socketTimeoutMS?: number;
  connectTimeoutMS?: number;
  heartbeatFrequencyMS?: number;
  maxIdleTimeMS?: number;
  retryWrites?: boolean;
  retryReads?: boolean;
  readPreference?: 'primary' | 'primaryPreferred' | 'secondary' | 'secondaryPreferred' | 'nearest';
  writeConcern?: {
    w?: string | number;
    j?: boolean;
    wtimeout?: number;
  };
}

/**
 * 数据库统计信息接口
 */
export interface DatabaseStats {
  connectionState: DatabaseConnectionState;
  connectedAt: Date | undefined;
  lastPingAt: Date | undefined;
  totalConnections: number;
  activeConnections: number;
  reconnectCount: number;
  errorCount: number;
}

export class DatabaseConfig {
    private static instance: DatabaseConfig;
    private isConnected: boolean = false;
    private connectedAt: Date | undefined;
    private lastPingAt: Date | undefined;
    private reconnectCount: number = 0;
    private errorCount: number = 0;
    private connectionOptions: DatabaseConfigOptions;
    private healthCheckInterval: NodeJS.Timeout | undefined;
    private reconnectTimeout: NodeJS.Timeout | undefined;

    private constructor() {
        this.connectionOptions = this.getDefaultOptions();
        this.setupEventListeners();
    }

    public static getInstance(): DatabaseConfig {
        if (!DatabaseConfig.instance) {
            DatabaseConfig.instance = new DatabaseConfig();
        }
        return DatabaseConfig.instance;
    }

    /**
     * 获取默认连接选项
     */
    private getDefaultOptions(): DatabaseConfigOptions {
        const config = envConfig.getConfig();

        return {
            uri: config.mongodbUri,
            // 优化连接池配置以提升并发性能
            maxPoolSize: parseInt(process.env['MONGODB_MAX_POOL_SIZE'] || '20'),        // 增加到20个连接
            minPoolSize: parseInt(process.env['MONGODB_MIN_POOL_SIZE'] || '5'),         // 最小5个连接
            serverSelectionTimeoutMS: parseInt(process.env['MONGODB_SERVER_SELECTION_TIMEOUT'] || '5000'),
            socketTimeoutMS: parseInt(process.env['MONGODB_SOCKET_TIMEOUT'] || '45000'),
            connectTimeoutMS: parseInt(process.env['MONGODB_CONNECT_TIMEOUT'] || '10000'),
            heartbeatFrequencyMS: parseInt(process.env['MONGODB_HEARTBEAT_FREQUENCY'] || '10000'),
            maxIdleTimeMS: parseInt(process.env['MONGODB_MAX_IDLE_TIME'] || '30000'),    // 减少到30秒空闲超时
            retryWrites: process.env['MONGODB_RETRY_WRITES'] !== 'false',
            retryReads: process.env['MONGODB_RETRY_READS'] !== 'false',
            readPreference: (process.env['MONGODB_READ_PREFERENCE'] as 'primary' | 'primaryPreferred' | 'secondary' | 'secondaryPreferred' | 'nearest') || 'primary',
            writeConcern: {
                w: process.env['MONGODB_WRITE_CONCERN_W'] || 'majority',
                j: process.env['MONGODB_WRITE_CONCERN_J'] !== 'false',
                wtimeout: parseInt(process.env['MONGODB_WRITE_CONCERN_TIMEOUT'] || '5000'),
            },
        };
    }

    /**
     * 设置事件监听器
     */
    private setupEventListeners(): void {
        // 进程退出时清理
        process.on('SIGINT', this.gracefulShutdown.bind(this));
        process.on('SIGTERM', this.gracefulShutdown.bind(this));
        process.on('SIGUSR2', this.gracefulShutdown.bind(this)); // nodemon restart
    }

    /**
     * 连接到MongoDB数据库
     */
    public async connect(options?: Partial<DatabaseConfigOptions>): Promise<void> {
        try {
            if (this.isConnected) {
                Logger.info('数据库已经连接');
                return;
            }

            // 合并配置选项
            if (options) {
                this.connectionOptions = { ...this.connectionOptions, ...options };
            }

            const { uri, ...mongooseOptions } = this.connectionOptions;

            Logger.info('正在连接MongoDB数据库...', {
                uri: this.maskUri(uri),
                options: {
                    maxPoolSize: mongooseOptions.maxPoolSize,
                    serverSelectionTimeoutMS: mongooseOptions.serverSelectionTimeoutMS,
                    socketTimeoutMS: mongooseOptions.socketTimeoutMS,
                }
            });

            // 设置Mongoose全局选项 - 优化性能
            mongoose.set('strictQuery', true);
            mongoose.set('bufferCommands', false);      // 禁用命令缓冲，立即失败
            // mongoose.set('bufferMaxEntries', 0);        // 此选项在新版本中已移除

            await mongoose.connect(uri, mongooseOptions as any);

            this.isConnected = true;
            this.connectedAt = new Date();
            Logger.info('MongoDB数据库连接成功', {
                uri: this.maskUri(uri),
                connectedAt: this.connectedAt,
                readyState: mongoose.connection.readyState
            });

            // 设置连接事件监听器
            this.setupConnectionEventListeners();

            // 启动健康检查
            this.startHealthCheck();

        } catch (error) {
            Logger.error('MongoDB连接失败', error);
            this.isConnected = false;
            this.errorCount++;
            throw error;
        }
    }

    /**
     * 设置连接事件监听器
     */
    private setupConnectionEventListeners(): void {
        mongoose.connection.on('error', (error) => {
            Logger.error('MongoDB连接错误', error);
            this.isConnected = false;
            this.errorCount++;
            this.scheduleReconnect();
        });

        mongoose.connection.on('disconnected', () => {
            Logger.warn('MongoDB连接断开');
            this.isConnected = false;
            this.scheduleReconnect();
        });

        mongoose.connection.on('reconnected', () => {
            Logger.info('MongoDB重新连接成功');
            this.isConnected = true;
            this.reconnectCount++;
            this.connectedAt = new Date();
        });

        mongoose.connection.on('close', () => {
            Logger.info('MongoDB连接已关闭');
            this.isConnected = false;
        });

        mongoose.connection.on('fullsetup', () => {
            Logger.info('MongoDB副本集连接完成');
        });
    }

    /**
     * 计划重连
     */
    private scheduleReconnect(): void {
        if (this.reconnectTimeout) {
            clearTimeout(this.reconnectTimeout);
        }

        const delay = Math.min(1000 * Math.pow(2, this.reconnectCount), 30000); // 指数退避，最大30秒
        Logger.info(`计划在 ${delay}ms 后重新连接数据库`);

        this.reconnectTimeout = setTimeout(async () => {
            try {
                Logger.info('尝试重新连接数据库...');
                await this.connect();
            } catch (error) {
                Logger.error('重新连接失败', error);
            }
        }, delay);
    }

    /**
     * 断开数据库连接
     */
    public async disconnect(): Promise<void> {
        try {
            if (!this.isConnected) {
                Logger.info('数据库未连接，无需断开');
                return;
            }

            // 清理定时器
            this.stopHealthCheck();
            if (this.reconnectTimeout) {
                clearTimeout(this.reconnectTimeout);
                this.reconnectTimeout = undefined;
            }

            await mongoose.disconnect();
            this.isConnected = false;
            this.connectedAt = undefined;
            Logger.info('MongoDB数据库连接已断开');
        } catch (error) {
            Logger.error('断开MongoDB连接失败', error);
            throw error;
        }
    }

    /**
     * 获取连接状态
     */
    public getConnectionStatus(): boolean {
        return this.isConnected && mongoose.connection.readyState === 1;
    }

    /**
     * 获取详细连接状态
     */
    public getConnectionState(): DatabaseConnectionState {
        return mongoose.connection.readyState as unknown as DatabaseConnectionState;
    }

    /**
     * 获取数据库连接实例
     */
    public getConnection(): typeof mongoose.connection {
        return mongoose.connection;
    }

    /**
     * 获取数据库统计信息
     */
    public getStats(): DatabaseStats {
        return {
            connectionState: this.getConnectionState(),
            connectedAt: this.connectedAt,
            lastPingAt: this.lastPingAt,
            totalConnections: 1, // 单例模式，总是1
            activeConnections: this.isConnected ? 1 : 0,
            reconnectCount: this.reconnectCount,
            errorCount: this.errorCount,
        };
    }

    /**
     * 健康检查
     */
    public async healthCheck(): Promise<boolean> {
        try {
            if (!this.isConnected) {
                return false;
            }

            // 执行简单的ping操作
            await mongoose.connection.db?.admin().ping();
            this.lastPingAt = new Date();
            return true;
        } catch (error) {
            Logger.error('数据库健康检查失败', error);
            this.errorCount++;
            return false;
        }
    }

    /**
     * 启动定期健康检查
     */
    private startHealthCheck(): void {
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
        }

        const interval = parseInt(process.env['MONGODB_HEALTH_CHECK_INTERVAL'] || '30000'); // 30秒
        this.healthCheckInterval = setInterval(async () => {
            const isHealthy = await this.healthCheck();
            if (!isHealthy) {
                Logger.warn('数据库健康检查失败，可能需要重连');
            }
        }, interval);
    }

    /**
     * 停止健康检查
     */
    private stopHealthCheck(): void {
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
            this.healthCheckInterval = undefined;
        }
    }
    /**
     * 掩码URI中的敏感信息
     */
    private maskUri(uri: string): string {
        return uri.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@');
    }

    /**
     * 优雅关闭
     */
    private async gracefulShutdown(): Promise<void> {
        Logger.info('收到关闭信号，正在优雅关闭数据库连接...');
        try {
            await this.disconnect();
            process.exit(0);
        } catch (error) {
            Logger.error('优雅关闭数据库连接失败', error);
            process.exit(1);
        }
    }

    /**
     * 获取数据库信息
     */
    public async getDatabaseInfo(): Promise<any> {
        try {
            if (!this.isConnected) {
                throw new Error('数据库未连接');
            }

            const admin = mongoose.connection.db?.admin();
            const [serverStatus, dbStats] = await Promise.all([
                admin?.serverStatus(),
                mongoose.connection.db?.stats(),
            ]);

            return {
                serverStatus: {
                    version: serverStatus?.['version'],
                    uptime: serverStatus?.['uptime'],
                    connections: serverStatus?.['connections'],
                },
                dbStats: {
                    db: dbStats?.['db'],
                    collections: dbStats?.['collections'],
                    objects: dbStats?.['objects'],
                    dataSize: dbStats?.['dataSize'],
                    storageSize: dbStats?.['storageSize'],
                    indexes: dbStats?.['indexes'],
                    indexSize: dbStats?.['indexSize'],
                },
                connectionInfo: this.getStats(),
            };
        } catch (error) {
            Logger.error('获取数据库信息失败', error);
            throw error;
        }
    }

    /**
     * 测试数据库连接
     */
    public async testConnection(): Promise<boolean> {
        try {
            const startTime = Date.now();
            const isHealthy = await this.healthCheck();
            const duration = Date.now() - startTime;

            Logger.info('数据库连接测试完成', {
                isHealthy,
                duration: `${duration}ms`,
                connectionState: this.getConnectionState(),
            });

            return isHealthy;
        } catch (error) {
            Logger.error('数据库连接测试失败', error);
            return false;
        }
    }
}
