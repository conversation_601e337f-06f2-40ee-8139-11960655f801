/**
 * 修复MultiEffectButton缺少Button组件的问题
 * 这个工具脚本会自动检查所有有MultiEffectButton组件但缺少Button组件的节点
 * 并为它们添加Button组件
 */

import { _decorator, Component, Node, find, Button } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('FixMultiEffectButtons')
export class FixMultiEffectButtons extends Component {
    
    @property({ tooltip: '是否自动修复' })
    public autoFix: boolean = true;
    
    @property({ tooltip: '是否显示详细日志' })
    public verbose: boolean = true;

    protected onLoad(): void {
        console.log('🔧 FixMultiEffectButtons: 工具脚本加载');
    }

    protected start(): void {
        if (this.autoFix) {
            this.scheduleOnce(() => {
                this.fixAllMultiEffectButtons();
            }, 1.0); // 延迟1秒执行，确保场景完全加载
        }
    }

    /**
     * 修复所有MultiEffectButton节点
     */
    public fixAllMultiEffectButtons(): void {
        console.log('🔧 开始检查和修复MultiEffectButton节点...');
        
        const canvas = find('Canvas');
        if (!canvas) {
            console.error('🔧 未找到Canvas节点');
            return;
        }
        
        const problemNodes: Node[] = [];
        const fixedNodes: Node[] = [];
        
        // 递归检查所有节点
        this.checkNodeRecursively(canvas, problemNodes, fixedNodes);
        
        // 输出结果
        console.log('🔧 ========== 修复结果 ==========');
        console.log(`🔧 发现问题节点: ${problemNodes.length}`);
        console.log(`🔧 成功修复节点: ${fixedNodes.length}`);
        
        if (this.verbose) {
            problemNodes.forEach((node, index) => {
                console.log(`🔧 ${index + 1}. ${this.getNodePath(node)}`);
            });
        }
        
        if (fixedNodes.length > 0) {
            console.log('✅ 所有MultiEffectButton节点已修复完成！');
        } else if (problemNodes.length === 0) {
            console.log('✅ 没有发现需要修复的节点');
        }
        
        console.log('🔧 ===============================');
    }

    /**
     * 递归检查节点
     */
    private checkNodeRecursively(node: Node, problemNodes: Node[], fixedNodes: Node[]): void {
        // 检查当前节点
        this.checkSingleNode(node, problemNodes, fixedNodes);
        
        // 递归检查子节点
        for (let i = 0; i < node.children.length; i++) {
            this.checkNodeRecursively(node.children[i], problemNodes, fixedNodes);
        }
    }

    /**
     * 检查单个节点
     */
    private checkSingleNode(node: Node, problemNodes: Node[], fixedNodes: Node[]): void {
        // 检查是否有MultiEffectButton组件
        const multiEffectButton = node.getComponent('MultiEffectButton');
        if (!multiEffectButton) {
            return; // 没有MultiEffectButton组件，跳过
        }
        
        // 检查是否有Button组件
        const button = node.getComponent(Button);
        if (button) {
            if (this.verbose) {
                console.log(`✅ 节点正常: ${this.getNodePath(node)}`);
            }
            return; // 已经有Button组件，正常
        }
        
        // 发现问题节点
        problemNodes.push(node);
        
        if (this.verbose) {
            console.log(`❌ 发现问题节点: ${this.getNodePath(node)}`);
        }
        
        // 尝试修复
        if (this.fixNode(node)) {
            fixedNodes.push(node);
            if (this.verbose) {
                console.log(`🔧 修复成功: ${this.getNodePath(node)}`);
            }
        } else {
            console.error(`❌ 修复失败: ${this.getNodePath(node)}`);
        }
    }

    /**
     * 修复单个节点
     */
    private fixNode(node: Node): boolean {
        try {
            // 添加Button组件
            const button = node.addComponent(Button);
            
            if (button) {
                // 设置Button的基本属性
                button.transition = Button.Transition.NONE; // 禁用Button自带的过渡效果，使用MultiEffectButton的效果
                button.interactable = true;
                
                // 设置target为自身
                button.target = node;
                
                return true;
            }
        } catch (error) {
            console.error(`🔧 修复节点时出错: ${this.getNodePath(node)}`, error);
        }
        
        return false;
    }

    /**
     * 获取节点路径
     */
    private getNodePath(node: Node): string {
        const path: string[] = [];
        let current = node;
        
        while (current && current.name !== 'Scene') {
            path.unshift(current.name);
            current = current.parent;
        }
        
        return path.join('/');
    }

    /**
     * 手动触发修复（可以在编辑器中调用）
     */
    public manualFix(): void {
        console.log('🔧 手动触发修复...');
        this.fixAllMultiEffectButtons();
    }

    /**
     * 只检查不修复
     */
    public checkOnly(): void {
        console.log('🔧 开始检查MultiEffectButton节点（仅检查，不修复）...');
        
        const canvas = find('Canvas');
        if (!canvas) {
            console.error('🔧 未找到Canvas节点');
            return;
        }
        
        const problemNodes: Node[] = [];
        const normalNodes: Node[] = [];
        
        // 递归检查所有节点
        this.checkNodeRecursivelyReadOnly(canvas, problemNodes, normalNodes);
        
        // 输出结果
        console.log('🔧 ========== 检查结果 ==========');
        console.log(`✅ 正常节点: ${normalNodes.length}`);
        console.log(`❌ 问题节点: ${problemNodes.length}`);
        
        if (problemNodes.length > 0) {
            console.log('🔧 需要修复的节点列表:');
            problemNodes.forEach((node, index) => {
                console.log(`🔧 ${index + 1}. ${this.getNodePath(node)}`);
            });
        }
        
        console.log('🔧 ===============================');
    }

    /**
     * 递归检查节点（只读模式）
     */
    private checkNodeRecursivelyReadOnly(node: Node, problemNodes: Node[], normalNodes: Node[]): void {
        // 检查当前节点
        const multiEffectButton = node.getComponent('MultiEffectButton');
        if (multiEffectButton) {
            const button = node.getComponent(Button);
            if (button) {
                normalNodes.push(node);
            } else {
                problemNodes.push(node);
            }
        }
        
        // 递归检查子节点
        for (let i = 0; i < node.children.length; i++) {
            this.checkNodeRecursivelyReadOnly(node.children[i], problemNodes, normalNodes);
        }
    }

    /**
     * 获取统计信息
     */
    public getStatistics(): { total: number, normal: number, problem: number } {
        const canvas = find('Canvas');
        if (!canvas) {
            return { total: 0, normal: 0, problem: 0 };
        }
        
        const problemNodes: Node[] = [];
        const normalNodes: Node[] = [];
        
        this.checkNodeRecursivelyReadOnly(canvas, problemNodes, normalNodes);
        
        return {
            total: normalNodes.length + problemNodes.length,
            normal: normalNodes.length,
            problem: problemNodes.length
        };
    }
}
