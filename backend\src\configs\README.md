# Copyright (c) 2025 "放置". All rights reserved.

# 后端配置表结构说明

## 目录结构
```
backend/src/configs/
├── numerical/          # 数值配置
│   ├── character/      # 角色数值配置
│   ├── equipment/      # 装备数值配置
│   ├── skills/         # 技能数值配置
│   └── battle/         # 战斗数值配置
├── economic/           # 经济配置
│   ├── currency/       # 货币配置
│   ├── shop/           # 商店配置
│   ├── rewards/        # 奖励配置
│   └── pricing/        # 定价配置
├── gameplay/           # 游戏玩法配置
│   ├── idle/           # 挂机配置
│   ├── adventure/      # 冒险配置
│   ├── quests/         # 任务配置
│   └── achievements/   # 成就配置
├── balance/            # 平衡性配置
│   ├── formulas/       # 计算公式
│   ├── curves/         # 成长曲线
│   └── limits/         # 数值限制
└── security/           # 安全配置
    ├── validation/     # 数据验证
    ├── anti_cheat/     # 防作弊
    └── rate_limits/    # 频率限制
```

## 配置表设计原则
1. **数据安全**：所有敏感数值和计算公式都在后端
2. **防作弊**：关键数据验证和反作弊机制
3. **平衡性**：统一的数值平衡和成长曲线管理
4. **可扩展**：支持动态配置和热更新
5. **性能优化**：合理的缓存策略和数据结构

## 配置文件规范
- 所有配置文件使用TypeScript接口定义
- 配置数据使用JSON格式存储
- 每个配置文件包含版本号和校验信息
- 支持环境变量和动态配置

## 安全注意事项
- 敏感配置不得暴露给前端
- 所有数值计算必须在后端验证
- 配置更新需要权限验证
- 定期备份配置数据
