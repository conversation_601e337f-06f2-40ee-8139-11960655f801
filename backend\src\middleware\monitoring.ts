import { Request, Response, NextFunction } from 'express';
import { Logger } from '../utils/logger';
import promClient from 'prom-client';

/**
 * Prometheus指标收集器
 */
class MetricsCollector {
    private static instance: MetricsCollector;
    
    // HTTP请求指标
    public httpRequestsTotal: promClient.Counter<string>;
    public httpRequestDuration: promClient.Histogram<string>;
    public httpRequestSize: promClient.Histogram<string>;
    public httpResponseSize: promClient.Histogram<string>;
    
    // 业务指标
    public activeUsers: promClient.Gauge<string>;
    public gameActions: promClient.Counter<string>;
    public databaseOperations: promClient.Counter<string>;
    public cacheOperations: promClient.Counter<string>;
    
    // 系统指标
    public memoryUsage: promClient.Gauge<string>;
    public cpuUsage: promClient.Gauge<string>;
    public errorRate: promClient.Counter<string>;

    private constructor() {
        // 创建默认指标收集器
        promClient.collectDefaultMetrics({
            timeout: 5000,
            gcDurationBuckets: [0.001, 0.01, 0.1, 1, 2, 5],
        });

        // HTTP请求总数
        this.httpRequestsTotal = new promClient.Counter({
            name: 'http_requests_total',
            help: 'Total number of HTTP requests',
            labelNames: ['method', 'route', 'status_code'],
        });

        // HTTP请求持续时间
        this.httpRequestDuration = new promClient.Histogram({
            name: 'http_request_duration_seconds',
            help: 'Duration of HTTP requests in seconds',
            labelNames: ['method', 'route', 'status_code'],
            buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10],
        });

        // HTTP请求大小
        this.httpRequestSize = new promClient.Histogram({
            name: 'http_request_size_bytes',
            help: 'Size of HTTP requests in bytes',
            labelNames: ['method', 'route'],
            buckets: [100, 1000, 10000, 100000, 1000000],
        });

        // HTTP响应大小
        this.httpResponseSize = new promClient.Histogram({
            name: 'http_response_size_bytes',
            help: 'Size of HTTP responses in bytes',
            labelNames: ['method', 'route', 'status_code'],
            buckets: [100, 1000, 10000, 100000, 1000000],
        });

        // 活跃用户数
        this.activeUsers = new promClient.Gauge({
            name: 'active_users_total',
            help: 'Number of active users',
            labelNames: ['type'],
        });

        // 游戏行为计数
        this.gameActions = new promClient.Counter({
            name: 'game_actions_total',
            help: 'Total number of game actions',
            labelNames: ['action_type', 'user_id'],
        });

        // 数据库操作计数
        this.databaseOperations = new promClient.Counter({
            name: 'database_operations_total',
            help: 'Total number of database operations',
            labelNames: ['operation', 'collection', 'status'],
        });

        // 缓存操作计数
        this.cacheOperations = new promClient.Counter({
            name: 'cache_operations_total',
            help: 'Total number of cache operations',
            labelNames: ['operation', 'status'],
        });

        // 内存使用
        this.memoryUsage = new promClient.Gauge({
            name: 'memory_usage_bytes',
            help: 'Memory usage in bytes',
            labelNames: ['type'],
        });

        // CPU使用率
        this.cpuUsage = new promClient.Gauge({
            name: 'cpu_usage_percent',
            help: 'CPU usage percentage',
        });

        // 错误率
        this.errorRate = new promClient.Counter({
            name: 'errors_total',
            help: 'Total number of errors',
            labelNames: ['type', 'severity'],
        });

        // 定期更新系统指标
        this.startSystemMetricsCollection();
    }

    public static getInstance(): MetricsCollector {
        if (!MetricsCollector.instance) {
            MetricsCollector.instance = new MetricsCollector();
        }
        return MetricsCollector.instance;
    }

    /**
     * 开始系统指标收集
     */
    private startSystemMetricsCollection(): void {
        setInterval(() => {
            const memUsage = process.memoryUsage();
            this.memoryUsage.set({ type: 'rss' }, memUsage.rss);
            this.memoryUsage.set({ type: 'heapUsed' }, memUsage.heapUsed);
            this.memoryUsage.set({ type: 'heapTotal' }, memUsage.heapTotal);
            this.memoryUsage.set({ type: 'external' }, memUsage.external);

            // CPU使用率（简化版本）
            const cpuUsage = process.cpuUsage();
            this.cpuUsage.set((cpuUsage.user + cpuUsage.system) / 1000000);
        }, 10000); // 每10秒更新一次
    }

    /**
     * 获取所有指标
     */
    public async getMetrics(): Promise<string> {
        return promClient.register.metrics();
    }

    /**
     * 清除所有指标
     */
    public clearMetrics(): void {
        promClient.register.clear();
    }
}

// 单例实例
export const metricsCollector = MetricsCollector.getInstance();

/**
 * HTTP请求监控中间件
 */
export function httpMetricsMiddleware(req: Request, res: Response, next: NextFunction): void {
    const startTime = Date.now();
    const startUsage = process.hrtime();

    // 记录请求大小
    const requestSize = parseInt(req.get('content-length') || '0', 10);
    if (requestSize > 0) {
        metricsCollector.httpRequestSize.observe(
            { method: req.method, route: req.route?.path || req.path },
            requestSize
        );
    }

    // 监听响应完成
    res.on('finish', () => {
        const duration = Date.now() - startTime;
        const hrDuration = process.hrtime(startUsage);
        const durationInSeconds = hrDuration[0] + hrDuration[1] / 1e9;

        const labels = {
            method: req.method,
            route: req.route?.path || req.path,
            status_code: res.statusCode.toString(),
        };

        // 记录指标
        metricsCollector.httpRequestsTotal.inc(labels);
        metricsCollector.httpRequestDuration.observe(labels, durationInSeconds);

        // 记录响应大小
        const responseSize = parseInt(res.get('content-length') || '0', 10);
        if (responseSize > 0) {
            metricsCollector.httpResponseSize.observe(labels, responseSize);
        }

        // 记录错误
        if (res.statusCode >= 400) {
            metricsCollector.errorRate.inc({
                type: 'http_error',
                severity: res.statusCode >= 500 ? 'error' : 'warning',
            });
        }

        // 记录日志
        Logger.info('HTTP Request', {
            method: req.method,
            url: req.url,
            statusCode: res.statusCode,
            duration: `${duration}ms`,
            userAgent: req.get('User-Agent'),
            ip: req.ip,
        });
    });

    next();
}

/**
 * 游戏行为监控
 */
export function trackGameAction(actionType: string, userId: string): void {
    metricsCollector.gameActions.inc({ action_type: actionType, user_id: userId });
    
    Logger.info('Game Action', {
        actionType,
        userId,
        timestamp: new Date().toISOString(),
    });
}

/**
 * 数据库操作监控
 */
export function trackDatabaseOperation(operation: string, collection: string, status: 'success' | 'error'): void {
    metricsCollector.databaseOperations.inc({ operation, collection, status });
    
    if (status === 'error') {
        metricsCollector.errorRate.inc({
            type: 'database_error',
            severity: 'error',
        });
    }
}

/**
 * 缓存操作监控
 */
export function trackCacheOperation(operation: string, status: 'hit' | 'miss' | 'error'): void {
    metricsCollector.cacheOperations.inc({ operation, status });
    
    if (status === 'error') {
        metricsCollector.errorRate.inc({
            type: 'cache_error',
            severity: 'warning',
        });
    }
}

/**
 * 活跃用户监控
 */
export function updateActiveUsers(count: number, type: 'online' | 'total' = 'online'): void {
    metricsCollector.activeUsers.set({ type }, count);
}

/**
 * 错误监控
 */
export function trackError(errorType: string, severity: 'info' | 'warning' | 'error' = 'error'): void {
    metricsCollector.errorRate.inc({ type: errorType, severity });
    
    Logger.error('Error tracked', {
        errorType,
        severity,
        timestamp: new Date().toISOString(),
    });
}

/**
 * 性能监控中间件
 */
export function performanceMiddleware(req: Request, res: Response, next: NextFunction): void {
    const startTime = process.hrtime();
    const startMemory = process.memoryUsage();

    res.on('finish', () => {
        const diff = process.hrtime(startTime);
        const duration = diff[0] * 1000 + diff[1] * 1e-6; // 转换为毫秒

        const endMemory = process.memoryUsage();
        const memoryDiff = endMemory.heapUsed - startMemory.heapUsed;

        // 记录性能日志
        Logger.info('Performance Metrics', {
            method: req.method,
            url: req.url,
            duration: `${duration.toFixed(2)}ms`,
            memoryDiff: `${(memoryDiff / 1024 / 1024).toFixed(2)}MB`,
            statusCode: res.statusCode,
        });

        // 如果响应时间过长，记录警告
        if (duration > 1000) {
            Logger.warn('Slow Request', {
                method: req.method,
                url: req.url,
                duration: `${duration.toFixed(2)}ms`,
            });
        }
    });

    next();
}

/**
 * 健康检查端点
 */
export async function healthCheck(req: Request, res: Response): Promise<void> {
    try {
        const memUsage = process.memoryUsage();
        const uptime = process.uptime();
        
        const health = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: `${Math.floor(uptime)}s`,
            memory: {
                rss: `${(memUsage.rss / 1024 / 1024).toFixed(2)}MB`,
                heapUsed: `${(memUsage.heapUsed / 1024 / 1024).toFixed(2)}MB`,
                heapTotal: `${(memUsage.heapTotal / 1024 / 1024).toFixed(2)}MB`,
            },
            version: process.env.npm_package_version || '1.0.0',
        };

        res.status(200).json(health);
    } catch (error) {
        Logger.error('Health check failed', error);
        res.status(500).json({
            status: 'unhealthy',
            error: error.message,
        });
    }
}

/**
 * 指标端点
 */
export async function metricsEndpoint(req: Request, res: Response): Promise<void> {
    try {
        const metrics = await metricsCollector.getMetrics();
        res.set('Content-Type', promClient.register.contentType);
        res.end(metrics);
    } catch (error) {
        Logger.error('Metrics endpoint failed', error);
        res.status(500).json({
            error: 'Failed to collect metrics',
        });
    }
}
