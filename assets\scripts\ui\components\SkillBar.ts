/**
 * 技能栏组件 - 挂机手游版本
 * 用于展示当前装备的技能，技能自动释放，玩家可以查看技能信息和配置技能
 */

import { _decorator, Component, Node, Prefab, instantiate, Label, ProgressBar } from 'cc';
import { UIButton } from './UIButton';
import { IPlayerSkill } from '../panels/SkillPanel';
import { ConfigManager } from '../../managers/ConfigManager';
import { EventManager } from '../../managers/EventManager';

const { ccclass, property } = _decorator;

/**
 * 技能栏槽位接口 - 挂机手游版本
 */
export interface ISkillBarSlot {
    /** 槽位索引 */
    index: number;

    /** 槽位节点 */
    node: Node;

    /** 槽位按钮 */
    button: UIButton | null;

    /** 绑定的技能 */
    skill: IPlayerSkill | null;

    /** 冷却结束时间 */
    cooldownEndTime: number;

    /** 是否启用自动释放 */
    autoUse: boolean;

    /** 技能等级 */
    skillLevel: number;

    /** 上次自动释放时间 */
    lastAutoUseTime: number;
}

/**
 * 技能栏事件类型 - 挂机手游版本
 */
export enum SkillBarEventType {
    SkillAutoUsed = 'skill_auto_used',      // 技能自动释放
    SkillAdded = 'skill_added',             // 技能添加到栏位
    SkillRemoved = 'skill_removed',         // 技能从栏位移除
    SkillInfoViewed = 'skill_info_viewed',  // 查看技能信息
    SkillConfigChanged = 'skill_config_changed' // 技能配置改变
}

@ccclass('SkillBar')
export class SkillBar extends Component {
    
    @property({ type: Prefab, tooltip: '技能槽预制体' })
    public skillSlotPrefab: Prefab | null = null;
    
    @property({ tooltip: '技能栏槽位数量' })
    public slotCount: number = 8;
    
    @property({ tooltip: '是否启用快捷键' })
    public enableHotkeys: boolean = true;
    
    @property({ tooltip: '是否显示冷却时间' })
    public showCooldown: boolean = true;
    
    @property({ tooltip: '是否允许拖拽' })
    public allowDrag: boolean = true;
    
    @property({ tooltip: '技能栏方向' })
    public horizontal: boolean = true;
    
    // 私有属性
    private _slots: ISkillBarSlot[] = [];
    private _defaultHotkeys: KeyCode[] = [
        KeyCode.DIGIT_1, KeyCode.DIGIT_2, KeyCode.DIGIT_3, KeyCode.DIGIT_4,
        KeyCode.DIGIT_5, KeyCode.DIGIT_6, KeyCode.DIGIT_7, KeyCode.DIGIT_8
    ];
    private _draggedSlot: ISkillBarSlot | null = null;

    protected onLoad(): void {
        // 创建技能槽
        this.createSkillSlots();
        
        // 绑定事件
        this.bindEvents();
        
        console.log(`技能栏初始化完成，${this.slotCount} 个槽位`);
    }

    protected onDestroy(): void {
        this.unbindEvents();
    }

    /**
     * 创建技能槽
     */
    private createSkillSlots(): void {
        if (!this.skillSlotPrefab) {
            console.warn('缺少技能槽预制体');
            return;
        }
        
        // 清除现有槽位
        this.node.removeAllChildren();
        this._slots = [];
        
        // 创建新槽位
        for (let i = 0; i < this.slotCount; i++) {
            const slotNode = instantiate(this.skillSlotPrefab);
            slotNode.name = `SkillSlot_${i}`;
            slotNode.setParent(this.node);
            
            // 获取按钮组件
            const button = slotNode.getComponent(UIButton);
            
            // 创建槽位数据
            const slot: ISkillBarSlot = {
                index: i,
                node: slotNode,
                button,
                skill: null,
                hotkey: this.enableHotkeys && i < this._defaultHotkeys.length ? this._defaultHotkeys[i] : null,
                cooldownEndTime: 0,
                available: true
            };
            
            // 绑定槽位事件
            this.bindSlotEvents(slot);
            
            this._slots.push(slot);
        }
        
        console.log(`创建了 ${this.slotCount} 个技能槽`);
    }

    /**
     * 绑定槽位事件
     */
    private bindSlotEvents(slot: ISkillBarSlot): void {
        if (slot.button) {
            slot.button.setClickCallback(() => {
                this.onSlotClick(slot);
            });
        }
        
        // 绑定拖拽事件
        if (this.allowDrag) {
            slot.node.on(Node.EventType.TOUCH_START, (event) => {
                this.onSlotDragStart(slot, event);
            });
            
            slot.node.on(Node.EventType.TOUCH_MOVE, (event) => {
                this.onSlotDragMove(slot, event);
            });
            
            slot.node.on(Node.EventType.TOUCH_END, (event) => {
                this.onSlotDragEnd(slot, event);
            });
        }
    }

    /**
     * 绑定事件
     */
    private bindEvents(): void {
        if (this.enableHotkeys) {
            input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        }
    }

    /**
     * 解绑事件
     */
    private unbindEvents(): void {
        if (this.enableHotkeys) {
            input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        }
    }

    /**
     * 键盘按下事件
     */
    private onKeyDown(event: EventKeyboard): void {
        const keyCode = event.keyCode;
        
        // 查找对应的槽位
        const slot = this._slots.find(s => s.hotkey === keyCode);
        if (slot) {
            this.useSkill(slot);
        }
    }

    /**
     * 槽位点击事件
     */
    private onSlotClick(slot: ISkillBarSlot): void {
        if (slot.skill) {
            this.useSkill(slot);
        }
        
        // 发送槽位点击事件
        this.emitSkillBarEvent(SkillBarEventType.SlotClicked, {
            slotIndex: slot.index,
            skill: slot.skill
        });
    }

    /**
     * 槽位拖拽开始
     */
    private onSlotDragStart(slot: ISkillBarSlot, event: any): void {
        if (slot.skill) {
            this._draggedSlot = slot;
            console.log(`开始拖拽技能: ${slot.skill.skillData.name}`);
        }
    }

    /**
     * 槽位拖拽移动
     */
    private onSlotDragMove(slot: ISkillBarSlot, event: any): void {
        if (this._draggedSlot) {
            // 这里可以添加拖拽视觉效果
        }
    }

    /**
     * 槽位拖拽结束
     */
    private onSlotDragEnd(slot: ISkillBarSlot, event: any): void {
        if (this._draggedSlot) {
            // 检查是否拖拽到其他槽位
            const targetSlot = this.getSlotAtPosition(event.getLocation());
            if (targetSlot && targetSlot !== this._draggedSlot) {
                this.swapSkills(this._draggedSlot, targetSlot);
            }
            
            this._draggedSlot = null;
        }
    }

    /**
     * 获取指定位置的槽位
     */
    private getSlotAtPosition(worldPos: any): ISkillBarSlot | null {
        for (const slot of this._slots) {
            const nodePos = slot.node.getWorldPosition();
            const nodeSize = slot.node.getContentSize();
            
            if (Math.abs(worldPos.x - nodePos.x) < nodeSize.width / 2 &&
                Math.abs(worldPos.y - nodePos.y) < nodeSize.height / 2) {
                return slot;
            }
        }
        return null;
    }

    /**
     * 交换技能
     */
    private swapSkills(slot1: ISkillBarSlot, slot2: ISkillBarSlot): void {
        const tempSkill = slot1.skill;
        slot1.skill = slot2.skill;
        slot2.skill = tempSkill;
        
        // 更新显示
        this.updateSlotDisplay(slot1);
        this.updateSlotDisplay(slot2);
        
        console.log(`交换技能: 槽位${slot1.index} <-> 槽位${slot2.index}`);
    }

    /**
     * 使用技能
     */
    private useSkill(slot: ISkillBarSlot): void {
        if (!slot.skill || !slot.available) {
            return;
        }
        
        // 检查冷却时间
        const currentTime = Date.now();
        if (currentTime < slot.cooldownEndTime) {
            console.log(`技能冷却中: ${slot.skill.skillData.name}`);
            return;
        }
        
        // 检查法力消耗
        // 这里需要与玩家数据系统集成
        
        console.log(`使用技能: ${slot.skill.skillData.name}`);
        
        // 设置冷却时间
        slot.cooldownEndTime = currentTime + (slot.skill.skillData.cooldown * 1000);
        
        // 更新使用统计
        slot.skill.useCount++;
        slot.skill.lastUseTime = currentTime;
        
        // 开始冷却动画
        this.startCooldownAnimation(slot);
        
        // 发送技能使用事件
        this.emitSkillBarEvent(SkillBarEventType.SkillUsed, {
            slotIndex: slot.index,
            skill: slot.skill
        });
    }

    /**
     * 开始冷却动画
     */
    private startCooldownAnimation(slot: ISkillBarSlot): void {
        if (!this.showCooldown) {
            return;
        }
        
        // 这里需要实现冷却动画
        // 可以使用遮罩、进度条或其他视觉效果
        console.log(`开始冷却动画: ${slot.skill?.skillData.name}`);
        
        // 设置定时器更新冷却显示
        const updateCooldown = () => {
            const currentTime = Date.now();
            const remainingTime = slot.cooldownEndTime - currentTime;
            
            if (remainingTime <= 0) {
                // 冷却结束
                this.endCooldownAnimation(slot);
            } else {
                // 更新冷却显示
                this.updateCooldownDisplay(slot, remainingTime);
                setTimeout(updateCooldown, 100);
            }
        };
        
        updateCooldown();
    }

    /**
     * 结束冷却动画
     */
    private endCooldownAnimation(slot: ISkillBarSlot): void {
        console.log(`冷却结束: ${slot.skill?.skillData.name}`);
        this.updateSlotDisplay(slot);
    }

    /**
     * 更新冷却显示
     */
    private updateCooldownDisplay(slot: ISkillBarSlot, remainingTime: number): void {
        // 这里需要根据实际的槽位预制体结构来更新冷却显示
        // 例如更新进度条、文本等
    }

    /**
     * 更新槽位显示
     */
    private updateSlotDisplay(slot: ISkillBarSlot): void {
        if (slot.skill) {
            // 显示技能图标
            // 显示技能名称
            // 显示快捷键
            slot.node.active = true;
            
            if (slot.button) {
                slot.button.setEnabled(slot.available);
            }
            
            console.log(`更新槽位显示: ${slot.skill.skillData.name}`);
        } else {
            // 显示空槽位
            slot.node.active = true;
            
            if (slot.button) {
                slot.button.setEnabled(false);
            }
        }
    }

    /**
     * 发送技能栏事件
     */
    private emitSkillBarEvent(eventType: SkillBarEventType, data: any): void {
        EventManager.getInstance().emit(`skillbar_${eventType}`, data);
    }

    // ==================== 公共API ====================

    /**
     * 添加技能到槽位
     */
    public addSkill(skill: IPlayerSkill, slotIndex?: number): boolean {
        let targetSlot: ISkillBarSlot | null = null;
        
        if (slotIndex !== undefined && slotIndex >= 0 && slotIndex < this._slots.length) {
            targetSlot = this._slots[slotIndex];
        } else {
            // 查找空闲槽位
            targetSlot = this._slots.find(slot => !slot.skill) || null;
        }
        
        if (!targetSlot) {
            console.warn('没有可用的技能槽位');
            return false;
        }
        
        targetSlot.skill = skill;
        this.updateSlotDisplay(targetSlot);
        
        // 发送技能添加事件
        this.emitSkillBarEvent(SkillBarEventType.SkillAdded, {
            slotIndex: targetSlot.index,
            skill
        });
        
        console.log(`添加技能到槽位 ${targetSlot.index}: ${skill.skillData.name}`);
        return true;
    }

    /**
     * 从槽位移除技能
     */
    public removeSkill(slotIndex: number): boolean {
        if (slotIndex < 0 || slotIndex >= this._slots.length) {
            return false;
        }
        
        const slot = this._slots[slotIndex];
        const removedSkill = slot.skill;
        
        if (!removedSkill) {
            return false;
        }
        
        slot.skill = null;
        slot.cooldownEndTime = 0;
        this.updateSlotDisplay(slot);
        
        // 发送技能移除事件
        this.emitSkillBarEvent(SkillBarEventType.SkillRemoved, {
            slotIndex,
            skill: removedSkill
        });
        
        console.log(`从槽位 ${slotIndex} 移除技能: ${removedSkill.skillData.name}`);
        return true;
    }

    /**
     * 获取槽位技能
     */
    public getSkill(slotIndex: number): IPlayerSkill | null {
        if (slotIndex < 0 || slotIndex >= this._slots.length) {
            return null;
        }
        
        return this._slots[slotIndex].skill;
    }

    /**
     * 设置槽位可用性
     */
    public setSlotAvailable(slotIndex: number, available: boolean): void {
        if (slotIndex < 0 || slotIndex >= this._slots.length) {
            return;
        }
        
        const slot = this._slots[slotIndex];
        slot.available = available;
        this.updateSlotDisplay(slot);
    }

    /**
     * 清空所有槽位
     */
    public clearAllSlots(): void {
        for (const slot of this._slots) {
            slot.skill = null;
            slot.cooldownEndTime = 0;
            this.updateSlotDisplay(slot);
        }
        
        console.log('清空所有技能槽位');
    }

    /**
     * 获取所有技能
     */
    public getAllSkills(): IPlayerSkill[] {
        return this._slots
            .map(slot => slot.skill)
            .filter(skill => skill !== null) as IPlayerSkill[];
    }

    /**
     * 设置快捷键
     */
    public setHotkey(slotIndex: number, keyCode: KeyCode | null): void {
        if (slotIndex < 0 || slotIndex >= this._slots.length) {
            return;
        }
        
        this._slots[slotIndex].hotkey = keyCode;
        console.log(`设置槽位 ${slotIndex} 快捷键: ${keyCode}`);
    }

    /**
     * 获取技能栏状态
     */
    public getSkillBarStatus(): any {
        return {
            slotCount: this.slotCount,
            usedSlots: this._slots.filter(slot => slot.skill).length,
            skills: this._slots.map(slot => ({
                index: slot.index,
                skillId: slot.skill?.skillData.id || null,
                hotkey: slot.hotkey,
                available: slot.available,
                cooldownRemaining: Math.max(0, slot.cooldownEndTime - Date.now())
            }))
        };
    }
}
