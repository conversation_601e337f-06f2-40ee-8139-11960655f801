/**
 * UI组件基类
 * 提供组件基础功能、数据更新机制、组件通信等
 */

import { _decorator, Component, Node, EventTarget } from 'cc';
import { EventManager } from '../../managers/EventManager';

const { ccclass, property } = _decorator;

/**
 * 组件状态枚举
 */
export enum ComponentState {
    Inactive = 'inactive',
    Active = 'active',
    Disabled = 'disabled',
    Loading = 'loading'
}

/**
 * 组件数据接口
 */
export interface IComponentData {
    [key: string]: any;
}

/**
 * 组件事件接口
 */
export interface IComponentEvent {
    /** 事件类型 */
    type: string;
    
    /** 事件数据 */
    data?: any;
    
    /** 事件源组件 */
    source?: BaseUIComponent;
    
    /** 时间戳 */
    timestamp: number;
}

/**
 * 组件配置接口
 */
export interface IComponentConfig {
    /** 组件ID */
    id?: string;
    
    /** 是否自动更新 */
    autoUpdate?: boolean;
    
    /** 更新间隔（毫秒） */
    updateInterval?: number;
    
    /** 是否启用事件 */
    enableEvents?: boolean;
    
    /** 调试模式 */
    debugMode?: boolean;
}

@ccclass('BaseUIComponent')
export class BaseUIComponent extends Component {
    
    @property({ tooltip: '组件ID' })
    public componentId: string = '';
    
    @property({ tooltip: '是否自动更新' })
    public autoUpdate: boolean = false;
    
    @property({ tooltip: '更新间隔（秒）' })
    public updateInterval: number = 1.0;
    
    @property({ tooltip: '是否启用事件' })
    public enableEvents: boolean = true;
    
    @property({ tooltip: '调试模式' })
    public debugMode: boolean = false;
    
    // 组件状态
    protected _componentState: ComponentState = ComponentState.Inactive;
    
    // 组件数据
    protected _componentData: IComponentData = {};
    
    // 事件目标
    protected _eventTarget: EventTarget = new EventTarget();
    
    // 更新定时器
    protected _updateTimer: number = 0;
    
    // 子组件列表
    protected _childComponents: BaseUIComponent[] = [];
    
    // 父组件引用
    protected _parentComponent: BaseUIComponent | null = null;
    
    // 组件配置
    protected _config: IComponentConfig = {};

    protected onLoad(): void {
        // 生成默认ID
        if (!this.componentId) {
            this.componentId = `component_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }
        
        // 设置配置
        this._config = {
            id: this.componentId,
            autoUpdate: this.autoUpdate,
            updateInterval: this.updateInterval * 1000, // 转换为毫秒
            enableEvents: this.enableEvents,
            debugMode: this.debugMode
        };
        
        if (this.debugMode) {
            console.log(`🔧 UI组件加载: ${this.componentId}`);
        }
        
        // 查找子组件
        this.findChildComponents();
        
        // 调用子类初始化
        this.onComponentLoad();
    }

    protected onEnable(): void {
        this._componentState = ComponentState.Active;
        
        // 绑定事件
        if (this.enableEvents) {
            this.bindComponentEvents();
        }
        
        // 启动自动更新
        if (this.autoUpdate) {
            this.startAutoUpdate();
        }
        
        // 调用子类启用
        this.onComponentEnable();
        
        if (this.debugMode) {
            console.log(`🔧 UI组件启用: ${this.componentId}`);
        }
    }

    protected onDisable(): void {
        this._componentState = ComponentState.Disabled;
        
        // 停止自动更新
        this.stopAutoUpdate();
        
        // 解绑事件
        if (this.enableEvents) {
            this.unbindComponentEvents();
        }
        
        // 调用子类禁用
        this.onComponentDisable();
        
        if (this.debugMode) {
            console.log(`🔧 UI组件禁用: ${this.componentId}`);
        }
    }

    protected onDestroy(): void {
        // 清理资源
        this.cleanup();
        
        // 调用子类销毁
        this.onComponentDestroy();
        
        if (this.debugMode) {
            console.log(`🔧 UI组件销毁: ${this.componentId}`);
        }
    }

    /**
     * 查找子组件
     */
    private findChildComponents(): void {
        this._childComponents = [];
        
        const findComponents = (node: Node) => {
            const components = node.getComponents(BaseUIComponent);
            for (const component of components) {
                if (component !== this) {
                    this._childComponents.push(component);
                    component._parentComponent = this;
                }
            }
            
            for (const child of node.children) {
                findComponents(child);
            }
        };
        
        for (const child of this.node.children) {
            findComponents(child);
        }
        
        if (this.debugMode && this._childComponents.length > 0) {
            console.log(`🔧 找到 ${this._childComponents.length} 个子组件`);
        }
    }

    /**
     * 绑定组件事件
     */
    private bindComponentEvents(): void {
        const eventManager = EventManager.getInstance();
        
        // 绑定全局组件事件
        eventManager.on(`component_${this.componentId}_update`, this.onExternalUpdate, this);
        eventManager.on(`component_${this.componentId}_refresh`, this.onExternalRefresh, this);
        
        // 绑定组件通信事件
        eventManager.on('component_broadcast', this.onComponentBroadcast, this);
        
        // 调用子类事件绑定
        this.bindEvents();
    }

    /**
     * 解绑组件事件
     */
    private unbindComponentEvents(): void {
        const eventManager = EventManager.getInstance();
        
        // 解绑全局组件事件
        eventManager.off(`component_${this.componentId}_update`, this.onExternalUpdate, this);
        eventManager.off(`component_${this.componentId}_refresh`, this.onExternalRefresh, this);
        
        // 解绑组件通信事件
        eventManager.off('component_broadcast', this.onComponentBroadcast, this);
        
        // 调用子类事件解绑
        this.unbindEvents();
    }

    /**
     * 启动自动更新
     */
    private startAutoUpdate(): void {
        if (this._updateTimer) {
            return;
        }
        
        this._updateTimer = setInterval(() => {
            this.updateComponent();
        }, this._config.updateInterval || 1000);
        
        if (this.debugMode) {
            console.log(`🔧 启动自动更新: ${this.componentId}, 间隔: ${this._config.updateInterval}ms`);
        }
    }

    /**
     * 停止自动更新
     */
    private stopAutoUpdate(): void {
        if (this._updateTimer) {
            clearInterval(this._updateTimer);
            this._updateTimer = 0;
            
            if (this.debugMode) {
                console.log(`🔧 停止自动更新: ${this.componentId}`);
            }
        }
    }

    /**
     * 清理资源
     */
    private cleanup(): void {
        // 停止自动更新
        this.stopAutoUpdate();
        
        // 清理事件目标
        this._eventTarget.removeAll();
        
        // 清理数据
        this._componentData = {};
        
        // 清理子组件引用
        for (const child of this._childComponents) {
            child._parentComponent = null;
        }
        this._childComponents = [];
        
        // 清理父组件引用
        if (this._parentComponent) {
            const index = this._parentComponent._childComponents.indexOf(this);
            if (index !== -1) {
                this._parentComponent._childComponents.splice(index, 1);
            }
            this._parentComponent = null;
        }
    }

    /**
     * 外部更新事件处理
     */
    private onExternalUpdate(data: any): void {
        this.setComponentData(data);
    }

    /**
     * 外部刷新事件处理
     */
    private onExternalRefresh(): void {
        this.refreshComponent();
    }

    /**
     * 组件广播事件处理
     */
    private onComponentBroadcast(event: IComponentEvent): void {
        if (event.source !== this) {
            this.onBroadcastReceived(event);
        }
    }

    // ==================== 数据管理 ====================

    /**
     * 设置组件数据
     */
    public setComponentData(data: IComponentData): void {
        const oldData = { ...this._componentData };
        this._componentData = { ...this._componentData, ...data };
        
        // 调用数据变更回调
        this.onDataChanged(this._componentData, oldData);
        
        // 发送数据变更事件
        this.emitComponentEvent('data_changed', {
            newData: this._componentData,
            oldData: oldData
        });
        
        if (this.debugMode) {
            console.log(`🔧 组件数据更新: ${this.componentId}`, data);
        }
    }

    /**
     * 获取组件数据
     */
    public getComponentData(): IComponentData {
        return { ...this._componentData };
    }

    /**
     * 获取数据值
     */
    public getDataValue(key: string, defaultValue?: any): any {
        return this._componentData[key] !== undefined ? this._componentData[key] : defaultValue;
    }

    /**
     * 设置数据值
     */
    public setDataValue(key: string, value: any): void {
        this.setComponentData({ [key]: value });
    }

    /**
     * 清空组件数据
     */
    public clearComponentData(): void {
        const oldData = { ...this._componentData };
        this._componentData = {};
        
        this.onDataChanged(this._componentData, oldData);
        this.emitComponentEvent('data_cleared', { oldData });
    }

    // ==================== 组件通信 ====================

    /**
     * 发送组件事件
     */
    public emitComponentEvent(type: string, data?: any): void {
        const event: IComponentEvent = {
            type,
            data,
            source: this,
            timestamp: Date.now()
        };
        
        // 发送到本地事件目标
        this._eventTarget.emit(type, event);
        
        // 发送到全局事件管理器
        if (this.enableEvents) {
            const eventManager = EventManager.getInstance();
            eventManager.emit(`component_${this.componentId}_${type}`, event);
        }
        
        if (this.debugMode) {
            console.log(`🔧 组件事件: ${this.componentId}.${type}`, data);
        }
    }

    /**
     * 监听组件事件
     */
    public onComponentEvent(type: string, callback: (event: IComponentEvent) => void): void {
        this._eventTarget.on(type, callback);
    }

    /**
     * 取消监听组件事件
     */
    public offComponentEvent(type: string, callback?: (event: IComponentEvent) => void): void {
        if (callback) {
            this._eventTarget.off(type, callback);
        } else {
            this._eventTarget.removeAll(type);
        }
    }

    /**
     * 广播事件到所有组件
     */
    public broadcastEvent(type: string, data?: any): void {
        const event: IComponentEvent = {
            type,
            data,
            source: this,
            timestamp: Date.now()
        };
        
        const eventManager = EventManager.getInstance();
        eventManager.emit('component_broadcast', event);
        
        if (this.debugMode) {
            console.log(`🔧 广播事件: ${type}`, data);
        }
    }

    /**
     * 发送消息到父组件
     */
    public sendToParent(type: string, data?: any): void {
        if (this._parentComponent) {
            this._parentComponent._eventTarget.emit(type, {
                type,
                data,
                source: this,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 发送消息到子组件
     */
    public sendToChildren(type: string, data?: any): void {
        const event: IComponentEvent = {
            type,
            data,
            source: this,
            timestamp: Date.now()
        };
        
        for (const child of this._childComponents) {
            child._eventTarget.emit(type, event);
        }
    }

    /**
     * 查找组件
     */
    public findComponent(componentId: string): BaseUIComponent | null {
        // 在子组件中查找
        for (const child of this._childComponents) {
            if (child.componentId === componentId) {
                return child;
            }
            
            const found = child.findComponent(componentId);
            if (found) {
                return found;
            }
        }
        
        return null;
    }

    // ==================== 组件更新 ====================

    /**
     * 更新组件
     */
    public updateComponent(): void {
        if (this._componentState !== ComponentState.Active) {
            return;
        }
        
        // 调用子类更新
        this.onUpdate();
        
        // 更新子组件
        for (const child of this._childComponents) {
            if (child.autoUpdate) {
                child.updateComponent();
            }
        }
    }

    /**
     * 刷新组件
     */
    public refreshComponent(): void {
        // 调用子类刷新
        this.onRefresh();
        
        // 刷新子组件
        for (const child of this._childComponents) {
            child.refreshComponent();
        }
        
        // 发送刷新事件
        this.emitComponentEvent('refreshed');
    }

    // ==================== 公共API ====================

    /**
     * 获取组件状态
     */
    public getComponentState(): ComponentState {
        return this._componentState;
    }

    /**
     * 设置组件状态
     */
    public setComponentState(state: ComponentState): void {
        const oldState = this._componentState;
        this._componentState = state;
        
        this.onStateChanged(state, oldState);
        this.emitComponentEvent('state_changed', { newState: state, oldState });
    }

    /**
     * 获取组件配置
     */
    public getComponentConfig(): IComponentConfig {
        return { ...this._config };
    }

    /**
     * 更新组件配置
     */
    public updateComponentConfig(config: Partial<IComponentConfig>): void {
        this._config = { ...this._config, ...config };
        
        // 应用配置变更
        if (config.autoUpdate !== undefined) {
            this.autoUpdate = config.autoUpdate;
            if (config.autoUpdate) {
                this.startAutoUpdate();
            } else {
                this.stopAutoUpdate();
            }
        }
        
        if (config.updateInterval !== undefined) {
            this.updateInterval = config.updateInterval / 1000; // 转换为秒
            if (this.autoUpdate) {
                this.stopAutoUpdate();
                this.startAutoUpdate();
            }
        }
        
        if (config.debugMode !== undefined) {
            this.debugMode = config.debugMode;
        }
    }

    /**
     * 获取子组件列表
     */
    public getChildComponents(): BaseUIComponent[] {
        return [...this._childComponents];
    }

    /**
     * 获取父组件
     */
    public getParentComponent(): BaseUIComponent | null {
        return this._parentComponent;
    }

    /**
     * 检查是否为活跃状态
     */
    public isActive(): boolean {
        return this._componentState === ComponentState.Active;
    }

    // ==================== 抽象方法（子类实现） ====================

    /**
     * 组件加载时调用
     */
    protected onComponentLoad(): void {
        // 子类可以重写
    }

    /**
     * 组件启用时调用
     */
    protected onComponentEnable(): void {
        // 子类可以重写
    }

    /**
     * 组件禁用时调用
     */
    protected onComponentDisable(): void {
        // 子类可以重写
    }

    /**
     * 组件销毁时调用
     */
    protected onComponentDestroy(): void {
        // 子类可以重写
    }

    /**
     * 绑定事件
     */
    protected bindEvents(): void {
        // 子类可以重写
    }

    /**
     * 解绑事件
     */
    protected unbindEvents(): void {
        // 子类可以重写
    }

    /**
     * 组件更新时调用
     */
    protected onUpdate(): void {
        // 子类可以重写
    }

    /**
     * 组件刷新时调用
     */
    protected onRefresh(): void {
        // 子类可以重写
    }

    /**
     * 数据变更时调用
     */
    protected onDataChanged(newData: IComponentData, oldData: IComponentData): void {
        // 子类可以重写
    }

    /**
     * 状态变更时调用
     */
    protected onStateChanged(newState: ComponentState, oldState: ComponentState): void {
        // 子类可以重写
    }

    /**
     * 接收广播事件时调用
     */
    protected onBroadcastReceived(event: IComponentEvent): void {
        // 子类可以重写
    }
}
