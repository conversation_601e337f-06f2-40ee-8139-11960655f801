// Copyright (c) 2025 "放置". All rights reserved.
import { _decorator, Component, assetManager, resources, find } from 'cc';
import { BaseManager } from './BaseManager';
import { NetworkManager } from '../network/NetworkManager';
import { ServerConfig } from '../config/ServerConfig';
import { EventManager } from './EventManager';
import { ConfigMigrationHelper } from './ConfigMigrationHelper';

const { ccclass } = _decorator;

/**
 * 地点配置数据接口（扩展支持游戏区域功能）
 */
export interface ILocationConfig {
    locationId: string;
    name: string;
    description: string;
    type: string;
    level: number;
    unlockConditions: {
        playerLevel: number;
        requiredItems: string[];
        completedQuests: string[];
        requiredAreas?: string[]; // 新增：前置区域要求
        customConditions?: ICustomUnlockCondition[]; // 新增：自定义解锁条件
    };
    mapConfig: {
        backgroundImagePath: string;
        mapSize: { width: number; height: number };
        nodeSpacing: { x: number; y: number };
        startPosition: { x: number; y: number };
        branchCount: number;
        nodesPerBranch: number;
    };
    environment: {
        weather: string;
        timeOfDay: string;
        backgroundMusic: string;
        ambientSounds: string[];
        lightingColor: { r: number; g: number; b: number; a: number };
    };
    resources: {
        monsters: any[];
        collectibles: any[];
    };
    rewards: {
        baseExp: number;
        baseGold: number;
        dropTables: string[];
    };
    uiElements: any[];
    nodeData: any[];
    // 新增：区域行为配置
    behaviors?: ILocationBehavior[];
    // 新增：区域解锁状态
    unlockStatus?: ILocationUnlockStatus;
}

/**
 * 自定义解锁条件接口
 */
export interface ICustomUnlockCondition {
    type: string; // 条件类型：currency, achievement, time等
    params: { [key: string]: any }; // 条件参数
    description: string; // 条件描述
}

/**
 * 地点行为配置接口
 */
export interface ILocationBehavior {
    id: string;
    name: string;
    description: string;
    type: string; // fishing, farming, logging, exploration等
    duration: number; // 执行时间（秒）
    cooldown: number; // 冷却时间（秒）
    energyCost: number; // 能量消耗
    requirements: {
        items: string[]; // 必需道具
        skills: string[]; // 必需技能
        level: number; // 等级要求
    };
    rewards: {
        baseExp: number;
        items: ILocationBehaviorItemReward[];
        currency: { [currencyType: string]: number[] }; // [最小值, 最大值]
    };
}

/**
 * 地点行为道具奖励接口
 */
export interface ILocationBehaviorItemReward {
    id: string;
    probability: number; // 获得概率 0-1
    quantity: number[]; // 数量范围 [最小值, 最大值]
}

/**
 * 地点解锁状态接口
 */
export interface ILocationUnlockStatus {
    isUnlocked: boolean;
    unmetConditions: string[];
    unlockProgress: number; // 0-1
    unlockedAt?: Date;
}

/**
 * 地点配置缓存项
 */
interface ILocationConfigCache {
    config: ILocationConfig;
    timestamp: number;
    expiry: number;
}

/**
 * 地点配置管理器
 * 专门管理LinearBranchMapContainer的地点配置数据
 */
@ccclass('LocationConfigManager')
export class LocationConfigManager extends BaseManager {
    private static _instance: LocationConfigManager;

    /**
     * 地点配置缓存
     */
    private _locationConfigCache: Map<string, ILocationConfigCache> = new Map();

    /**
     * 配置加载Promise缓存
     */
    private _configLoadingPromises: Map<string, Promise<ILocationConfig>> = new Map();

    /**
     * 缓存配置
     */
    private readonly LOCATION_CONFIG_CACHE_DURATION = 30 * 60 * 1000; // 30分钟

    /**
     * 获取单例实例
     */
    public static getInstance(): LocationConfigManager {
        return LocationConfigManager._instance;
    }

    protected onLoad(): void {
        super.onLoad();
        LocationConfigManager._instance = this;
        console.log('🗺️ LocationConfigManager: 地点配置管理器初始化');
    }

    /**
     * 初始化管理器
     */
    public async initializeManager(): Promise<void> {
        console.log('🗺️ LocationConfigManager: 初始化地点配置管理器');

        // 打印服务器配置信息
        ServerConfig.printConfig();

        // 验证配置
        const validation = ServerConfig.validateConfig();
        if (!validation.isValid) {
            console.error('❌ 服务器配置验证失败:', validation.errors);
        }
        if (validation.warnings.length > 0) {
            console.warn('⚠️ 服务器配置警告:', validation.warnings);
        }

        // 可以在这里预加载常用配置
    }

    /**
     * 销毁管理器
     */
    public async destroyManager(): Promise<void> {
        console.log('🗺️ LocationConfigManager: 销毁地点配置管理器');
        this._locationConfigCache.clear();
        this._configLoadingPromises.clear();
    }

    /**
     * 获取地点配置数据
     */
    public async getLocationConfig(locationId: string, version: string = 'latest'): Promise<ILocationConfig | null> {
        console.log(`🗺️ 获取地点配置: ${locationId} (${version})`);
        
        const cacheKey = `${locationId}_${version}`;
        
        // 检查缓存
        const cached = this._locationConfigCache.get(cacheKey);
        if (cached && Date.now() < cached.expiry) {
            console.log(`🗺️ 从缓存获取地点配置: ${locationId}`);
            return cached.config;
        }
        
        // 检查是否正在加载
        if (this._configLoadingPromises.has(cacheKey)) {
            console.log(`🗺️ 地点配置正在加载中: ${locationId}`);
            return this._configLoadingPromises.get(cacheKey)!;
        }
        
        // 开始加载
        const loadPromise = this.loadLocationConfigFromSource(locationId, version);
        this._configLoadingPromises.set(cacheKey, loadPromise);
        
        try {
            const config = await loadPromise;
            
            // 缓存配置
            if (config) {
                this._locationConfigCache.set(cacheKey, {
                    config,
                    timestamp: Date.now(),
                    expiry: Date.now() + this.LOCATION_CONFIG_CACHE_DURATION
                });
            }
            
            return config;
        } catch (error) {
            console.error(`❌ 获取地点配置失败: ${locationId}`, error);
            return null;
        } finally {
            this._configLoadingPromises.delete(cacheKey);
        }
    }

    /**
     * 批量获取地点配置
     */
    public async getLocationConfigsBatch(locationIds: string[], version: string = 'latest'): Promise<Map<string, ILocationConfig>> {
        console.log(`🗺️ 批量获取地点配置: ${locationIds.join(', ')}`);
        
        const configs = new Map<string, ILocationConfig>();
        const uncachedLocationIds: string[] = [];
        
        // 检查缓存
        for (const locationId of locationIds) {
            const cacheKey = `${locationId}_${version}`;
            const cached = this._locationConfigCache.get(cacheKey);
            
            if (cached && Date.now() < cached.expiry) {
                configs.set(locationId, cached.config);
            } else {
                uncachedLocationIds.push(locationId);
            }
        }
        
        // 加载未缓存的配置
        for (const locationId of uncachedLocationIds) {
            try {
                const config = await this.getLocationConfig(locationId, version);
                if (config) {
                    configs.set(locationId, config);
                }
            } catch (error) {
                console.error(`❌ 批量获取地点配置失败: ${locationId}`, error);
            }
        }
        
        return configs;
    }

    /**
     * 从数据源加载地点配置（支持新旧格式兼容）
     */
    private async loadLocationConfigFromSource(locationId: string, version: string): Promise<ILocationConfig | null> {
        // 1. 尝试使用配置迁移助手智能加载
        const migrationHelper = ConfigMigrationHelper.getInstance();
        if (migrationHelper) {
            console.log(`🔄 使用配置迁移助手加载: ${locationId}`);
            const config = await migrationHelper.loadLocationConfig(locationId);
            if (config) {
                console.log(`✅ 配置迁移助手加载成功: ${locationId}`);
                return config;
            }
        }

        // 2. 降级到原有逻辑：优先从服务器加载
        console.log(`🔄 使用传统方式加载配置: ${locationId}`);
        let config = await this.loadLocationConfigFromServer(locationId, version);

        // 3. 如果服务器加载失败，尝试从本地分包加载
        if (!config) {
            config = await this.loadLocationConfigFromBundle(locationId);
        }

        return config;
    }

    /**
     * 从服务器加载地点配置
     */
    private async loadLocationConfigFromServer(locationId: string, version: string): Promise<ILocationConfig | null> {
        try {
            console.log(`🌐 请求服务器地点配置: ${locationId} (版本: ${version})`);

            // 使用网络管理器请求服务器API
            const result = await this.fallbackToLocalServer('getLocationConfig', {
                locationId,
                version
            });

            if (result.success && result.data && result.data.config) {
                console.log(`🌐 从服务器加载地点配置成功: ${locationId}`);
                return result.data.config as ILocationConfig;
            }

            console.warn(`⚠️ 服务器响应格式错误:`, result);
            return null;
        } catch (error) {
            console.error(`❌ 从服务器加载地点配置失败: ${locationId}`, error);
            return null;
        }
    }



    /**
     * 降级到备用服务器
     */
    private async fallbackToLocalServer(action: string, data: any): Promise<any> {
        // 获取启用的服务器列表
        const servers = ServerConfig.getEnabledServers();

        if (servers.length === 0) {
            throw new Error('没有可用的服务器');
        }

        for (const server of servers) {
            try {
                console.log(`🔄 尝试连接服务器: ${server.name} (${server.baseUrl})`);

                let url = '';
                switch (action) {
                    case 'getLocationConfig':
                        url = `${server.baseUrl}/api/v1/locations/config/${data.locationId}?version=${data.version || 'latest'}`;
                        break;
                    case 'getLocationConfigsBatch':
                        url = `${server.baseUrl}/api/v1/locations/config/batch`;
                        break;
                    default:
                        throw new Error(`不支持的action: ${action}`);
                }

                // 创建带超时的fetch请求
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), server.timeout);

                const response = await fetch(url, {
                    method: action === 'getLocationConfigsBatch' ? 'POST' : 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: action === 'getLocationConfigsBatch' ? JSON.stringify(data) : undefined,
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP请求失败: ${response.status} ${response.statusText}`);
                }

                const result = await response.json();
                console.log(`✅ 服务器响应成功: ${server.name} - ${action}`);
                return result;

            } catch (error) {
                console.warn(`⚠️ 服务器连接失败: ${server.name}`, error);
                // 继续尝试下一个服务器
            }
        }

        // 所有服务器都失败
        throw new Error('所有服务器都无法连接');
    }

    /**
     * 从本地分包加载地点配置
     */
    private async loadLocationConfigFromBundle(locationId: string): Promise<ILocationConfig | null> {
        try {
            // 加载地点资源分包
            const bundle = await this.loadBundle('locations');
            if (!bundle) {
                return null;
            }

            // 从分包加载配置文件
            const configPath = `${locationId.toLowerCase()}-location-config`;
            const configAsset = await this.loadAssetFromBundle(bundle, configPath);
            
            if (configAsset && configAsset.json) {
                console.log(`📦 从分包加载地点配置成功: ${locationId}`);
                return configAsset.json as ILocationConfig;
            }
            
            return null;
        } catch (error) {
            console.error(`❌ 从分包加载地点配置失败: ${locationId}`, error);
            return null;
        }
    }

    /**
     * 加载分包
     */
    private async loadBundle(bundleName: string): Promise<any> {
        return new Promise((resolve, reject) => {
            assetManager.loadBundle(bundleName, (error, bundle) => {
                if (error) {
                    reject(error);
                } else {
                    resolve(bundle);
                }
            });
        });
    }

    /**
     * 从分包加载资源
     */
    private async loadAssetFromBundle(bundle: any, assetPath: string): Promise<any> {
        return new Promise((resolve, reject) => {
            bundle.load(assetPath, (error, asset) => {
                if (error) {
                    reject(error);
                } else {
                    resolve(asset);
                }
            });
        });
    }

    /**
     * 应用地点配置到LinearBranchMapContainer（扩展支持区域功能）
     */
    public async applyLocationConfigToContainer(locationId: string, containerComponent: any): Promise<boolean> {
        try {
            const config = await this.getLocationConfig(locationId);
            if (!config) {
                console.error(`❌ 无法获取地点配置: ${locationId}`);
                return false;
            }

            console.log(`🎨 应用地点配置到容器: ${locationId}`);

            // 检查解锁状态
            const unlockStatus = await this.checkLocationUnlockStatus(locationId, config);
            if (!unlockStatus.isUnlocked) {
                console.warn(`⚠️ 地点未解锁: ${locationId}`, unlockStatus.unmetConditions);
                this.showUnlockHint(locationId, unlockStatus);
                return false;
            }

            // 应用地图配置
            if (config.mapConfig && containerComponent.branchMapPanel) {
                const mapPanel = containerComponent.branchMapPanel;
                mapPanel.backgroundImagePath = config.mapConfig.backgroundImagePath;
                mapPanel.nodeSpacingX = config.mapConfig.nodeSpacing.x;
                mapPanel.nodeSpacingY = config.mapConfig.nodeSpacing.y;
                mapPanel.branchCount = config.mapConfig.branchCount;
                mapPanel.nodesPerBranch = config.mapConfig.nodesPerBranch;
            }

            // 应用节点数据
            if (config.nodeData && containerComponent.setNodeData) {
                containerComponent.setNodeData(config.nodeData);
            }

            // 应用UI元素
            if (config.uiElements && containerComponent.setUIElements) {
                containerComponent.setUIElements(config.uiElements);
            }

            // 新增：加载区域行为到BehaviorPanel
            await this.loadLocationBehaviors(locationId, config);

            // 新增：发送区域切换事件
            this.emitLocationSwitchEvent(locationId, config);

            console.log(`✅ 地点配置应用成功: ${locationId}`);
            return true;
        } catch (error) {
            console.error(`❌ 应用地点配置失败: ${locationId}`, error);
            return false;
        }
    }

    /**
     * 清除地点配置缓存
     */
    public clearLocationConfigCache(locationId?: string): void {
        if (locationId) {
            // 清除特定地点的缓存
            const keysToDelete: string[] = [];
            for (const key of this._locationConfigCache.keys()) {
                if (key.startsWith(`${locationId}_`)) {
                    keysToDelete.push(key);
                }
            }
            keysToDelete.forEach(key => this._locationConfigCache.delete(key));
            console.log(`🗑️ 已清除地点 ${locationId} 的配置缓存`);
        } else {
            // 清除所有地点配置缓存
            this._locationConfigCache.clear();
            console.log('🗑️ 已清除所有地点配置缓存');
        }
    }

    /**
     * 获取地点配置缓存统计信息
     */
    public getLocationConfigCacheStats(): {
        totalCached: number;
        cacheSize: number;
        expiredCount: number;
    } {
        const now = Date.now();
        let expiredCount = 0;

        for (const cache of this._locationConfigCache.values()) {
            if (now >= cache.expiry) {
                expiredCount++;
            }
        }

        return {
            totalCached: this._locationConfigCache.size,
            cacheSize: this._locationConfigCache.size,
            expiredCount
        };
    }

    // ==================== 新增：区域功能扩展方法 ====================

    /**
     * 检查地点解锁状态
     */
    public async checkLocationUnlockStatus(locationId: string, config: ILocationConfig): Promise<ILocationUnlockStatus> {
        const unmetConditions: string[] = [];
        let totalConditions = 0;
        let metConditions = 0;

        // 检查玩家等级
        totalConditions++;
        const playerLevel = await this.getPlayerLevel();
        if (playerLevel >= config.unlockConditions.playerLevel) {
            metConditions++;
        } else {
            unmetConditions.push(`需要等级${config.unlockConditions.playerLevel}，当前等级${playerLevel}`);
        }

        // 检查必需道具
        if (config.unlockConditions.requiredItems.length > 0) {
            totalConditions++;
            const hasAllItems = await this.checkRequiredItems(config.unlockConditions.requiredItems);
            if (hasAllItems) {
                metConditions++;
            } else {
                unmetConditions.push(`缺少必需道具: ${config.unlockConditions.requiredItems.join(', ')}`);
            }
        }

        // 检查完成的任务
        if (config.unlockConditions.completedQuests.length > 0) {
            totalConditions++;
            const hasCompletedQuests = await this.checkCompletedQuests(config.unlockConditions.completedQuests);
            if (hasCompletedQuests) {
                metConditions++;
            } else {
                unmetConditions.push(`需要完成任务: ${config.unlockConditions.completedQuests.join(', ')}`);
            }
        }

        // 检查前置区域
        if (config.unlockConditions.requiredAreas && config.unlockConditions.requiredAreas.length > 0) {
            totalConditions++;
            const hasRequiredAreas = await this.checkRequiredAreas(config.unlockConditions.requiredAreas);
            if (hasRequiredAreas) {
                metConditions++;
            } else {
                unmetConditions.push(`需要解锁区域: ${config.unlockConditions.requiredAreas.join(', ')}`);
            }
        }

        // 检查自定义条件
        if (config.unlockConditions.customConditions) {
            for (const customCondition of config.unlockConditions.customConditions) {
                totalConditions++;
                const isValid = await this.checkCustomCondition(customCondition);
                if (isValid) {
                    metConditions++;
                } else {
                    unmetConditions.push(customCondition.description);
                }
            }
        }

        const unlockProgress = totalConditions > 0 ? metConditions / totalConditions : 1;
        const isUnlocked = unmetConditions.length === 0;

        return {
            isUnlocked,
            unmetConditions,
            unlockProgress,
            unlockedAt: isUnlocked ? new Date() : undefined
        };
    }

    /**
     * 加载地点行为到BehaviorPanel
     */
    public async loadLocationBehaviors(locationId: string, config: ILocationConfig): Promise<void> {
        if (!config.behaviors || config.behaviors.length === 0) {
            console.log(`📝 地点${locationId}没有配置行为`);
            return;
        }

        try {
            // 查找BehaviorPanel
            const behaviorPanel = this.findBehaviorPanel();
            if (!behaviorPanel) {
                console.warn('⚠️ 未找到BehaviorPanel，跳过行为加载');
                return;
            }

            // 清除现有行为
            if (behaviorPanel.clearBehaviorQueue) {
                behaviorPanel.clearBehaviorQueue();
            }

            // 加载新的行为
            for (const locationBehavior of config.behaviors) {
                const behaviorData = this.convertLocationBehaviorToBehaviorData(locationBehavior);

                // 使用BehaviorPanel的现有方法添加行为
                if (behaviorPanel.queueBehavior) {
                    behaviorPanel.queueBehavior(behaviorData.type, behaviorData);
                }
            }

            console.log(`✅ 加载了${config.behaviors.length}个地点行为: ${locationId}`);

        } catch (error) {
            console.error(`❌ 加载地点行为失败: ${locationId}`, error);
        }
    }

    /**
     * 显示解锁提示
     */
    private showUnlockHint(locationId: string, unlockStatus: ILocationUnlockStatus): void {
        console.log(`💡 地点${locationId}解锁提示:`);
        console.log(`   解锁进度: ${(unlockStatus.unlockProgress * 100).toFixed(1)}%`);
        console.log(`   未满足条件: ${unlockStatus.unmetConditions.join(', ')}`);

        // 这里可以显示UI提示
        // UIManager.showUnlockHint(locationId, unlockStatus);
    }

    /**
     * 发送地点切换事件（扩展支持后端数据同步）
     */
    private emitLocationSwitchEvent(locationId: string, config: ILocationConfig): void {
        try {
            const eventManager = EventManager.getInstance();

            // 发送地点切换事件
            eventManager.emit('location:switched', {
                locationId,
                config,
                timestamp: Date.now()
            });

            // 发送后端数据同步请求
            this.syncLocationDataToServer(locationId, config);

        } catch (error) {
            console.warn('⚠️ 发送地点切换事件失败', error);
        }
    }

    /**
     * 同步地点数据到服务器
     */
    private async syncLocationDataToServer(locationId: string, config: ILocationConfig): Promise<void> {
        try {
            // 构建同步数据
            const syncData = {
                type: 'location_switch',
                locationId,
                playerData: {
                    currentLocation: locationId,
                    switchTime: Date.now()
                },
                locationData: {
                    name: config.name,
                    type: config.type,
                    level: config.level
                }
            };

            // 发送到后端数据同步服务
            this.sendToDataSyncService(syncData);

            console.log(`🔄 地点数据同步请求已发送: ${locationId}`);

        } catch (error) {
            console.error('❌ 同步地点数据到服务器失败', error);
        }
    }

    /**
     * 发送数据到同步服务
     */
    private sendToDataSyncService(syncData: any): void {
        try {
            // 使用事件方式发送数据同步请求
            const eventManager = EventManager.getInstance();
            eventManager.emit('data:sync_request', syncData);

            console.log('🔄 数据同步请求已通过事件发送');

        } catch (error) {
            console.error('❌ 发送数据同步请求失败', error);
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 查找BehaviorPanel组件
     */
    private findBehaviorPanel(): any {
        try {
            const behaviorPanelNode = find('Canvas/MainUI/BehaviorPanel');
            if (behaviorPanelNode) {
                return behaviorPanelNode.getComponent('BehaviorPanel');
            }
        } catch (error) {
            console.warn('⚠️ 查找BehaviorPanel失败', error);
        }
        return null;
    }

    /**
     * 转换地点行为为BehaviorPanel行为数据
     */
    private convertLocationBehaviorToBehaviorData(locationBehavior: ILocationBehavior): any {
        // 映射地点行为类型到BehaviorType
        let behaviorType: string;
        switch (locationBehavior.type) {
            case 'fishing':
                behaviorType = 'skill';
                break;
            case 'farming':
                behaviorType = 'skill';
                break;
            case 'logging':
                behaviorType = 'skill';
                break;
            case 'exploration':
                behaviorType = 'move';
                break;
            default:
                behaviorType = 'idle';
        }

        return {
            type: behaviorType,
            name: locationBehavior.name,
            description: locationBehavior.description,
            duration: locationBehavior.duration,
            interruptible: true,
            cooldown: locationBehavior.cooldown,
            extraData: {
                locationBehaviorId: locationBehavior.id,
                energyCost: locationBehavior.energyCost,
                requirements: locationBehavior.requirements,
                rewards: locationBehavior.rewards
            }
        };
    }

    /**
     * 获取玩家等级（需要实现）
     */
    private async getPlayerLevel(): Promise<number> {
        // 这里应该从玩家数据管理器获取等级
        // 暂时返回固定值用于测试
        return 1;
    }

    /**
     * 检查必需道具（需要实现）
     */
    private async checkRequiredItems(requiredItems: string[]): Promise<boolean> {
        // 这里应该检查玩家背包中是否有必需道具
        // 暂时返回true用于测试
        console.log(`🎒 检查必需道具: ${requiredItems.join(', ')}`);
        return true;
    }

    /**
     * 检查完成的任务（需要实现）
     */
    private async checkCompletedQuests(completedQuests: string[]): Promise<boolean> {
        // 这里应该检查玩家是否完成了必需任务
        // 暂时返回true用于测试
        console.log(`📋 检查完成的任务: ${completedQuests.join(', ')}`);
        return true;
    }

    /**
     * 检查前置区域（需要实现）
     */
    private async checkRequiredAreas(requiredAreas: string[]): Promise<boolean> {
        // 这里应该检查玩家是否已解锁前置区域
        // 暂时返回true用于测试
        console.log(`🗺️ 检查前置区域: ${requiredAreas.join(', ')}`);
        return true;
    }

    /**
     * 检查自定义条件（需要实现）
     */
    private async checkCustomCondition(condition: ICustomUnlockCondition): Promise<boolean> {
        // 这里应该根据条件类型进行相应的检查
        switch (condition.type) {
            case 'currency':
                // 检查货币数量
                return true;
            case 'achievement':
                // 检查成就
                return true;
            case 'time':
                // 检查时间条件
                return true;
            default:
                console.warn(`⚠️ 未知的自定义条件类型: ${condition.type}`);
                return false;
        }
    }

    /**
     * 执行地点行为
     */
    public async executeLocationBehavior(locationId: string, behaviorId: string): Promise<boolean> {
        try {
            const config = await this.getLocationConfig(locationId);
            if (!config || !config.behaviors) {
                console.error(`❌ 地点${locationId}没有行为配置`);
                return false;
            }

            const behavior = config.behaviors.find(b => b.id === behaviorId);
            if (!behavior) {
                console.error(`❌ 地点${locationId}中未找到行为${behaviorId}`);
                return false;
            }

            // 检查行为执行要求
            const canExecute = await this.checkBehaviorRequirements(behavior);
            if (!canExecute) {
                console.warn(`⚠️ 不满足行为执行要求: ${behaviorId}`);
                return false;
            }

            // 使用BehaviorPanel执行行为
            const behaviorPanel = this.findBehaviorPanel();
            if (behaviorPanel && behaviorPanel.executeBehavior) {
                const behaviorData = this.convertLocationBehaviorToBehaviorData(behavior);
                behaviorPanel.executeBehavior(behaviorData.type, behaviorData);

                console.log(`✅ 地点行为执行成功: ${behaviorId}`);
                return true;
            } else {
                console.warn('⚠️ BehaviorPanel不可用，无法执行行为');
                return false;
            }

        } catch (error) {
            console.error(`❌ 执行地点行为失败: ${behaviorId}`, error);
            return false;
        }
    }

    /**
     * 检查行为执行要求
     */
    private async checkBehaviorRequirements(behavior: ILocationBehavior): Promise<boolean> {
        // 检查等级要求
        const playerLevel = await this.getPlayerLevel();
        if (playerLevel < behavior.requirements.level) {
            return false;
        }

        // 检查道具要求
        if (!await this.checkRequiredItems(behavior.requirements.items)) {
            return false;
        }

        // 检查技能要求（暂时跳过）
        // if (!await this.checkRequiredSkills(behavior.requirements.skills)) {
        //     return false;
        // }

        return true;
    }

    /**
     * 获取地点行为列表
     */
    public async getLocationBehaviors(locationId: string): Promise<ILocationBehavior[]> {
        const config = await this.getLocationConfig(locationId);
        return config?.behaviors || [];
    }

    /**
     * 获取地点解锁状态
     */
    public async getLocationUnlockStatus(locationId: string): Promise<ILocationUnlockStatus | null> {
        const config = await this.getLocationConfig(locationId);
        if (!config) {
            return null;
        }
        return await this.checkLocationUnlockStatus(locationId, config);
    }
}
