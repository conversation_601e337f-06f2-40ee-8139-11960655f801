/**
 * 技能选择面板
 * 负责显示可学习的技能列表和技能详情
 */

import { _decorator, Node, ScrollView, Layout, instantiate, Prefab, resources } from 'cc';
import { BaseUIPanel } from '../base/BaseUIPanel';
import { SkillListItem } from './SkillListItem';
import { SkillDetailView } from './SkillDetailView';
import { SkillManager } from '../../managers/SkillManager';
import { ConfigManager } from '../../managers/ConfigManager';
import { EventManager } from '../../managers/EventManager';
import { UIManager } from '../../managers/UIManager';
import { UIPanelType } from '../types/UITypes';
import { ISkillData, IPlayerSkill } from '../../config/interfaces/ISkillData';

const { ccclass, property } = _decorator;

/**
 * 技能选择面板配置
 */
export interface ISkillSelectionConfig {
    /** 目标技能槽索引 */
    targetSlotIndex?: number;
    
    /** 当前技能ID */
    currentSkillId?: string;
    
    /** 显示模式 */
    displayMode: 'all' | 'learned' | 'available';
    
    /** 是否显示技能详情 */
    showDetails: boolean;
}

@ccclass('SkillSelectionPanel')
export class SkillSelectionPanel extends BaseUIPanel {
    
    @property({ type: Node, tooltip: '技能列表容器' })
    public skillListContainer: Node = null!;
    
    @property({ type: ScrollView, tooltip: '技能列表滚动视图' })
    public skillListScrollView: ScrollView = null!;
    
    @property({ type: Node, tooltip: '技能详情容器' })
    public skillDetailContainer: Node = null!;
    
    @property({ type: Prefab, tooltip: '技能列表项预制体' })
    public skillListItemPrefab: Prefab = null!;
    
    @property({ type: Node, tooltip: '关闭按钮' })
    public closeButton: Node = null!;
    
    @property({ type: Node, tooltip: '确认按钮' })
    public confirmButton: Node = null!;
    
    @property({ type: Node, tooltip: '取消按钮' })
    public cancelButton: Node = null!;
    
    // 管理器引用
    private _skillManager: SkillManager = null!;
    private _configManager: ConfigManager = null!;
    
    // 技能列表项
    private _skillListItems: SkillListItem[] = [];
    
    // 技能详情视图
    private _skillDetailView: SkillDetailView = null!;
    
    // 当前选中的技能
    private _selectedSkillData: ISkillData | null = null;
    
    // 面板配置
    private _panelConfig: ISkillSelectionConfig = {
        displayMode: 'learned',
        showDetails: true
    };
    
    // 技能数据
    private _allSkills: ISkillData[] = [];
    private _learnedSkills: IPlayerSkill[] = [];
    private _availableSkills: ISkillData[] = [];

    protected onPanelLoad(): void {
        console.log('⚔️ SkillSelectionPanel: 面板加载');
        
        // 获取管理器引用
        this._skillManager = SkillManager.getInstance();
        this._configManager = ConfigManager.getInstance();
        
        // 设置面板类型
        this._panelType = UIPanelType.Skills;
    }

    protected onPanelEnable(): void {
        console.log('⚔️ SkillSelectionPanel: 面板启用');
    }

    protected onPanelDisable(): void {
        console.log('⚔️ SkillSelectionPanel: 面板禁用');
    }

    protected onPanelDestroy(): void {
        console.log('⚔️ SkillSelectionPanel: 面板销毁');
        
        // 清理技能列表项
        this.clearSkillListItems();
    }

    protected async initializeUI(): Promise<void> {
        console.log('⚔️ 初始化技能选择面板UI');
        
        try {
            // 初始化技能详情视图
            await this.initializeSkillDetailView();
            
            // 设置滚动视图布局
            this.setupScrollViewLayout();
            
            console.log('✅ 技能选择面板UI初始化完成');
            
        } catch (error) {
            console.error('❌ 技能选择面板UI初始化失败:', error);
            throw error;
        }
    }

    protected bindEvents(): void {
        // 绑定按钮事件
        if (this.closeButton) {
            this.closeButton.on(Node.EventType.TOUCH_END, this.onCloseButtonClick, this);
        }
        
        if (this.confirmButton) {
            this.confirmButton.on(Node.EventType.TOUCH_END, this.onConfirmButtonClick, this);
        }
        
        if (this.cancelButton) {
            this.cancelButton.on(Node.EventType.TOUCH_END, this.onCancelButtonClick, this);
        }
        
        // 监听技能相关事件
        const eventManager = EventManager.getInstance();
        eventManager.on('skill_learned', this.onSkillLearned, this);
        eventManager.on('skill_level_up', this.onSkillLevelUp, this);
        eventManager.on('open_skill_selection', this.onOpenSkillSelection, this);
    }

    protected unbindEvents(): void {
        // 解绑按钮事件
        if (this.closeButton) {
            this.closeButton.off(Node.EventType.TOUCH_END, this.onCloseButtonClick, this);
        }
        
        if (this.confirmButton) {
            this.confirmButton.off(Node.EventType.TOUCH_END, this.onConfirmButtonClick, this);
        }
        
        if (this.cancelButton) {
            this.cancelButton.off(Node.EventType.TOUCH_END, this.onCancelButtonClick, this);
        }
        
        // 解绑技能事件
        const eventManager = EventManager.getInstance();
        eventManager.off('skill_learned', this.onSkillLearned, this);
        eventManager.off('skill_level_up', this.onSkillLevelUp, this);
        eventManager.off('open_skill_selection', this.onOpenSkillSelection, this);
    }

    protected async onInitialize(data?: any): Promise<void> {
        console.log('⚔️ 初始化技能选择面板数据');
        
        // 加载技能数据
        await this.loadSkillData();
    }

    protected async onBeforeShow(data?: any): Promise<void> {
        console.log('⚔️ 技能选择面板显示前处理');
        
        // 更新面板配置
        if (data) {
            this._panelConfig = { ...this._panelConfig, ...data };
        }
        
        // 刷新技能列表
        await this.refreshSkillList();
        
        // 更新按钮状态
        this.updateButtonStates();
    }

    protected async onAfterShow(data?: any): Promise<void> {
        console.log('⚔️ 技能选择面板显示后处理');
    }

    protected async onBeforeHide(): Promise<void> {
        console.log('⚔️ 技能选择面板隐藏前处理');
    }

    protected async onAfterHide(): Promise<void> {
        console.log('⚔️ 技能选择面板隐藏后处理');
        
        // 清空选中状态
        this._selectedSkillData = null;
    }

    protected onRefresh(data?: any): void {
        console.log('⚔️ 刷新技能选择面板');
        
        // 重新加载技能数据
        this.loadSkillData().then(() => {
            this.refreshSkillList();
        });
    }

    /**
     * 初始化技能详情视图
     */
    private async initializeSkillDetailView(): Promise<void> {
        if (!this.skillDetailContainer) {
            return;
        }
        
        // 查找或创建技能详情视图组件
        this._skillDetailView = this.skillDetailContainer.getComponent(SkillDetailView);
        if (!this._skillDetailView) {
            this._skillDetailView = this.skillDetailContainer.addComponent(SkillDetailView);
        }
        
        await this._skillDetailView.initialize();
    }

    /**
     * 设置滚动视图布局
     */
    private setupScrollViewLayout(): void {
        if (!this.skillListScrollView || !this.skillListContainer) {
            return;
        }
        
        // 设置垂直布局
        let layout = this.skillListContainer.getComponent(Layout);
        if (!layout) {
            layout = this.skillListContainer.addComponent(Layout);
        }
        
        layout.type = Layout.Type.VERTICAL;
        layout.spacingY = 5;
        layout.verticalDirection = Layout.VerticalDirection.TOP_TO_BOTTOM;
        layout.horizontalDirection = Layout.HorizontalDirection.CENTER;
    }

    /**
     * 加载技能数据
     */
    private async loadSkillData(): Promise<void> {
        console.log('⚔️ 加载技能数据');
        
        try {
            // 等待配置管理器和技能管理器初始化
            await this._configManager.waitForInitialization();
            await this._skillManager.waitForInitialization();
            
            // 获取所有技能数据
            this._allSkills = this._configManager.getAllSkillData();
            
            // 获取已学会的技能
            this._learnedSkills = this._skillManager.getLearnedSkills();
            
            // 获取可学习的技能（假设玩家等级为10）
            this._availableSkills = this._skillManager.getAvailableSkillsForLevel(10);
            
            console.log(`✅ 技能数据加载完成: 总计${this._allSkills.length}个，已学会${this._learnedSkills.length}个，可学习${this._availableSkills.length}个`);
            
        } catch (error) {
            console.error('❌ 技能数据加载失败:', error);
            throw error;
        }
    }

    /**
     * 刷新技能列表
     */
    private async refreshSkillList(): Promise<void> {
        console.log(`⚔️ 刷新技能列表: ${this._panelConfig.displayMode} 模式`);
        
        // 清理现有列表项
        this.clearSkillListItems();
        
        // 根据显示模式获取要显示的技能
        let skillsToShow: ISkillData[] = [];
        
        switch (this._panelConfig.displayMode) {
            case 'all':
                skillsToShow = this._allSkills;
                break;
            case 'learned':
                skillsToShow = this._learnedSkills.map(playerSkill => 
                    this._configManager.getSkillData(playerSkill.skillId)!
                ).filter(skill => skill !== null);
                break;
            case 'available':
                skillsToShow = this._availableSkills;
                break;
        }
        
        // 创建技能列表项
        for (const skillData of skillsToShow) {
            await this.createSkillListItem(skillData);
        }
        
        console.log(`✅ 技能列表刷新完成: ${this._skillListItems.length} 项`);
    }

    /**
     * 创建技能列表项
     */
    private async createSkillListItem(skillData: ISkillData): Promise<void> {
        try {
            // 如果没有预制体，尝试加载默认预制体
            if (!this.skillListItemPrefab) {
                this.skillListItemPrefab = await this.loadSkillListItemPrefab();
            }
            
            // 实例化列表项节点
            const itemNode = instantiate(this.skillListItemPrefab);
            itemNode.name = `SkillListItem_${skillData.id}`;
            itemNode.setParent(this.skillListContainer);
            
            // 获取列表项组件
            const skillListItem = itemNode.getComponent(SkillListItem);
            if (!skillListItem) {
                throw new Error(`技能列表项预制体缺少SkillListItem组件: ${skillData.id}`);
            }
            
            // 获取玩家技能数据
            const playerSkill = this._skillManager.getPlayerSkill(skillData.id);
            
            // 初始化列表项
            await skillListItem.initialize({
                skillData: skillData,
                playerSkill: playerSkill,
                selectable: true,
                showDetails: this._panelConfig.showDetails
            });
            
            // 绑定选择事件
            skillListItem.onItemSelected = (selectedSkillData: ISkillData) => {
                this.onSkillItemSelected(selectedSkillData);
            };
            
            // 绑定学习事件
            skillListItem.onLearnSkill = (skillId: string) => {
                this.onLearnSkillRequest(skillId);
            };
            
            this._skillListItems.push(skillListItem);
            
        } catch (error) {
            console.error(`❌ 创建技能列表项失败: ${skillData.id}`, error);
        }
    }

    /**
     * 加载技能列表项预制体
     */
    private async loadSkillListItemPrefab(): Promise<Prefab> {
        return new Promise((resolve, reject) => {
            resources.load('ui/skill/SkillListItem', Prefab, (error, prefab) => {
                if (error) {
                    reject(new Error(`加载技能列表项预制体失败: ${error.message}`));
                    return;
                }
                resolve(prefab);
            });
        });
    }

    /**
     * 清理技能列表项
     */
    private clearSkillListItems(): void {
        for (const item of this._skillListItems) {
            if (item && item.node && item.node.isValid) {
                item.node.destroy();
            }
        }
        this._skillListItems = [];
    }

    /**
     * 更新按钮状态
     */
    private updateButtonStates(): void {
        // 更新确认按钮状态
        if (this.confirmButton) {
            const canConfirm = this._selectedSkillData !== null && 
                              this._panelConfig.targetSlotIndex !== undefined;
            this.confirmButton.getComponent('Button')!.interactable = canConfirm;
        }
    }

    // ==================== 事件处理 ====================

    /**
     * 技能项选择事件处理
     */
    private onSkillItemSelected(skillData: ISkillData): void {
        console.log(`⚔️ 选择技能: ${skillData.name}`);
        
        // 更新选中状态
        this._selectedSkillData = skillData;
        
        // 更新其他列表项的选中状态
        for (const item of this._skillListItems) {
            item.setSelected(item.getSkillData()?.id === skillData.id);
        }
        
        // 更新技能详情显示
        if (this._skillDetailView && this._panelConfig.showDetails) {
            const playerSkill = this._skillManager.getPlayerSkill(skillData.id);
            this._skillDetailView.showSkillDetail(skillData, playerSkill);
        }
        
        // 更新按钮状态
        this.updateButtonStates();
    }

    /**
     * 学习技能请求事件处理
     */
    private async onLearnSkillRequest(skillId: string): Promise<void> {
        console.log(`⚔️ 请求学习技能: ${skillId}`);
        
        try {
            const result = await this._skillManager.learnSkill(skillId);
            
            if (result.success) {
                console.log(`✅ 技能学习成功: ${skillId}`);
                
                // 刷新技能列表
                await this.refreshSkillList();
            } else {
                console.warn(`⚠️ 技能学习失败: ${result.error}`);
                // TODO: 显示错误提示
            }
            
        } catch (error) {
            console.error(`❌ 学习技能异常: ${skillId}`, error);
        }
    }

    /**
     * 关闭按钮点击事件处理
     */
    private onCloseButtonClick(): void {
        console.log('⚔️ 点击关闭按钮');
        this.hide();
    }

    /**
     * 确认按钮点击事件处理
     */
    private onConfirmButtonClick(): void {
        console.log('⚔️ 点击确认按钮');
        
        if (this._selectedSkillData && this._panelConfig.targetSlotIndex !== undefined) {
            // 设置技能到指定槽位
            const success = this._skillManager.setSkillSlot(
                this._panelConfig.targetSlotIndex,
                this._selectedSkillData.id
            );
            
            if (success) {
                console.log(`✅ 技能设置成功: 槽位${this._panelConfig.targetSlotIndex} = ${this._selectedSkillData.name}`);
                this.hide();
            } else {
                console.warn('⚠️ 技能设置失败');
                // TODO: 显示错误提示
            }
        }
    }

    /**
     * 取消按钮点击事件处理
     */
    private onCancelButtonClick(): void {
        console.log('⚔️ 点击取消按钮');
        this.hide();
    }

    /**
     * 技能学习事件处理
     */
    private onSkillLearned(eventData: any): void {
        console.log(`⚔️ 技能学习事件: ${eventData.skillId}`);
        
        // 刷新技能列表
        this.refreshSkillList();
    }

    /**
     * 技能升级事件处理
     */
    private onSkillLevelUp(eventData: any): void {
        console.log(`⚔️ 技能升级事件: ${eventData.skillId} Lv.${eventData.newLevel}`);
        
        // 刷新技能列表
        this.refreshSkillList();
    }

    /**
     * 打开技能选择事件处理
     */
    private onOpenSkillSelection(eventData: any): void {
        console.log(`⚔️ 打开技能选择: 槽位${eventData.slotIndex}`);
        
        // 更新面板配置
        this._panelConfig.targetSlotIndex = eventData.slotIndex;
        this._panelConfig.currentSkillId = eventData.currentSkillId;
        
        // 显示面板
        this.show(this._panelConfig);
    }

    // ==================== 公共API ====================

    /**
     * 设置显示模式
     */
    public setDisplayMode(mode: 'all' | 'learned' | 'available'): void {
        if (this._panelConfig.displayMode !== mode) {
            this._panelConfig.displayMode = mode;
            this.refreshSkillList();
        }
    }

    /**
     * 获取当前选中的技能
     */
    public getSelectedSkill(): ISkillData | null {
        return this._selectedSkillData;
    }

    /**
     * 设置目标技能槽
     */
    public setTargetSlot(slotIndex: number): void {
        this._panelConfig.targetSlotIndex = slotIndex;
        this.updateButtonStates();
    }
}
