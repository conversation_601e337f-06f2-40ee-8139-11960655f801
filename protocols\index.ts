/**
 * Copyright (c) 2025 "放置". All rights reserved.
 * 协议总入口 - 统一导出所有协议
 */

// ==================== 共享协议 ====================
export * from './shared/core';
export * from './shared/scene';
export * from './shared/player';

// ==================== 前端协议 ====================
export * from './frontend/ui';
export * from './frontend/business';
export * from './frontend/data';
export * from './frontend/network';

// ==================== 后端协议 ====================
export * from './backend/api';
export * from './backend/database';
export * from './backend/services';

// ==================== 协议版本信息 ====================
export const PROTOCOL_INFO = {
  name: "IdleGame Protocol Suite",
  version: "1.0.0",
  description: "挂机放置类游戏协议套件",
  author: "放置团队",
  lastUpdated: "2025-08-10",
  modules: {
    shared: "1.0.0",
    frontend: "1.0.0", 
    backend: "1.0.0"
  }
} as const;
