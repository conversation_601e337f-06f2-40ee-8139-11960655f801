# Copyright (c) 2025 "放置". All rights reserved.

# 地点区域系统使用指南（方案A实现）

## 📋 概述

本指南详细说明了基于**方案A**实现的地点区域系统，通过扩展现有的`LocationConfigManager`和相关组件，实现了完整的虚拟场景切换功能，包括区域行为绑定、解锁条件管理、与BehaviorPanel的集成等核心功能。

## 🎯 方案A特点

- **基于现有架构扩展**：扩展`LocationConfigManager`和`ILocationConfig`接口
- **保持兼容性**：不破坏现有代码，渐进式增强
- **直接集成**：直接使用现有的`BehaviorPanel`和`SceneSwitchButtons`
- **快速实现**：开发周期短，风险低

## 🏗️ 系统架构

### 核心组件扩展

1. **LocationConfigManager（扩展）**
   - 新增区域解锁条件检查
   - 新增区域行为管理
   - 新增与BehaviorPanel的集成
   - 保持原有地点配置功能

2. **ILocationConfig接口（扩展）**
   - 新增`behaviors`字段：区域行为配置
   - 新增`unlockStatus`字段：解锁状态
   - 扩展`unlockConditions`：支持更多解锁条件

3. **SceneSwitchButtons（扩展）**
   - 新增解锁状态检查方法
   - 新增行为执行方法
   - 保持原有场景切换功能

### 数据流程

```
用户点击切换按钮
        ↓
SceneSwitchButtons.switchLocationInContainer()
        ↓
LocationConfigManager.applyLocationConfigToContainer()
        ↓
检查解锁条件 → 应用地图配置 → 加载行为到BehaviorPanel → 发送切换事件
```

## 🚀 快速开始

### 1. 配置地点数据

在`assets/configs/areas/game_areas.json`中配置地点信息：

```json
{
  "locations": {
    "fishpond": {
      "locationId": "fishpond",
      "name": "鱼塘",
      "description": "宁静的鱼塘，适合钓鱼放松",
      "type": "fishing",
      "level": 1,
      
      "unlockConditions": {
        "playerLevel": 1,
        "requiredItems": [],
        "completedQuests": [],
        "requiredAreas": [],
        "customConditions": []
      },
      
      "behaviors": [
        {
          "id": "fishing_basic",
          "name": "基础钓鱼",
          "description": "使用基础鱼竿进行钓鱼",
          "type": "fishing",
          "duration": 30,
          "cooldown": 5,
          "energyCost": 10,
          "requirements": {
            "items": ["fishing_rod_basic"],
            "skills": [],
            "level": 1
          },
          "rewards": {
            "baseExp": 10,
            "items": [
              { "id": "fish_common", "probability": 0.7, "quantity": [1, 3] }
            ],
            "currency": {
              "gold": [5, 15]
            }
          }
        }
      ]
    }
  }
}
```

### 2. 使用地点切换功能

```typescript
import { LocationConfigManager } from '../managers/LocationConfigManager';

// 获取管理器实例
const locationManager = LocationConfigManager.getInstance();

// 切换到指定地点
const success = await locationManager.applyLocationConfigToContainer('fishpond', mapContainer);

if (success) {
    console.log('地点切换成功');
} else {
    console.log('地点切换失败，可能未解锁');
}
```

### 3. 检查解锁状态

```typescript
// 检查地点解锁状态
const unlockStatus = await locationManager.getLocationUnlockStatus('farm');

if (unlockStatus) {
    console.log('解锁状态:', unlockStatus.isUnlocked);
    console.log('解锁进度:', unlockStatus.unlockProgress);
    console.log('未满足条件:', unlockStatus.unmetConditions);
}
```

### 4. 执行地点行为

```typescript
// 获取地点行为列表
const behaviors = await locationManager.getLocationBehaviors('fishpond');

// 执行指定行为
if (behaviors.length > 0) {
    const success = await locationManager.executeLocationBehavior('fishpond', behaviors[0].id);
    console.log('行为执行结果:', success);
}
```

## 📝 配置文件详解

### 地点配置结构

```typescript
interface ILocationConfig {
    locationId: string;           // 地点ID
    name: string;                // 地点名称
    description: string;         // 地点描述
    type: string;               // 地点类型
    level: number;              // 地点等级
    
    unlockConditions: {
        playerLevel: number;                    // 玩家等级要求
        requiredItems: string[];               // 必需道具
        completedQuests: string[];             // 完成的任务
        requiredAreas?: string[];              // 前置区域
        customConditions?: ICustomUnlockCondition[]; // 自定义条件
    };
    
    behaviors?: ILocationBehavior[];  // 地点行为配置
    // ... 其他现有字段
}
```

### 行为配置结构

```typescript
interface ILocationBehavior {
    id: string;                 // 行为ID
    name: string;              // 行为名称
    description: string;       // 行为描述
    type: string;             // 行为类型：fishing, farming, logging等
    duration: number;         // 执行时间（秒）
    cooldown: number;         // 冷却时间（秒）
    energyCost: number;       // 能量消耗
    
    requirements: {
        items: string[];      // 必需道具
        skills: string[];     // 必需技能
        level: number;        // 等级要求
    };
    
    rewards: {
        baseExp: number;      // 基础经验
        items: ILocationBehaviorItemReward[];  // 道具奖励
        currency: { [type: string]: number[] }; // 货币奖励
    };
}
```

## 🎮 使用SceneSwitchButtons扩展功能

### 检查解锁状态

```typescript
const sceneSwitchButtons = this.getComponent(SceneSwitchButtons);

// 检查地点解锁状态
await sceneSwitchButtons.checkLocationUnlockStatus('farm');
```

### 执行地点行为

```typescript
// 执行指定地点的指定行为
await sceneSwitchButtons.executeLocationBehavior('fishpond', 'fishing_basic');

// 执行地点的第一个可用行为
await sceneSwitchButtons.executeFirstAvailableBehavior('fishpond');
```

## 🧪 测试与调试

### 使用测试组件

1. 将`LocationSystemTest`组件添加到场景中的任意节点
2. 运行游戏后使用快捷键进行测试：

```
T - 运行基础测试
1 - 切换到鱼塘
2 - 切换到农场  
3 - 切换到森林
U - 检查所有地点解锁状态
B - 执行当前地点第一个行为
L - 显示所有地点行为列表
```

### 调试技巧

1. **启用详细日志**
   ```typescript
   // 在测试组件中设置
   enableDetailedLogging = true;
   ```

2. **检查管理器状态**
   ```typescript
   const locationManager = LocationConfigManager.getInstance();
   console.log('缓存统计:', locationManager.getLocationConfigCacheStats());
   ```

3. **监控配置加载**
   ```typescript
   const config = await locationManager.getLocationConfig('fishpond');
   console.log('地点配置:', config);
   ```

## 🔧 自定义扩展

### 添加自定义解锁条件

在配置文件中添加自定义条件：

```json
{
  "unlockConditions": {
    "customConditions": [
      {
        "type": "currency",
        "params": {
          "currency": "gold",
          "amount": 1000
        },
        "description": "需要拥有1000金币"
      }
    ]
  }
}
```

在`LocationConfigManager.checkCustomCondition()`方法中处理：

```typescript
private async checkCustomCondition(condition: ICustomUnlockCondition): Promise<boolean> {
    switch (condition.type) {
        case 'currency':
            // 检查货币数量
            const playerGold = await this.getPlayerCurrency(condition.params.currency);
            return playerGold >= condition.params.amount;
        // 添加更多自定义条件类型
    }
}
```

### 扩展行为类型

1. 在配置文件中定义新的行为类型
2. 在`convertLocationBehaviorToBehaviorData()`方法中添加类型映射
3. 确保BehaviorPanel支持新的行为类型

## 📈 性能优化

### 1. 配置缓存
- LocationConfigManager自动缓存配置，减少重复加载
- 缓存有过期时间，确保数据新鲜度

### 2. 按需加载
- 只在需要时加载地点配置
- 解锁状态缓存，避免重复计算

### 3. 事件优化
- 使用现有的EventManager进行事件通信
- 及时清理事件监听，避免内存泄漏

## ⚠️ 注意事项

1. **兼容性**
   - 扩展的接口保持向后兼容
   - 新增字段都是可选的，不影响现有功能

2. **错误处理**
   - 所有异步操作都有错误处理
   - 配置文件格式错误时提供友好提示

3. **数据一致性**
   - 解锁条件检查基于实时数据
   - 行为执行前会重新检查要求

4. **测试覆盖**
   - 使用LocationSystemTest进行功能测试
   - 确保所有扩展功能正常工作

## 🔗 相关文档

- [LinearBranchMapContainer使用指南](./LINEAR_BRANCH_MAP_GUIDE.md)
- [BehaviorPanel集成指南](./BEHAVIOR_PANEL_GUIDE.md)
- [配置表结构设计文档](./Reports/Config_Table_Structure_Documentation.md)

---

**文档版本**: v1.0.0  
**最后更新**: 2025-01-10  
**实现方案**: 方案A（基于现有架构扩展）  
**维护者**: 爱豆团队
