import { _decorator, Component, Node, director, Scene, assetManager, AssetManager } from 'cc';
import { BaseManager } from './BaseManager';
import { ConfigManager, ISceneConfig } from './ConfigManager';
import { PerformanceMonitor } from '../tools/PerformanceMonitor';
import { SceneData, TransitionType, SceneManagerConfig } from './types/ManagerTypes';

const { ccclass, property } = _decorator;

/**
 * 场景管理器
 * 负责场景的加载、切换和状态管理
 */
@ccclass('SceneManager')
export class SceneManager extends BaseManager {
    
    /**
     * 当前场景数据
     */
    private _currentScene: SceneData | null = null;
    
    /**
     * 场景历史记录
     */
    private _sceneHistory: SceneData[] = [];
    
    /**
     * 场景管理器配置
     */
    private _config: SceneManagerConfig = {
        preloadScenes: ['Launch', 'Main', 'Battle'],
        defaultTransition: TransitionType.FADE,
        transitionDuration: 1000,
        debug: true,
        autoInit: true
    };
    
    /**
     * 是否正在切换场景
     */
    private _isTransitioning: boolean = false;
    
    /**
     * 预加载的场景缓存
     */
    private _preloadedScenes: Set<string> = new Set();

    /**
     * 已加载的分包缓存
     */
    private _loadedBundles: Map<string, AssetManager.Bundle> = new Map();

    /**
     * 场景配置缓存
     */
    private _sceneConfigs: Map<string, ISceneConfig> = new Map();

    /**
     * 分包加载Promise缓存
     */
    private _bundleLoadingPromises: Map<string, Promise<AssetManager.Bundle>> = new Map();

    /**
     * 获取SceneManager单例实例
     */
    public static getInstance(): SceneManager {
        return super.getInstance.call(this) as SceneManager;
    }

    /**
     * 初始化场景管理器
     */
    protected async initializeManager(): Promise<void> {
        console.log('🎬 初始化场景管理器...');
        
        try {
            // 注册场景事件监听器
            this.registerSceneEvents();
            
            // 获取当前场景信息
            this.updateCurrentSceneInfo();
            
            // 预加载场景
            await this.preloadScenes();
            
            console.log('✅ 场景管理器初始化完成');
            
        } catch (error) {
            console.error('❌ 场景管理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 销毁场景管理器
     */
    public destroyManager(): void {
        console.log('🗑️ 销毁场景管理器...');
        
        // 移除场景事件监听器
        this.unregisterSceneEvents();
        
        // 清理数据
        this._currentScene = null;
        this._sceneHistory = [];
        this._isTransitioning = false;
        this._preloadedScenes.clear();
        
        console.log('✅ 场景管理器销毁完成');
    }

    /**
     * 注册场景事件监听器
     */
    private registerSceneEvents(): void {
        console.log('📡 注册场景事件监听器...');
        
        // 监听场景启动前事件
        director.on(director.EVENT_BEFORE_SCENE_LAUNCH, this.onBeforeSceneLaunch, this);
        
        // 监听场景启动后事件
        director.on(director.EVENT_AFTER_SCENE_LAUNCH, this.onAfterSceneLaunch, this);
    }

    /**
     * 移除场景事件监听器
     */
    private unregisterSceneEvents(): void {
        console.log('📡 移除场景事件监听器...');
        
        director.off(director.EVENT_BEFORE_SCENE_LAUNCH, this.onBeforeSceneLaunch, this);
        director.off(director.EVENT_AFTER_SCENE_LAUNCH, this.onAfterSceneLaunch, this);
    }

    /**
     * 场景启动前事件处理
     */
    private onBeforeSceneLaunch(scene: Scene): void {
        console.log(`🎬 场景启动前: ${scene.name}`);
        this._isTransitioning = true;
    }

    /**
     * 场景启动后事件处理
     */
    private onAfterSceneLaunch(scene: Scene): void {
        console.log(`🎬 场景启动后: ${scene.name}`);
        this._isTransitioning = false;
        this.updateCurrentSceneInfo();
    }

    /**
     * 更新当前场景信息
     */
    private updateCurrentSceneInfo(): void {
        const currentScene = director.getScene();
        if (currentScene) {
            const sceneData: SceneData = {
                name: currentScene.name,
                loadTime: Date.now(),
                previousScene: this._currentScene?.name
            };
            
            // 添加到历史记录
            if (this._currentScene) {
                this._sceneHistory.push(this._currentScene);
                
                // 限制历史记录长度
                if (this._sceneHistory.length > 10) {
                    this._sceneHistory.shift();
                }
            }
            
            this._currentScene = sceneData;
            console.log(`📍 当前场景更新: ${sceneData.name}`);
        }
    }

    /**
     * 预加载场景
     */
    private async preloadScenes(): Promise<void> {
        console.log('📦 预加载场景...');
        
        const preloadPromises = this._config.preloadScenes?.map(sceneName => 
            this.preloadScene(sceneName)
        ) || [];
        
        try {
            await Promise.all(preloadPromises);
            console.log('✅ 场景预加载完成');
        } catch (error) {
            console.warn('⚠️ 部分场景预加载失败:', error);
        }
    }

    /**
     * 预加载单个场景
     */
    private async preloadScene(sceneName: string): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            director.preloadScene(sceneName, (error) => {
                if (error) {
                    console.warn(`⚠️ 场景预加载失败: ${sceneName}`, error);
                    reject(error);
                } else {
                    this._preloadedScenes.add(sceneName);
                    console.log(`✅ 场景预加载成功: ${sceneName}`);
                    resolve();
                }
            });
        });
    }

    /**
     * 切换场景
     */
    public async switchScene(
        sceneName: string, 
        params?: any, 
        transition: TransitionType = this._config.defaultTransition || TransitionType.FADE
    ): Promise<void> {
        console.log(`🔄 切换场景: ${this._currentScene?.name} → ${sceneName}`);
        
        if (this._isTransitioning) {
            console.warn('⚠️ 正在切换场景中，请稍后再试');
            return;
        }
        
        if (this._currentScene?.name === sceneName) {
            console.warn('⚠️ 目标场景与当前场景相同');
            return;
        }
        
        try {
            this._isTransitioning = true;
            
            // 执行场景切换前的处理
            await this.beforeSceneSwitch(sceneName, params);
            
            // 执行场景切换
            await this.performSceneSwitch(sceneName, transition);
            
            // 执行场景切换后的处理
            await this.afterSceneSwitch(sceneName, params);
            
            console.log(`✅ 场景切换成功: ${sceneName}`);
            
        } catch (error) {
            console.error(`❌ 场景切换失败: ${sceneName}`, error);
            this._isTransitioning = false;
            throw error;
        }
    }

    /**
     * 场景切换前处理
     */
    private async beforeSceneSwitch(sceneName: string, params?: any): Promise<void> {
        console.log(`🔄 场景切换前处理: ${sceneName}`);
        
        // 这里可以添加场景切换前的逻辑
        // 例如：保存当前场景状态、显示加载界面等
        
        // 如果场景未预加载，则进行预加载
        if (!this._preloadedScenes.has(sceneName)) {
            await this.preloadScene(sceneName);
        }
    }

    /**
     * 执行场景切换（支持分包加载）
     */
    private async performSceneSwitch(sceneName: string, transition: TransitionType): Promise<void> {
        console.log(`🎬 执行场景切换: ${sceneName} (${transition})`);

        try {
            // 1. 获取场景配置
            const sceneConfig = await this.getSceneConfig(sceneName);

            // 2. 检查是否需要加载分包
            const bundleName = this.getBundleNameForScene(sceneName);
            if (bundleName) {
                await this.loadBundleIfNeeded(bundleName);
            }

            // 3. 加载场景
            await this.loadSceneFromBundle(sceneName, bundleName);

            // 4. 应用场景配置
            if (sceneConfig) {
                await this.applySceneConfig(sceneConfig);
            }

            console.log(`✅ 场景切换成功: ${sceneName}`);
        } catch (error) {
            console.error(`❌ 场景切换失败: ${sceneName}`, error);
            throw error;
        }
    }

    /**
     * 获取场景对应的分包名称
     */
    private getBundleNameForScene(sceneName: string): string | null {
        // 根据场景名称确定分包
        const sceneBundleMap: { [key: string]: string } = {
            'Forest': 'scenes',
            'Desert': 'scenes',
            'Mountain': 'scenes',
            'Battle': 'scenes'
        };

        return sceneBundleMap[sceneName] || null;
    }

    /**
     * 加载分包（如果需要）
     */
    private async loadBundleIfNeeded(bundleName: string): Promise<AssetManager.Bundle> {
        // 检查是否已加载
        if (this._loadedBundles.has(bundleName)) {
            return this._loadedBundles.get(bundleName)!;
        }

        // 检查是否正在加载
        if (this._bundleLoadingPromises.has(bundleName)) {
            return this._bundleLoadingPromises.get(bundleName)!;
        }

        console.log(`📦 开始加载分包: ${bundleName}`);

        // 开始性能监控
        const performanceMonitor = PerformanceMonitor.getInstance();
        const loadId = performanceMonitor?.startBundleLoad(bundleName);

        // 开始加载分包
        const loadPromise = new Promise<AssetManager.Bundle>((resolve, reject) => {
            assetManager.loadBundle(bundleName, (error, bundle) => {
                if (error) {
                    console.error(`❌ 分包加载失败: ${bundleName}`, error);
                    // 记录加载失败
                    if (performanceMonitor && loadId) {
                        performanceMonitor.endBundleLoad(bundleName, loadId, false, error.message);
                    }
                    reject(error);
                } else {
                    console.log(`✅ 分包加载成功: ${bundleName}`);
                    this._loadedBundles.set(bundleName, bundle);
                    // 记录加载成功
                    if (performanceMonitor && loadId) {
                        performanceMonitor.endBundleLoad(bundleName, loadId, true);
                    }
                    resolve(bundle);
                }
            });
        });

        this._bundleLoadingPromises.set(bundleName, loadPromise);

        try {
            const bundle = await loadPromise;
            return bundle;
        } finally {
            this._bundleLoadingPromises.delete(bundleName);
        }
    }

    /**
     * 从分包加载场景
     */
    private async loadSceneFromBundle(sceneName: string, bundleName: string | null): Promise<void> {
        // 开始性能监控
        const performanceMonitor = PerformanceMonitor.getInstance();
        const loadId = performanceMonitor?.startSceneLoad(sceneName);

        return new Promise<void>((resolve, reject) => {
            if (bundleName && this._loadedBundles.has(bundleName)) {
                // 从分包加载场景
                const bundle = this._loadedBundles.get(bundleName)!;
                bundle.loadScene(sceneName, (error, sceneAsset) => {
                    if (error) {
                        console.error(`❌ 从分包加载场景失败: ${sceneName}`, error);
                        // 记录加载失败
                        if (performanceMonitor && loadId) {
                            performanceMonitor.endSceneLoad(sceneName, loadId, false, error.message);
                        }
                        reject(error);
                    } else {
                        director.runScene(sceneAsset);
                        console.log(`✅ 从分包加载场景成功: ${sceneName}`);
                        // 记录加载成功
                        if (performanceMonitor && loadId) {
                            performanceMonitor.endSceneLoad(sceneName, loadId, true);
                        }
                        resolve();
                    }
                });
            } else {
                // 从主包加载场景
                director.loadScene(sceneName, (error) => {
                    if (error) {
                        console.error(`❌ 从主包加载场景失败: ${sceneName}`, error);
                        // 记录加载失败
                        if (performanceMonitor && loadId) {
                            performanceMonitor.endSceneLoad(sceneName, loadId, false, error.message);
                        }
                        reject(error);
                    } else {
                        console.log(`✅ 从主包加载场景成功: ${sceneName}`);
                        // 记录加载成功
                        if (performanceMonitor && loadId) {
                            performanceMonitor.endSceneLoad(sceneName, loadId, true);
                        }
                        resolve();
                    }
                });
            }
        });
    }

    /**
     * 场景切换后处理
     */
    private async afterSceneSwitch(sceneName: string, params?: any): Promise<void> {
        console.log(`🔄 场景切换后处理: ${sceneName}`);
        
        // 这里可以添加场景切换后的逻辑
        // 例如：隐藏加载界面、初始化场景数据等
        
        // 触发场景切换完成事件
        // EventManager.getInstance().emit('sceneChanged', sceneName, params);
    }

    /**
     * 返回上一个场景
     */
    public async goBack(): Promise<void> {
        if (this._sceneHistory.length === 0) {
            console.warn('⚠️ 没有历史场景可以返回');
            return;
        }
        
        const previousScene = this._sceneHistory.pop();
        if (previousScene) {
            await this.switchScene(previousScene.name, previousScene.params);
        }
    }

    /**
     * 重新加载当前场景
     */
    public async reloadCurrentScene(): Promise<void> {
        if (!this._currentScene) {
            console.warn('⚠️ 没有当前场景可以重新加载');
            return;
        }
        
        const sceneName = this._currentScene.name;
        const params = this._currentScene.params;
        
        console.log(`🔄 重新加载当前场景: ${sceneName}`);
        await this.switchScene(sceneName, params);
    }

    /**
     * 获取当前场景信息
     */
    public getCurrentScene(): SceneData | null {
        return this._currentScene ? { ...this._currentScene } : null;
    }

    /**
     * 获取场景历史记录
     */
    public getSceneHistory(): SceneData[] {
        return [...this._sceneHistory];
    }

    /**
     * 检查场景是否已预加载
     */
    public isScenePreloaded(sceneName: string): boolean {
        return this._preloadedScenes.has(sceneName);
    }

    /**
     * 检查是否正在切换场景
     */
    public isTransitioning(): boolean {
        return this._isTransitioning;
    }

    /**
     * 获取场景管理器配置
     */
    public getConfig(): SceneManagerConfig {
        return { ...this._config };
    }

    /**
     * 更新场景管理器配置
     */
    public updateConfig(config: Partial<SceneManagerConfig>): void {
        this._config = { ...this._config, ...config };
        console.log('⚙️ 场景管理器配置已更新:', config);
    }

    /**
     * 清理场景历史记录
     */
    public clearHistory(): void {
        this._sceneHistory = [];
        console.log('🗑️ 场景历史记录已清理');
    }

    // ==================== 场景配置管理 ====================

    /**
     * 获取场景配置
     */
    private async getSceneConfig(sceneName: string): Promise<ISceneConfig | null> {
        // 检查缓存
        if (this._sceneConfigs.has(sceneName)) {
            return this._sceneConfigs.get(sceneName)!;
        }

        try {
            const configManager = ConfigManager.getInstance();
            const config = await configManager.getSceneConfig(sceneName);

            if (config) {
                this._sceneConfigs.set(sceneName, config);
                console.log(`📋 获取场景配置成功: ${sceneName}`);
            }

            return config;
        } catch (error) {
            console.error(`❌ 获取场景配置失败: ${sceneName}`, error);
            return null;
        }
    }

    /**
     * 应用场景配置
     */
    private async applySceneConfig(config: ISceneConfig): Promise<void> {
        try {
            console.log(`🎨 应用场景配置: ${config.sceneId}`);

            // 这里可以根据配置数据设置场景
            // 例如：背景音乐、环境设置、UI元素等
            const sceneData = config.data;

            if (sceneData.environment) {
                await this.applyEnvironmentSettings(sceneData.environment);
            }

            if (sceneData.ui) {
                await this.applyUISettings(sceneData.ui);
            }

            console.log(`✅ 场景配置应用完成: ${config.sceneId}`);
        } catch (error) {
            console.error(`❌ 应用场景配置失败: ${config.sceneId}`, error);
        }
    }

    /**
     * 应用环境设置
     */
    private async applyEnvironmentSettings(environment: any): Promise<void> {
        console.log('🌍 应用环境设置:', environment);

        // 设置背景音乐
        if (environment.backgroundMusic) {
            // TODO: 播放背景音乐
            console.log(`🎵 设置背景音乐: ${environment.backgroundMusic}`);
        }

        // 设置环境音效
        if (environment.ambientSounds) {
            // TODO: 播放环境音效
            console.log(`🔊 设置环境音效:`, environment.ambientSounds);
        }
    }

    /**
     * 应用UI设置
     */
    private async applyUISettings(ui: any): Promise<void> {
        console.log('🎨 应用UI设置:', ui);

        // 设置背景图片
        if (ui.backgroundImage) {
            // TODO: 设置背景图片
            console.log(`🖼️ 设置背景图片: ${ui.backgroundImage}`);
        }

        // 创建UI元素
        if (ui.uiElements && Array.isArray(ui.uiElements)) {
            for (const element of ui.uiElements) {
                await this.createUIElement(element);
            }
        }
    }

    /**
     * 创建UI元素
     */
    private async createUIElement(element: any): Promise<void> {
        console.log('🔧 创建UI元素:', element);

        // TODO: 根据元素配置创建UI组件
        // 这里可以根据element.type创建不同类型的UI元素
    }

    /**
     * 预加载场景配置
     */
    public async preloadSceneConfigs(sceneNames: string[]): Promise<void> {
        console.log('📋 预加载场景配置:', sceneNames);

        try {
            const configManager = ConfigManager.getInstance();
            const configs = await configManager.getSceneConfigsBatch(sceneNames);

            for (const [sceneName, config] of configs) {
                this._sceneConfigs.set(sceneName, config);
            }

            console.log(`✅ 预加载 ${configs.size} 个场景配置完成`);
        } catch (error) {
            console.error('❌ 预加载场景配置失败:', error);
        }
    }

    /**
     * 清除场景配置缓存
     */
    public clearSceneConfigCache(): void {
        this._sceneConfigs.clear();
        console.log('🗑️ 场景配置缓存已清理');
    }

    /**
     * 获取已加载的分包信息
     */
    public getBundleInfo(): { [bundleName: string]: boolean } {
        const info: { [bundleName: string]: boolean } = {};
        for (const bundleName of this._loadedBundles.keys()) {
            info[bundleName] = true;
        }
        return info;
    }
}
