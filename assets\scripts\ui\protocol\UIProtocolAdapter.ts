/**
 * Copyright (c) 2025 "放置". All rights reserved.
 * UI层协议适配器 - 处理UI事件到业务协议的转换
 */

import { _decorator, Component, Button, Node } from 'cc';
import { ProtocolRouter, ProtocolMessage, ProtocolLayer, ProtocolUtils } from '../../core/protocol/ProtocolRouter';
import {
  MapProtocolType,
  MapProtocolUtils,
  MapButtonClickedPayload,
  MapButtonInfoRequestedPayload
} from '../../../protocols/shared/scene/scene.messages';
import { MapButtonData } from '../../../protocols/shared/scene/scene.types';

const { ccclass, property } = _decorator;

// ==================== UI协议类型定义 ====================

/**
 * UI事件协议类型
 */
export enum UIProtocolType {
  AREA_BUTTON_CLICKED = "ui.area.button.clicked",
  AREA_INFO_REQUESTED = "ui.area.info.requested",
  AREA_UNLOCK_REQUESTED = "ui.area.unlock.requested",
  PLAYER_STATUS_UPDATED = "ui.player.status.updated"
}

/**
 * 区域按钮点击事件
 */
export interface AreaButtonClickedPayload {
  areaId: string;                      // 区域ID（如：fishpond, farm, forest）
  buttonType: "enter" | "info" | "unlock"; // 按钮类型
  playerData: {
    playerId: string;
    level: number;
    currentAreaId?: string;
  };
  uiContext: {
    componentId: string;               // UI组件ID
    buttonNode: string;                // 按钮节点名称
    clickPosition: { x: number; y: number }; // 点击位置
  };
}

/**
 * 区域信息请求事件
 */
export interface AreaInfoRequestedPayload {
  areaId: string;
  requestType: "basic" | "detailed" | "unlock_conditions";
  playerData: {
    playerId: string;
    level: number;
    unlockedAreas: string[];
  };
}

// ==================== UI协议适配器 ====================

/**
 * UI协议适配器 - 将UI事件转换为标准协议消息
 */
@ccclass('UIProtocolAdapter')
export class UIProtocolAdapter extends Component {
  private router: ProtocolRouter = null!;

  onLoad() {
    this.router = ProtocolRouter.getInstance();
    console.log('🔌 UI协议适配器已初始化');
  }

  /**
   * 处理地图按钮点击事件（新协议版本）
   */
  public handleMapButtonClick(
    buttonData: MapButtonData,
    playerData: any,
    buttonNode: Node
  ): void {
    console.log(`🗺️ 地图按钮点击: ${buttonData.displayName} (${buttonData.locationId})`);

    // 构建地图按钮点击载荷
    const payload: MapButtonClickedPayload = {
      buttonData,
      playerData: {
        playerId: playerData.playerId || "player_001",
        level: playerData.level || 1,
        currentLocationId: playerData.currentLocationId
      },
      uiContext: {
        componentId: this.node?.uuid || "unknown",
        buttonNode: buttonNode.name,
        clickPosition: buttonNode.getWorldPosition ? buttonNode.getWorldPosition() : { x: 0, y: 0 },
        timestamp: Date.now()
      }
    };

    // 创建地图按钮点击消息
    const message = MapProtocolUtils.createMapButtonClickedMessage(
      payload.buttonData,
      payload.playerData,
      payload.uiContext
    );

    // 发送到业务层
    this.router.sendWithCallback(message, (response) => {
      this.handleMapButtonResponse(response, buttonData);
    });
  }

  /**
   * 处理区域按钮点击事件（兼容旧版本）
   */
  public handleAreaButtonClick(
    areaId: string,
    buttonType: "enter" | "info" | "unlock",
    buttonNode: Node,
    playerData: any
  ): void {
    console.log(`🖱️ 区域按钮点击: ${areaId} (${buttonType})`);

    // 转换为新的地图按钮数据格式
    const buttonData: MapButtonData = {
      locationId: areaId,
      buttonName: buttonNode.name,
      displayName: areaId,
      isUnlocked: true, // 假设已解锁，实际应该检查
      unlockProgress: 1.0,
      buttonType: buttonType
    };

    // 使用新的地图按钮点击处理
    this.handleMapButtonClick(buttonData, playerData, buttonNode);
  }

  /**
   * 处理区域信息请求
   */
  public requestAreaInfo(
    areaId: string,
    requestType: "basic" | "detailed" | "unlock_conditions",
    playerData: any
  ): void {
    console.log(`ℹ️ 请求区域信息: ${areaId} (${requestType})`);

    const payload: AreaInfoRequestedPayload = {
      areaId,
      requestType,
      playerData: {
        playerId: playerData.playerId || "player_001",
        level: playerData.level || 1,
        unlockedAreas: playerData.unlockedAreas || []
      }
    };

    const message = ProtocolUtils.createMessage(
      UIProtocolType.AREA_INFO_REQUESTED,
      ProtocolLayer.UI,
      payload,
      {
        userId: playerData.playerId,
        priority: "normal",
        timeout: 3000
      }
    );

    this.router.sendWithCallback(message, (response) => {
      this.handleAreaInfoResponse(response, areaId, requestType);
    });
  }

  /**
   * 批量请求多个区域信息
   */
  public requestMultipleAreaInfo(
    areaIds: string[],
    playerData: any
  ): void {
    console.log(`📋 批量请求区域信息: ${areaIds.join(', ')}`);

    // 并行发送多个请求
    areaIds.forEach(areaId => {
      this.requestAreaInfo(areaId, "basic", playerData);
    });
  }

  /**
   * 更新玩家状态显示
   */
  public updatePlayerStatus(playerData: any): void {
    const payload = {
      playerId: playerData.playerId,
      level: playerData.level,
      exp: playerData.exp,
      gold: playerData.gold,
      energy: playerData.energy,
      currentAreaId: playerData.currentAreaId,
      updateType: "status_change"
    };

    const message = ProtocolUtils.createMessage(
      UIProtocolType.PLAYER_STATUS_UPDATED,
      ProtocolLayer.UI,
      payload,
      {
        userId: playerData.playerId,
        priority: "normal"
      }
    );

    this.router.send(message);
  }

  /**
   * 处理地图按钮响应（新协议版本）
   */
  private handleMapButtonResponse(
    response: any,
    buttonData: MapButtonData
  ): void {
    if (response.success) {
      console.log(`✅ 地图按钮操作成功: ${buttonData.displayName} (${buttonData.buttonType})`);

      switch (buttonData.buttonType) {
        case "enter":
          this.handleMapEnterSuccess(response.data, buttonData);
          break;
        case "info":
          this.handleMapInfoSuccess(response.data, buttonData);
          break;
        case "unlock":
          this.handleMapUnlockSuccess(response.data, buttonData);
          break;
      }
    } else {
      console.error(`❌ 地图按钮操作失败: ${buttonData.displayName}`, response.error);
      this.handleMapButtonError(response.error, buttonData);
    }
  }

  /**
   * 处理区域按钮响应（兼容旧版本）
   */
  private handleAreaButtonResponse(
    response: any,
    areaId: string,
    buttonType: string
  ): void {
    if (response.success) {
      console.log(`✅ 区域按钮操作成功: ${areaId} (${buttonType})`);

      switch (buttonType) {
        case "enter":
          this.handleAreaEnterSuccess(response.data, areaId);
          break;
        case "info":
          this.handleAreaInfoSuccess(response.data, areaId);
          break;
        case "unlock":
          this.handleAreaUnlockSuccess(response.data, areaId);
          break;
      }
    } else {
      console.error(`❌ 区域按钮操作失败: ${areaId} (${buttonType})`, response.error);
      this.handleAreaButtonError(response.error, areaId, buttonType);
    }
  }

  /**
   * 处理区域进入成功
   */
  private handleAreaEnterSuccess(data: any, areaId: string): void {
    console.log(`🚪 进入区域成功: ${areaId}`);
    
    // 触发UI更新事件
    this.node.emit('area-entered', {
      areaId,
      areaData: data.areaData,
      playerState: data.playerState
    });

    // 可以在这里添加进入动画、音效等
    this.playAreaEnterAnimation(areaId);
  }

  /**
   * 处理区域信息成功
   */
  private handleAreaInfoSuccess(data: any, areaId: string): void {
    console.log(`📖 获取区域信息成功: ${areaId}`);
    
    // 触发信息显示事件
    this.node.emit('area-info-received', {
      areaId,
      areaInfo: data.areaInfo,
      unlockStatus: data.unlockStatus
    });
  }

  /**
   * 处理区域解锁成功
   */
  private handleAreaUnlockSuccess(data: any, areaId: string): void {
    console.log(`🔓 区域解锁成功: ${areaId}`);
    
    // 触发解锁动画和UI更新
    this.node.emit('area-unlocked', {
      areaId,
      newAreaData: data.areaData,
      playerState: data.playerState
    });

    this.playAreaUnlockAnimation(areaId);
  }

  /**
   * 处理区域信息响应
   */
  private handleAreaInfoResponse(
    response: any,
    areaId: string,
    requestType: string
  ): void {
    if (response.success) {
      console.log(`✅ 区域信息获取成功: ${areaId} (${requestType})`);
      
      // 根据请求类型处理不同的信息
      switch (requestType) {
        case "basic":
          this.updateAreaBasicInfo(areaId, response.data);
          break;
        case "detailed":
          this.updateAreaDetailedInfo(areaId, response.data);
          break;
        case "unlock_conditions":
          this.updateAreaUnlockConditions(areaId, response.data);
          break;
      }
    } else {
      console.error(`❌ 区域信息获取失败: ${areaId} (${requestType})`, response.error);
      this.handleAreaInfoError(response.error, areaId, requestType);
    }
  }

  /**
   * 处理按钮操作错误
   */
  private handleAreaButtonError(error: any, areaId: string, buttonType: string): void {
    // 根据错误类型显示不同的提示
    switch (error.code) {
      case "AREA_LOCKED":
        this.showAreaLockedMessage(areaId);
        break;
      case "INSUFFICIENT_LEVEL":
        this.showInsufficientLevelMessage(areaId, error.details?.requiredLevel);
        break;
      case "MISSING_ITEMS":
        this.showMissingItemsMessage(areaId, error.details?.requiredItems);
        break;
      default:
        this.showGenericErrorMessage(error.message);
    }
  }

  /**
   * 处理信息获取错误
   */
  private handleAreaInfoError(error: any, areaId: string, requestType: string): void {
    console.error(`区域信息获取错误: ${areaId}`, error);
    // 可以显示默认信息或重试提示
  }

  // ==================== 地图处理方法（新协议版本） ====================

  /**
   * 处理地图进入成功
   */
  private handleMapEnterSuccess(data: any, buttonData: MapButtonData): void {
    console.log(`🗺️ 进入地图成功: ${buttonData.displayName}`);

    // 触发地图切换事件
    if (this.node) {
      this.node.emit('map-entered', {
        locationId: buttonData.locationId,
        buttonData,
        sceneData: data.sceneData,
        playerState: data.playerState
      });
    }

    // 播放进入动画
    this.playMapEnterAnimation(buttonData.locationId);
  }

  /**
   * 处理地图信息成功
   */
  private handleMapInfoSuccess(data: any, buttonData: MapButtonData): void {
    console.log(`📖 获取地图信息成功: ${buttonData.displayName}`);

    // 触发信息显示事件
    if (this.node) {
      this.node.emit('map-info-received', {
        locationId: buttonData.locationId,
        buttonData,
        mapInfo: data.mapInfo,
        unlockStatus: data.unlockStatus
      });
    }
  }

  /**
   * 处理地图解锁成功
   */
  private handleMapUnlockSuccess(data: any, buttonData: MapButtonData): void {
    console.log(`🔓 地图解锁成功: ${buttonData.displayName}`);

    // 触发解锁动画和UI更新
    if (this.node) {
      this.node.emit('map-unlocked', {
        locationId: buttonData.locationId,
        buttonData,
        newMapData: data.mapData,
        playerState: data.playerState
      });
    }

    this.playMapUnlockAnimation(buttonData.locationId);
  }

  /**
   * 处理地图按钮错误
   */
  private handleMapButtonError(error: any, buttonData: MapButtonData): void {
    // 根据错误类型显示不同的提示
    switch (error.code) {
      case "LOCATION_LOCKED":
        this.showLocationLockedMessage(buttonData.locationId);
        break;
      case "INSUFFICIENT_LEVEL":
        this.showInsufficientLevelMessage(buttonData.locationId, error.details?.requiredLevel);
        break;
      case "MISSING_ITEMS":
        this.showMissingItemsMessage(buttonData.locationId, error.details?.requiredItems);
        break;
      default:
        this.showGenericErrorMessage(error.message);
    }
  }

  // ==================== UI更新方法 ====================

  private updateAreaBasicInfo(areaId: string, data: any): void {
    // 更新区域基础信息显示
    if (this.node) {
      this.node.emit('update-area-basic-info', { areaId, data });
    }
  }

  private updateAreaDetailedInfo(areaId: string, data: any): void {
    // 更新区域详细信息显示
    if (this.node) {
      this.node.emit('update-area-detailed-info', { areaId, data });
    }
  }

  private updateAreaUnlockConditions(areaId: string, data: any): void {
    // 更新区域解锁条件显示
    if (this.node) {
      this.node.emit('update-area-unlock-conditions', { areaId, data });
    }
  }

  // ==================== 动画和效果方法 ====================

  private playMapEnterAnimation(locationId: string): void {
    // 播放进入地图的动画效果
    console.log(`🎬 播放地图进入动画: ${locationId}`);
  }

  private playMapUnlockAnimation(locationId: string): void {
    // 播放地图解锁的动画效果
    console.log(`🎬 播放地图解锁动画: ${locationId}`);
  }

  private playAreaEnterAnimation(areaId: string): void {
    // 播放进入区域的动画效果
    console.log(`🎬 播放区域进入动画: ${areaId}`);
  }

  private playAreaUnlockAnimation(areaId: string): void {
    // 播放区域解锁的动画效果
    console.log(`🎬 播放区域解锁动画: ${areaId}`);
  }

  // ==================== 消息提示方法 ====================

  private showLocationLockedMessage(locationId: string): void {
    console.log(`🔒 地点已锁定: ${locationId}`);
    // 显示锁定提示UI
  }

  private showAreaLockedMessage(areaId: string): void {
    console.log(`🔒 区域已锁定: ${areaId}`);
    // 显示锁定提示UI
  }

  private showInsufficientLevelMessage(locationId: string, requiredLevel: number): void {
    console.log(`📊 等级不足: ${locationId} (需要等级 ${requiredLevel})`);
    // 显示等级不足提示UI
  }

  private showMissingItemsMessage(locationId: string, requiredItems: string[]): void {
    console.log(`🎒 缺少物品: ${locationId} (需要 ${requiredItems?.join(', ') || '未知物品'})`);
    // 显示缺少物品提示UI
  }

  private showGenericErrorMessage(message: string): void {
    console.log(`⚠️ 操作失败: ${message}`);
    // 显示通用错误提示UI
  }
}
