#!/bin/bash
# Copyright (c) 2025 "放置". All rights reserved.
# 阿里云服务器自动化部署脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SERVER_IP=""  # 请填入您的服务器IP
SERVER_USER="root"
APP_NAME="idlegame"
APP_DIR="/opt/${APP_NAME}"
BACKUP_DIR="/opt/backup"
NODE_VERSION="18"

# 函数：打印彩色日志
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 函数：检查必要参数
check_prerequisites() {
    log_info "检查部署前置条件..."
    
    if [ -z "$SERVER_IP" ]; then
        log_error "请在脚本中设置 SERVER_IP 变量"
        exit 1
    fi
    
    if ! command -v ssh &> /dev/null; then
        log_error "SSH 客户端未安装"
        exit 1
    fi
    
    if ! command -v scp &> /dev/null; then
        log_error "SCP 客户端未安装"
        exit 1
    fi
    
    log_success "前置条件检查通过"
}

# 函数：测试服务器连接
test_connection() {
    log_info "测试服务器连接..."
    
    if ssh -o ConnectTimeout=10 ${SERVER_USER}@${SERVER_IP} "echo 'Connection test successful'"; then
        log_success "服务器连接正常"
    else
        log_error "无法连接到服务器 ${SERVER_IP}"
        exit 1
    fi
}

# 函数：安装服务器环境
setup_server_environment() {
    log_info "配置服务器环境..."
    
    ssh ${SERVER_USER}@${SERVER_IP} << 'EOF'
        # 更新系统
        apt update && apt upgrade -y
        
        # 安装基础工具
        apt install -y curl wget git nginx
        
        # 安装 Node.js
        curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
        apt-get install -y nodejs
        
        # 安装 PM2
        npm install -g pm2
        
        # 创建应用目录
        mkdir -p /opt/idlegame
        mkdir -p /opt/backup
        
        # 配置防火墙
        ufw allow 22
        ufw allow 80
        ufw allow 443
        ufw allow 3000
        ufw --force enable
        
        echo "服务器环境配置完成"
EOF
    
    log_success "服务器环境配置完成"
}

# 函数：备份现有应用
backup_existing_app() {
    log_info "备份现有应用..."
    
    ssh ${SERVER_USER}@${SERVER_IP} << EOF
        if [ -d "${APP_DIR}" ]; then
            BACKUP_NAME="backup_\$(date +%Y%m%d_%H%M%S)"
            cp -r ${APP_DIR} ${BACKUP_DIR}/\${BACKUP_NAME}
            echo "应用已备份到 ${BACKUP_DIR}/\${BACKUP_NAME}"
        else
            echo "没有找到现有应用，跳过备份"
        fi
EOF
    
    log_success "备份完成"
}

# 函数：上传应用代码
upload_application() {
    log_info "上传应用代码..."
    
    # 创建临时目录
    TEMP_DIR=$(mktemp -d)
    
    # 复制后端代码
    cp -r ../backend/* ${TEMP_DIR}/
    
    # 删除不需要的文件
    rm -rf ${TEMP_DIR}/node_modules
    rm -rf ${TEMP_DIR}/.git
    rm -f ${TEMP_DIR}/.env
    
    # 上传到服务器
    scp -r ${TEMP_DIR}/* ${SERVER_USER}@${SERVER_IP}:${APP_DIR}/
    
    # 清理临时目录
    rm -rf ${TEMP_DIR}
    
    log_success "应用代码上传完成"
}

# 函数：配置应用环境
configure_application() {
    log_info "配置应用环境..."
    
    ssh ${SERVER_USER}@${SERVER_IP} << EOF
        cd ${APP_DIR}
        
        # 安装依赖
        npm install --production
        
        # 创建环境配置文件
        cat > .env << 'ENVEOF'
NODE_ENV=production
PORT=3000
MONGODB_URI=mongodb://localhost:27017/idlegame
API_BASE_URL=http://localhost:3000
CORS_ORIGIN=*
ENVEOF
        
        # 创建 PM2 配置文件
        cat > ecosystem.config.js << 'PMEOF'
module.exports = {
  apps: [{
    name: 'idlegame-api',
    script: 'index.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
};
PMEOF
        
        echo "应用环境配置完成"
EOF
    
    log_success "应用环境配置完成"
}

# 函数：配置 Nginx
configure_nginx() {
    log_info "配置 Nginx..."
    
    ssh ${SERVER_USER}@${SERVER_IP} << 'EOF'
        # 创建 Nginx 配置
        cat > /etc/nginx/sites-available/idlegame << 'NGINXEOF'
server {
    listen 80;
    server_name _;
    
    # API 代理
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS 配置
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type, Authorization";
        
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }
    
    # 健康检查
    location /health {
        proxy_pass http://localhost:3000/health;
    }
    
    # 静态文件
    location / {
        root /var/www/html;
        try_files $uri $uri/ =404;
    }
}
NGINXEOF
        
        # 启用站点
        ln -sf /etc/nginx/sites-available/idlegame /etc/nginx/sites-enabled/
        rm -f /etc/nginx/sites-enabled/default
        
        # 测试配置
        nginx -t
        
        # 重启 Nginx
        systemctl restart nginx
        systemctl enable nginx
        
        echo "Nginx 配置完成"
EOF
    
    log_success "Nginx 配置完成"
}

# 函数：启动应用
start_application() {
    log_info "启动应用..."
    
    ssh ${SERVER_USER}@${SERVER_IP} << EOF
        cd ${APP_DIR}
        
        # 停止现有进程
        pm2 stop all || true
        pm2 delete all || true
        
        # 启动应用
        pm2 start ecosystem.config.js
        
        # 保存 PM2 配置
        pm2 save
        pm2 startup
        
        echo "应用启动完成"
EOF
    
    log_success "应用启动完成"
}

# 函数：验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 等待服务启动
    sleep 5
    
    # 测试健康检查接口
    if curl -f "http://${SERVER_IP}/health" > /dev/null 2>&1; then
        log_success "健康检查通过"
    else
        log_warning "健康检查失败，请检查应用状态"
    fi
    
    # 测试 API 接口
    if curl -f "http://${SERVER_IP}/api/v1/locations/list" > /dev/null 2>&1; then
        log_success "API 接口测试通过"
    else
        log_warning "API 接口测试失败"
    fi
    
    log_info "部署验证完成"
    log_info "应用访问地址: http://${SERVER_IP}"
    log_info "健康检查: http://${SERVER_IP}/health"
    log_info "API 文档: http://${SERVER_IP}/api/v1/locations/list"
}

# 函数：显示部署后信息
show_post_deployment_info() {
    log_info "部署后配置信息："
    echo ""
    echo "🌐 服务器信息："
    echo "   IP地址: ${SERVER_IP}"
    echo "   应用目录: ${APP_DIR}"
    echo "   日志查看: ssh ${SERVER_USER}@${SERVER_IP} 'pm2 logs'"
    echo ""
    echo "🔧 常用命令："
    echo "   重启应用: ssh ${SERVER_USER}@${SERVER_IP} 'pm2 restart all'"
    echo "   查看状态: ssh ${SERVER_USER}@${SERVER_IP} 'pm2 status'"
    echo "   查看日志: ssh ${SERVER_USER}@${SERVER_IP} 'pm2 logs idlegame-api'"
    echo ""
    echo "📋 下一步："
    echo "   1. 配置域名解析（可选）"
    echo "   2. 申请 SSL 证书（可选）"
    echo "   3. 配置 MongoDB 数据库"
    echo "   4. 导入地点配置数据"
    echo ""
}

# 主函数
main() {
    log_info "开始阿里云服务器部署..."
    
    # 检查参数
    if [ -z "$1" ]; then
        log_error "使用方法: $0 <server-ip>"
        log_info "示例: $0 47.96.123.456"
        exit 1
    fi
    
    SERVER_IP=$1
    
    # 执行部署步骤
    check_prerequisites
    test_connection
    setup_server_environment
    backup_existing_app
    upload_application
    configure_application
    configure_nginx
    start_application
    verify_deployment
    show_post_deployment_info
    
    log_success "🎉 部署完成！"
}

# 执行主函数
main "$@"
