/**
 * UI对话框组件
 * 通用的对话框实现，支持确认、取消、自定义按钮等
 */

import { _decorator, Label, RichText } from 'cc';
import { UIPanel } from './UIPanel';
import { UIPanelType } from '../types/UITypes';
import { UIButton } from './UIButton';

const { ccclass, property } = _decorator;

/**
 * 对话框类型枚举
 */
export enum DialogType {
    Alert = 'alert',           // 警告对话框（只有确定按钮）
    Confirm = 'confirm',       // 确认对话框（确定和取消按钮）
    Custom = 'custom'          // 自定义对话框
}

/**
 * 对话框配置接口
 */
export interface IDialogConfig {
    /** 对话框类型 */
    type: DialogType;
    
    /** 标题 */
    title?: string;
    
    /** 内容文本 */
    content: string;
    
    /** 是否支持富文本 */
    richText?: boolean;
    
    /** 确认按钮文本 */
    confirmText?: string;
    
    /** 取消按钮文本 */
    cancelText?: string;
    
    /** 确认回调 */
    onConfirm?: () => void;
    
    /** 取消回调 */
    onCancel?: () => void;
    
    /** 自定义按钮配置 */
    customButtons?: Array<{
        text: string;
        callback: () => void;
        primary?: boolean;
    }>;
    
    /** 是否可以点击遮罩关闭 */
    maskClosable?: boolean;
    
    /** 自动关闭时间（毫秒，0表示不自动关闭） */
    autoCloseTime?: number;
}

@ccclass('UIDialog')
export class UIDialog extends UIPanel {
    
    @property({ type: Label, tooltip: '内容文本标签' })
    public contentLabel: Label | null = null;
    
    @property({ type: RichText, tooltip: '富文本内容' })
    public contentRichText: RichText | null = null;
    
    @property({ type: UIButton, tooltip: '确认按钮' })
    public confirmButton: UIButton | null = null;
    
    @property({ type: UIButton, tooltip: '取消按钮' })
    public cancelButton: UIButton | null = null;
    
    // 私有属性
    private _dialogConfig: IDialogConfig | null = null;
    private _autoCloseTimer: number = 0;

    protected onPanelLoad(): void {
        super.onPanelLoad();
        
        // 设置面板类型
        this._panelType = UIPanelType.Dialog;
        
        // 查找组件
        this.findDialogComponents();
    }

    protected bindEvents(): void {
        super.bindEvents();
        
        // 绑定确认按钮
        if (this.confirmButton) {
            this.confirmButton.setClickCallback(() => {
                this.onConfirmClick();
            });
        }
        
        // 绑定取消按钮
        if (this.cancelButton) {
            this.cancelButton.setClickCallback(() => {
                this.onCancelClick();
            });
        }
    }

    protected async onPanelInitialize(data?: any): Promise<void> {
        if (data && typeof data === 'object') {
            this._dialogConfig = data as IDialogConfig;
            this.applyDialogConfig();
        }
    }

    protected async onPanelBeforeShow(data?: any): Promise<void> {
        if (data && typeof data === 'object') {
            this._dialogConfig = data as IDialogConfig;
            this.applyDialogConfig();
        }
        
        // 启动自动关闭计时器
        this.startAutoCloseTimer();
    }

    protected async onPanelAfterHide(): Promise<void> {
        // 清除自动关闭计时器
        this.clearAutoCloseTimer();
    }

    /**
     * 查找对话框组件
     */
    private findDialogComponents(): void {
        if (!this.contentLabel) {
            this.contentLabel = this.node.getComponentInChildren(Label);
        }
        
        if (!this.contentRichText) {
            this.contentRichText = this.node.getComponentInChildren(RichText);
        }
        
        if (!this.confirmButton) {
            const confirmNode = this.node.getChildByName('ConfirmButton') ||
                               this.node.getChildByName('OKButton') ||
                               this.node.getChildByName('YesButton');
            if (confirmNode) {
                this.confirmButton = confirmNode.getComponent(UIButton);
            }
        }
        
        if (!this.cancelButton) {
            const cancelNode = this.node.getChildByName('CancelButton') ||
                              this.node.getChildByName('NoButton');
            if (cancelNode) {
                this.cancelButton = cancelNode.getComponent(UIButton);
            }
        }
    }

    /**
     * 应用对话框配置
     */
    private applyDialogConfig(): void {
        if (!this._dialogConfig) return;
        
        const config = this._dialogConfig;
        
        // 设置标题
        if (config.title) {
            this.setTitle(config.title);
        }
        
        // 设置内容
        this.setContent(config.content, config.richText);
        
        // 设置按钮
        this.setupButtons(config);
        
        // 设置关闭按钮可见性
        this.setCloseButtonVisible(config.type !== DialogType.Alert);
    }

    /**
     * 设置内容
     */
    private setContent(content: string, useRichText: boolean = false): void {
        if (useRichText && this.contentRichText) {
            this.contentRichText.string = content;
            this.contentRichText.node.active = true;
            
            if (this.contentLabel) {
                this.contentLabel.node.active = false;
            }
        } else if (this.contentLabel) {
            this.contentLabel.string = content;
            this.contentLabel.node.active = true;
            
            if (this.contentRichText) {
                this.contentRichText.node.active = false;
            }
        }
    }

    /**
     * 设置按钮
     */
    private setupButtons(config: IDialogConfig): void {
        switch (config.type) {
            case DialogType.Alert:
                this.setupAlertButtons(config);
                break;
            case DialogType.Confirm:
                this.setupConfirmButtons(config);
                break;
            case DialogType.Custom:
                this.setupCustomButtons(config);
                break;
        }
    }

    /**
     * 设置警告对话框按钮
     */
    private setupAlertButtons(config: IDialogConfig): void {
        if (this.confirmButton) {
            this.confirmButton.setText(config.confirmText || '确定');
            this.confirmButton.setVisible(true);
        }
        
        if (this.cancelButton) {
            this.cancelButton.setVisible(false);
        }
    }

    /**
     * 设置确认对话框按钮
     */
    private setupConfirmButtons(config: IDialogConfig): void {
        if (this.confirmButton) {
            this.confirmButton.setText(config.confirmText || '确定');
            this.confirmButton.setVisible(true);
        }
        
        if (this.cancelButton) {
            this.cancelButton.setText(config.cancelText || '取消');
            this.cancelButton.setVisible(true);
        }
    }

    /**
     * 设置自定义按钮
     */
    private setupCustomButtons(config: IDialogConfig): void {
        // 隐藏默认按钮
        if (this.confirmButton) {
            this.confirmButton.setVisible(false);
        }
        
        if (this.cancelButton) {
            this.cancelButton.setVisible(false);
        }
        
        // 创建自定义按钮
        if (config.customButtons && this.buttonContainer) {
            this.createCustomButtons(config.customButtons);
        }
    }

    /**
     * 创建自定义按钮
     */
    private createCustomButtons(buttons: Array<{ text: string; callback: () => void; primary?: boolean }>): void {
        // 清除现有按钮
        if (this.buttonContainer) {
            this.buttonContainer.removeAllChildren();
        }
        
        // 创建新按钮
        for (const buttonConfig of buttons) {
            // 这里需要动态创建按钮，实际实现时需要预制体支持
            console.log(`创建自定义按钮: ${buttonConfig.text}`);
        }
    }

    /**
     * 确认按钮点击
     */
    private onConfirmClick(): void {
        console.log('对话框确认');
        
        if (this._dialogConfig?.onConfirm) {
            this._dialogConfig.onConfirm();
        }
        
        this.hide();
    }

    /**
     * 取消按钮点击
     */
    private onCancelClick(): void {
        console.log('对话框取消');
        
        if (this._dialogConfig?.onCancel) {
            this._dialogConfig.onCancel();
        }
        
        this.hide();
    }

    /**
     * 启动自动关闭计时器
     */
    private startAutoCloseTimer(): void {
        if (this._dialogConfig?.autoCloseTime && this._dialogConfig.autoCloseTime > 0) {
            this._autoCloseTimer = setTimeout(() => {
                console.log('对话框自动关闭');
                this.hide();
            }, this._dialogConfig.autoCloseTime);
        }
    }

    /**
     * 清除自动关闭计时器
     */
    private clearAutoCloseTimer(): void {
        if (this._autoCloseTimer) {
            clearTimeout(this._autoCloseTimer);
            this._autoCloseTimer = 0;
        }
    }

    // ==================== 静态方法 ====================

    /**
     * 显示警告对话框
     */
    public static showAlert(content: string, title?: string, onConfirm?: () => void): Promise<UIDialog> {
        const config: IDialogConfig = {
            type: DialogType.Alert,
            title: title || '提示',
            content,
            confirmText: '确定',
            onConfirm
        };
        
        // 这里需要通过UIManager显示对话框
        // 实际实现时需要注册对话框预制体
        console.log('显示警告对话框:', config);
        
        return Promise.resolve(new UIDialog());
    }

    /**
     * 显示确认对话框
     */
    public static showConfirm(
        content: string, 
        title?: string, 
        onConfirm?: () => void, 
        onCancel?: () => void
    ): Promise<UIDialog> {
        const config: IDialogConfig = {
            type: DialogType.Confirm,
            title: title || '确认',
            content,
            confirmText: '确定',
            cancelText: '取消',
            onConfirm,
            onCancel
        };
        
        console.log('显示确认对话框:', config);
        
        return Promise.resolve(new UIDialog());
    }

    /**
     * 显示自定义对话框
     */
    public static showCustom(config: IDialogConfig): Promise<UIDialog> {
        console.log('显示自定义对话框:', config);
        
        return Promise.resolve(new UIDialog());
    }
}
