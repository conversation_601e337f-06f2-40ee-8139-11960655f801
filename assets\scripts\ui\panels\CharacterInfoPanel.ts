import { _decorator, Component, Node, Button, Label } from 'cc';
import { BaseUIPanel } from '../base/BaseUIPanel';
import { EventManager } from '../../managers/EventManager';

const { ccclass, property } = _decorator;

/**
 * 角色信息面板
 * 管理右侧的角色信息显示和功能按钮
 */
@ccclass('CharacterInfoPanel')
export class CharacterInfoPanel extends BaseUIPanel {
    
    @property(Node)
    detailInfoButton: Node = null!;
    
    @property(Node)
    inventoryButton: Node = null!;
    
    @property(Node)
    equipmentButton: Node = null!;
    
    @property(Node)
    levelButton: Node = null!;
    
    @property(Node)
    skillButton: Node = null!;

    // 当前选中的功能
    private _currentSelectedFunction: string = '';

    protected onLoad(): void {
        super.onLoad();
        this.initializeButtons();
    }

    /**
     * 初始化按钮事件
     */
    private initializeButtons(): void {
        this.setupButtonEvent(this.detailInfoButton, 'detail_info', '详细信息');
        this.setupButtonEvent(this.inventoryButton, 'inventory', '背包');
        this.setupButtonEvent(this.equipmentButton, 'equipment', '装备');
        this.setupButtonEvent(this.levelButton, 'level', '等级');
        this.setupButtonEvent(this.skillButton, 'skill', '技能');
    }

    /**
     * 设置按钮事件
     */
    private setupButtonEvent(buttonNode: Node, functionId: string, functionName: string): void {
        if (!buttonNode) return;
        
        const button = buttonNode.getComponent(Button);
        if (button) {
            button.node.on(Button.EventType.CLICK, () => {
                this.onFunctionSelected(functionId, functionName);
            }, this);
        }
    }

    /**
     * 功能选择事件处理
     */
    private onFunctionSelected(functionId: string, functionName: string): void {
        console.log(`选择功能: ${functionName} (${functionId})`);
        
        // 更新当前选中功能
        this._currentSelectedFunction = functionId;
        
        // 更新按钮状态
        this.updateButtonStates(functionId);
        
        // 发送功能选择事件
        EventManager.getInstance().emit('character-function-selected', {
            functionId: functionId,
            functionName: functionName
        });
    }

    /**
     * 更新按钮状态
     */
    private updateButtonStates(selectedFunctionId: string): void {
        const buttons = [
            { node: this.detailInfoButton, id: 'detail_info' },
            { node: this.inventoryButton, id: 'inventory' },
            { node: this.equipmentButton, id: 'equipment' },
            { node: this.levelButton, id: 'level' },
            { node: this.skillButton, id: 'skill' }
        ];

        buttons.forEach(buttonInfo => {
            if (buttonInfo.node) {
                const button = buttonInfo.node.getComponent(Button);
                if (button) {
                    // 添加选中状态的视觉效果
                    if (buttonInfo.id === selectedFunctionId) {
                        buttonInfo.node.setScale(1.1, 1.1, 1.0);
                    } else {
                        buttonInfo.node.setScale(1.0, 1.0, 1.0);
                    }
                }
            }
        });
    }

    /**
     * 获取当前选中的功能
     */
    public getCurrentSelectedFunction(): string {
        return this._currentSelectedFunction;
    }

    /**
     * 程序化选择功能
     */
    public selectFunction(functionId: string): void {
        const functionNames: { [key: string]: string } = {
            'detail_info': '详细信息',
            'inventory': '背包',
            'equipment': '装备',
            'level': '等级',
            'skill': '技能'
        };

        const functionName = functionNames[functionId];
        if (functionName) {
            this.onFunctionSelected(functionId, functionName);
        }
    }

    /**
     * 更新角色信息显示
     */
    public updateCharacterInfo(characterData: any): void {
        // 这里可以根据角色数据更新显示
        console.log('更新角色信息:', characterData);
        
        // 发送角色信息更新事件
        EventManager.getInstance().emit('character-info-updated', characterData);
    }

    protected onShow(): void {
        super.onShow();
        // 默认选择详细信息
        if (!this._currentSelectedFunction) {
            this.selectFunction('detail_info');
        }
    }

    protected onHide(): void {
        super.onHide();
    }

    // 实现BaseUIPanel的抽象方法
    protected onPanelLoad(): void {
        // 面板加载时的初始化逻辑
        console.log('CharacterInfoPanel: 面板加载');
    }

    protected onPanelEnable(): void {
        // 面板启用时的逻辑
        console.log('CharacterInfoPanel: 面板启用');
    }

    protected onPanelDisable(): void {
        // 面板禁用时的逻辑
        console.log('CharacterInfoPanel: 面板禁用');
    }

    protected onPanelDestroy(): void {
        // 面板销毁时的清理逻辑
        console.log('CharacterInfoPanel: 面板销毁');
    }

    protected onPanelDestroy(): void {
        // 面板销毁时的清理逻辑
        console.log('CharacterInfoPanel: 面板销毁');

        // 清理事件监听
        const buttons = [
            this.detailInfoButton, this.inventoryButton, this.equipmentButton,
            this.levelButton, this.skillButton
        ];

        buttons.forEach(buttonNode => {
            if (buttonNode) {
                const button = buttonNode.getComponent(Button);
                if (button) {
                    button.node.off(Button.EventType.CLICK);
                }
            }
        });
    }
}
