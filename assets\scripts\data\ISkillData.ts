/**
 * 技能数据接口定义
 * 基于原Godot项目的Skill.xml结构
 */

/**
 * 技能数据接口
 */
export interface ISkillData {
    /** 技能ID */
    id: string;
    
    /** 技能名称 */
    name: string;
    
    /** 技能描述 */
    description: string;
    
    /** 魔法消耗 */
    manaCost: number;
    
    /** 施法时间(毫秒) */
    castTime: number;
    
    /** 冷却时间(毫秒) */
    cooldown: number;
    
    /** 伤害类型 */
    damageType: string;
    
    /** 目标类型 */
    targetType: string;
    
    /** 基础伤害倍数 */
    baseDamageMultiplier: number;
    
    /** 技能等级 */
    level?: number;
    
    /** 最大等级 */
    maxLevel?: number;
    
    /** 学习所需等级 */
    requiredLevel?: number;
    
    /** 学习所需技能点 */
    requiredSkillPoints?: number;
    
    /** 前置技能ID */
    prerequisiteSkills?: string[];
    
    /** 技能图标路径 */
    iconPath?: string;
    
    /** 技能效果列表 */
    effects?: ISkillEffect[];
    
    /** 技能标签 */
    tags?: string[];
    
    /** 是否为被动技能 */
    isPassive?: boolean;
    
    /** 技能范围 */
    range?: number;
    
    /** 作用范围 */
    areaOfEffect?: number;
}

/**
 * 技能效果接口
 */
export interface ISkillEffect {
    /** 效果类型 */
    type: string;
    
    /** 效果值 */
    value: number;
    
    /** 持续时间(毫秒) */
    duration?: number;
    
    /** 效果描述 */
    description?: string;
}

/**
 * 玩家技能接口
 */
export interface IPlayerSkill {
    /** 技能ID */
    skillId: string;
    
    /** 当前等级 */
    level: number;
    
    /** 当前经验值 */
    experience: number;
    
    /** 升级所需经验 */
    experienceToNext: number;
    
    /** 最后使用时间 */
    lastUsed?: Date;
    
    /** 剩余冷却时间(毫秒) */
    remainingCooldown?: number;
    
    /** 是否已解锁 */
    isUnlocked: boolean;
}

/**
 * 技能使用结果接口
 */
export interface ISkillResult {
    /** 是否成功 */
    success: boolean;
    
    /** 结果消息 */
    message: string;
    
    /** 造成的伤害 */
    damage?: number;
    
    /** 治疗量 */
    healing?: number;
    
    /** 消耗的魔法值 */
    manaConsumed: number;
    
    /** 获得的经验值 */
    experienceGained?: number;
    
    /** 技能等级是否提升 */
    levelUp?: boolean;
    
    /** 新的技能等级 */
    newLevel?: number;
    
    /** 冷却时间(毫秒) */
    cooldownTime: number;
    
    /** 影响的目标 */
    affectedTargets?: string[];
    
    /** 触发的效果 */
    triggeredEffects?: ISkillEffect[];
    
    /** 错误信息 */
    error?: string;
}

/**
 * 技能学习结果接口
 */
export interface ISkillLearnResult {
    /** 是否成功 */
    success: boolean;
    
    /** 结果消息 */
    message: string;
    
    /** 学会的技能 */
    learnedSkill?: IPlayerSkill;
    
    /** 消耗的技能点 */
    skillPointsConsumed: number;
    
    /** 剩余技能点 */
    remainingSkillPoints: number;
    
    /** 错误信息 */
    error?: string;
}

/**
 * 技能树节点接口
 */
export interface ISkillTreeNode {
    /** 技能ID */
    skillId: string;
    
    /** 节点位置 */
    position: {
        x: number;
        y: number;
    };
    
    /** 前置技能节点 */
    prerequisites: string[];
    
    /** 后续技能节点 */
    unlocks: string[];
    
    /** 是否可见 */
    isVisible: boolean;
    
    /** 是否可学习 */
    canLearn: boolean;
}

/**
 * 技能配置接口
 */
export interface ISkillConfig {
    /** 技能列表 */
    skills: ISkillData[];
    
    /** 技能树配置 */
    skillTree?: ISkillTreeNode[];
    
    /** 全局技能设置 */
    globalSettings?: {
        /** 基础技能点获取率 */
        baseSkillPointGainRate: number;
        
        /** 最大技能等级 */
        maxSkillLevel: number;
        
        /** 技能冷却时间倍数 */
        cooldownMultiplier: number;
    };
}
