/**
 * Copyright (c) 2025 "放置". All rights reserved.
 * XML到JSON转换工具 - 策划配置专用
 */

const fs = require('fs');
const path = require('path');
const xml2js = require('xml2js');

class XMLToJSONConverter {
  constructor() {
    this.parser = new xml2js.Parser({
      explicitArray: false,
      mergeAttrs: true,
      normalize: true,
      normalizeTags: true,
      trim: true
    });
    
    this.results = [];
    this.errors = [];
  }

  /**
   * 转换单个XML文件到JSON
   */
  async convertFile(xmlFilePath, outputDir) {
    try {
      console.log(`🔄 转换文件: ${xmlFilePath}`);
      
      // 读取XML文件
      const xmlContent = fs.readFileSync(xmlFilePath, 'utf8');
      
      // 解析XML
      const result = await this.parser.parseStringPromise(xmlContent);
      
      // 根据文件类型进行特定转换
      const fileName = path.basename(xmlFilePath, '.xml');
      let jsonData;
      
      switch (fileName) {
        case 'scenes':
          jsonData = this.convertScenesXML(result);
          break;
        case 'monsters':
          jsonData = this.convertMonstersXML(result);
          break;
        case 'items':
          jsonData = this.convertItemsXML(result);
          break;
        case 'skills':
          jsonData = this.convertSkillsXML(result);
          break;
        default:
          jsonData = this.convertGenericXML(result);
      }
      
      // 确保输出目录存在
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }
      
      // 写入JSON文件
      const outputPath = path.join(outputDir, `${fileName}.json`);
      fs.writeFileSync(outputPath, JSON.stringify(jsonData, null, 2), 'utf8');
      
      this.addResult(`✅ 转换成功: ${fileName}.xml → ${fileName}.json`);
      return jsonData;
      
    } catch (error) {
      this.addError(`❌ 转换失败: ${xmlFilePath} - ${error.message}`);
      throw error;
    }
  }

  /**
   * 转换场景XML
   */
  convertScenesXML(xmlData) {
    const scenes = xmlData.gamescenes;
    
    const jsonData = {
      version: scenes.version || "1.0.0",
      config_type: "scenes",
      last_updated: scenes.lastupdated || new Date().toISOString(),
      scenes: {}
    };
    
    // 处理场景数组
    const sceneArray = Array.isArray(scenes.scene) ? scenes.scene : [scenes.scene];
    
    sceneArray.forEach(scene => {
      if (!scene || !scene.id) return;
      
      const sceneData = {
        locationId: scene.id,
        name: scene.basicinfo?.name || "",
        description: scene.basicinfo?.description || "",
        type: scene.basicinfo?.type || "",
        level: parseInt(scene.basicinfo?.level) || 1,
        
        unlockConditions: this.convertUnlockConditions(scene.unlockconditions),
        mapConfig: this.convertMapConfig(scene.mapconfig),
        environment: this.convertEnvironment(scene.environment),
        rewards: this.convertRewards(scene.rewards),
        behaviors: this.convertBehaviors(scene.behaviors),
        uiLayout: this.convertUILayout(scene.uilayout)
      };
      
      jsonData.scenes[scene.id] = sceneData;
    });
    
    return jsonData;
  }

  /**
   * 转换解锁条件
   */
  convertUnlockConditions(unlockConditions) {
    if (!unlockConditions) return {};
    
    return {
      playerLevel: parseInt(unlockConditions.playerlevel) || 1,
      requiredItems: this.convertStringList(unlockConditions.requireditems),
      completedQuests: this.convertStringList(unlockConditions.completedquests),
      requiredAreas: this.convertStringList(unlockConditions.requiredareas)
    };
  }

  /**
   * 转换地图配置
   */
  convertMapConfig(mapConfig) {
    if (!mapConfig) return {};
    
    return {
      backgroundImagePath: mapConfig.backgroundimagepath || "",
      mapSize: {
        width: parseInt(mapConfig.mapsize?.width) || 1600,
        height: parseInt(mapConfig.mapsize?.height) || 1200
      },
      nodeSpacing: {
        x: parseInt(mapConfig.nodespacing?.x) || 200,
        y: parseInt(mapConfig.nodespacing?.y) || 150
      },
      startPosition: {
        x: parseInt(mapConfig.startposition?.x) || 0,
        y: parseInt(mapConfig.startposition?.y) || 0
      },
      branchCount: parseInt(mapConfig.branchcount) || 3,
      nodesPerBranch: parseInt(mapConfig.nodesperbranch) || 5
    };
  }

  /**
   * 转换环境配置
   */
  convertEnvironment(environment) {
    if (!environment) return {};
    
    return {
      weather: environment.weather || "sunny",
      timeOfDay: environment.timeofday || "day",
      backgroundMusic: environment.backgroundmusic || "",
      ambientSounds: this.convertStringList(environment.ambientsounds),
      lightingColor: {
        r: parseInt(environment.lightingcolor?.r) || 255,
        g: parseInt(environment.lightingcolor?.g) || 255,
        b: parseInt(environment.lightingcolor?.b) || 255,
        a: parseInt(environment.lightingcolor?.a) || 255
      }
    };
  }

  /**
   * 转换奖励配置
   */
  convertRewards(rewards) {
    if (!rewards) return {};
    
    return {
      baseExp: parseInt(rewards.baseexp) || 0,
      baseGold: parseInt(rewards.basegold) || 0,
      dropTables: this.convertStringList(rewards.droptables)
    };
  }

  /**
   * 转换行为配置
   */
  convertBehaviors(behaviors) {
    if (!behaviors || !behaviors.behavior) return {};
    
    const behaviorArray = Array.isArray(behaviors.behavior) ? behaviors.behavior : [behaviors.behavior];
    const behaviorData = {};
    
    behaviorArray.forEach(behavior => {
      if (!behavior || !behavior.id) return;
      
      behaviorData[behavior.id] = {
        behaviorId: behavior.id,
        name: behavior.name || "",
        description: behavior.description || "",
        type: behavior.type || "",
        duration: parseInt(behavior.duration) || 30,
        cooldown: parseInt(behavior.cooldown) || 0,
        energyCost: parseInt(behavior.energycost) || 10,
        
        requirements: {
          items: this.convertStringList(behavior.requirements?.items),
          skills: this.convertStringList(behavior.requirements?.skills),
          level: parseInt(behavior.requirements?.level) || 1
        },
        
        rewards: {
          baseExp: parseInt(behavior.rewards?.baseexp) || 0,
          items: this.convertItemRewards(behavior.rewards?.items),
          currency: this.convertCurrency(behavior.rewards?.currency)
        }
      };
    });
    
    return behaviorData;
  }

  /**
   * 转换UI布局
   */
  convertUILayout(uiLayout) {
    if (!uiLayout) return {};
    
    return {
      behaviorButtonsPosition: {
        x: parseInt(uiLayout.behaviorbuttonsposition?.x) || 0,
        y: parseInt(uiLayout.behaviorbuttonsposition?.y) || -300
      },
      infoDisplayPosition: {
        x: parseInt(uiLayout.infodisplayposition?.x) || -400,
        y: parseInt(uiLayout.infodisplayposition?.y) || 250
      },
      customElements: this.convertCustomElements(uiLayout.customelements)
    };
  }

  /**
   * 转换字符串列表
   */
  convertStringList(listData) {
    if (!listData) return [];
    
    if (typeof listData === 'string') return [listData];
    
    if (listData.item) {
      return Array.isArray(listData.item) ? listData.item : [listData.item];
    }
    
    if (listData.quest) {
      return Array.isArray(listData.quest) ? listData.quest : [listData.quest];
    }
    
    if (listData.area) {
      return Array.isArray(listData.area) ? listData.area : [listData.area];
    }
    
    if (listData.sound) {
      return Array.isArray(listData.sound) ? listData.sound : [listData.sound];
    }
    
    if (listData.droptable) {
      return Array.isArray(listData.droptable) ? listData.droptable : [listData.droptable];
    }
    
    return [];
  }

  /**
   * 转换物品奖励
   */
  convertItemRewards(itemsData) {
    if (!itemsData || !itemsData.item) return [];
    
    const itemArray = Array.isArray(itemsData.item) ? itemsData.item : [itemsData.item];
    
    return itemArray.map(item => ({
      id: item.id || "",
      probability: parseFloat(item.probability) || 0,
      minQuantity: parseInt(item.minquantity) || 1,
      maxQuantity: parseInt(item.maxquantity) || 1
    }));
  }

  /**
   * 转换货币奖励
   */
  convertCurrency(currencyData) {
    if (!currencyData) return {};
    
    const result = {};
    
    if (currencyData.gold) {
      result.gold = {
        min: parseInt(currencyData.gold.min) || 0,
        max: parseInt(currencyData.gold.max) || 0
      };
    }
    
    return result;
  }

  /**
   * 转换自定义元素
   */
  convertCustomElements(customElements) {
    if (!customElements || !customElements.element) return [];
    
    const elementArray = Array.isArray(customElements.element) ? customElements.element : [customElements.element];
    
    return elementArray.map(element => ({
      type: element.type || "",
      position: {
        x: parseInt(element.x) || 0,
        y: parseInt(element.y) || 0
      },
      config: element.config || {}
    }));
  }

  /**
   * 转换怪物XML（示例）
   */
  convertMonstersXML(xmlData) {
    // 怪物转换逻辑
    return {
      version: "1.0.0",
      config_type: "monsters",
      last_updated: new Date().toISOString(),
      monsters: {}
    };
  }

  /**
   * 转换物品XML（示例）
   */
  convertItemsXML(xmlData) {
    // 物品转换逻辑
    return {
      version: "1.0.0",
      config_type: "items",
      last_updated: new Date().toISOString(),
      items: {}
    };
  }

  /**
   * 转换技能XML（示例）
   */
  convertSkillsXML(xmlData) {
    // 技能转换逻辑
    return {
      version: "1.0.0",
      config_type: "skills",
      last_updated: new Date().toISOString(),
      skills: {}
    };
  }

  /**
   * 通用XML转换
   */
  convertGenericXML(xmlData) {
    return {
      version: "1.0.0",
      config_type: "generic",
      last_updated: new Date().toISOString(),
      data: xmlData
    };
  }

  /**
   * 批量转换XML文件
   */
  async convertBatch(xmlDir, outputDir) {
    console.log(`🔄 开始批量转换: ${xmlDir} → ${outputDir}\n`);
    
    const xmlFiles = fs.readdirSync(xmlDir).filter(file => file.endsWith('.xml'));
    
    for (const xmlFile of xmlFiles) {
      const xmlPath = path.join(xmlDir, xmlFile);
      try {
        await this.convertFile(xmlPath, outputDir);
      } catch (error) {
        // 错误已在convertFile中记录
      }
    }
    
    this.printResults();
  }

  /**
   * 添加成功结果
   */
  addResult(message) {
    this.results.push(message);
    console.log(message);
  }

  /**
   * 添加错误
   */
  addError(message) {
    this.errors.push(message);
    console.error(message);
  }

  /**
   * 打印转换结果
   */
  printResults() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 XML到JSON转换结果');
    console.log('='.repeat(60));
    
    console.log(`\n✅ 成功转换: ${this.results.length} 个文件`);
    console.log(`❌ 转换失败: ${this.errors.length} 个文件`);
    
    if (this.errors.length > 0) {
      console.log('\n🚨 转换错误:');
      this.errors.forEach(error => console.log(`  ${error}`));
    }
    
    if (this.errors.length === 0) {
      console.log('\n🎉 所有XML文件转换成功！');
    }
    
    console.log('\n' + '='.repeat(60));
  }
}

// 命令行使用
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length < 2) {
    console.log('使用方法: node xml-to-json.js <XML文件或目录> <输出目录>');
    process.exit(1);
  }
  
  const inputPath = args[0];
  const outputDir = args[1];
  
  const converter = new XMLToJSONConverter();
  
  if (fs.statSync(inputPath).isDirectory()) {
    converter.convertBatch(inputPath, outputDir);
  } else {
    converter.convertFile(inputPath, outputDir);
  }
}

module.exports = XMLToJSONConverter;
