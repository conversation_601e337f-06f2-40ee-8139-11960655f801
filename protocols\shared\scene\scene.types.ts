/**
 * Copyright (c) 2025 "放置". All rights reserved.
 * 场景协议类型定义 - 游戏体验场景相关
 */

import { BaseProtocolMessage, BaseProtocolResponse, CachePolicy, QueryConfig } from '../core/base.types';

// ==================== 场景基础类型 ====================

/**
 * 场景ID类型
 */
export type SceneId = string;

/**
 * 场景类型枚举
 */
export enum SceneType {
  FISHING = "fishing",                // 钓鱼场景
  FARMING = "farming",                // 农场场景
  MINING = "mining",                  // 挖矿场景
  LOGGING = "logging",                // 伐木场景
  HUNTING = "hunting",                // 狩猎场景
  CRAFTING = "crafting",              // 制作场景
  TRADING = "trading",                // 交易场景
  BATTLE = "battle",                  // 战斗场景
  SOCIAL = "social",                  // 社交场景
  SPECIAL = "special"                 // 特殊场景
}

/**
 * 场景状态枚举
 */
export enum SceneStatus {
  LOCKED = "locked",                  // 锁定状态
  UNLOCKED = "unlocked",              // 已解锁
  AVAILABLE = "available",            // 可进入
  OCCUPIED = "occupied",              // 占用中
  MAINTENANCE = "maintenance"         // 维护中
}

/**
 * 天气类型枚举
 */
export enum WeatherType {
  SUNNY = "sunny",                    // 晴天
  CLOUDY = "cloudy",                  // 多云
  RAINY = "rainy",                    // 雨天
  SNOWY = "snowy",                    // 雪天
  FOGGY = "foggy",                    // 雾天
  STORMY = "stormy"                   // 暴风雨
}

/**
 * 时间段枚举
 */
export enum TimeOfDay {
  DAWN = "dawn",                      // 黎明
  MORNING = "morning",                // 上午
  NOON = "noon",                      // 中午
  AFTERNOON = "afternoon",            // 下午
  EVENING = "evening",                // 傍晚
  NIGHT = "night",                    // 夜晚
  MIDNIGHT = "midnight"               // 午夜
}

// ==================== 场景配置类型 ====================

/**
 * 场景基础信息
 */
export interface SceneBasicInfo {
  sceneId: SceneId;                   // 场景ID
  name: string;                       // 场景名称
  description: string;                // 场景描述
  type: SceneType;                    // 场景类型
  level: number;                      // 推荐等级
  status: SceneStatus;                // 场景状态
  iconPath?: string;                  // 图标路径
  thumbnailPath?: string;             // 缩略图路径
}

/**
 * 场景解锁条件
 */
export interface SceneUnlockConditions {
  playerLevel: number;                // 玩家等级要求
  requiredItems: Array<{              // 需要的物品
    itemId: string;
    quantity: number;
  }>;
  completedQuests: string[];          // 需要完成的任务
  requiredScenes: SceneId[];          // 前置场景
  requiredAchievements?: string[];    // 需要的成就
  unlockCost?: {                      // 解锁费用
    gold?: number;
    gems?: number;
    items?: Array<{ itemId: string; quantity: number }>;
  };
}

/**
 * 场景地图配置
 */
export interface SceneMapConfig {
  backgroundImagePath: string;        // 背景图片路径
  mapSize: {                          // 地图尺寸
    width: number;
    height: number;
  };
  nodeSpacing: {                      // 节点间距
    x: number;
    y: number;
  };
  startPosition: {                    // 起始位置
    x: number;
    y: number;
  };
  branchCount: number;                // 分支数量
  nodesPerBranch: number;             // 每个分支的节点数
  pathConfig?: {                      // 路径配置
    pathWidth: number;
    pathColor: string;
    pathStyle: "straight" | "curved" | "zigzag";
  };
}

/**
 * 场景环境配置
 */
export interface SceneEnvironment {
  weather: WeatherType;               // 天气
  timeOfDay: TimeOfDay;               // 时间段
  backgroundMusic: string;            // 背景音乐
  ambientSounds: string[];            // 环境音效
  lightingColor: {                    // 光照颜色
    r: number;
    g: number;
    b: number;
    a: number;
  };
  particleEffects?: Array<{           // 粒子效果
    type: string;
    position: { x: number; y: number };
    config: Record<string, any>;
  }>;
  weatherEffects?: {                  // 天气效果
    intensity: number;
    duration?: number;
    cyclic?: boolean;
  };
}

/**
 * 场景奖励配置
 */
export interface SceneRewards {
  baseExp: number;                    // 基础经验值
  baseGold: number;                   // 基础金币
  dropTables: string[];               // 掉落表ID
  bonusMultipliers?: {                // 奖励倍数
    exp: number;
    gold: number;
    dropRate: number;
  };
  specialRewards?: Array<{            // 特殊奖励
    type: "item" | "currency" | "buff";
    id: string;
    quantity: number;
    probability: number;
  }>;
}

/**
 * 场景UI布局配置
 */
export interface SceneUILayout {
  behaviorButtonsPosition: {          // 行为按钮位置
    x: number;
    y: number;
  };
  infoDisplayPosition: {              // 信息显示位置
    x: number;
    y: number;
  };
  customElements: Array<{             // 自定义元素
    type: string;
    position: { x: number; y: number };
    config: Record<string, any>;
  }>;
  hudElements?: Array<{               // HUD元素
    type: "health" | "energy" | "progress" | "timer";
    position: { x: number; y: number };
    visible: boolean;
  }>;
}

/**
 * 完整的场景配置
 */
export interface SceneConfig {
  basicInfo: SceneBasicInfo;          // 基础信息
  unlockConditions: SceneUnlockConditions; // 解锁条件
  mapConfig: SceneMapConfig;          // 地图配置
  environment: SceneEnvironment;      // 环境配置
  rewards: SceneRewards;              // 奖励配置
  uiLayout: SceneUILayout;            // UI布局
  behaviors: Record<string, SceneBehaviorConfig>; // 行为配置
  metadata?: {                        // 元数据
    version: string;
    lastUpdated: number;
    author?: string;
    tags?: string[];
  };
}

// ==================== 场景行为类型 ====================

/**
 * 场景行为配置
 */
export interface SceneBehaviorConfig {
  behaviorId: string;                 // 行为ID
  name: string;                       // 行为名称
  description: string;                // 行为描述
  type: string;                       // 行为类型
  duration: number;                   // 持续时间（秒）
  cooldown: number;                   // 冷却时间（秒）
  energyCost: number;                 // 消耗体力
  requirements: {                     // 需求条件
    items: Array<{ itemId: string; quantity: number }>;
    skills: Array<{ skillId: string; level: number }>;
    level: number;
    buffs?: string[];
  };
  rewards: {                          // 奖励配置
    baseExp: number;
    items: Array<{
      id: string;
      probability: number;
      minQuantity: number;
      maxQuantity: number;
    }>;
    currency: {
      gold?: { min: number; max: number };
      gems?: { min: number; max: number };
    };
  };
  animations?: {                      // 动画配置
    startAnimation?: string;
    loopAnimation?: string;
    endAnimation?: string;
  };
  effects?: {                         // 效果配置
    visualEffects?: string[];
    soundEffects?: string[];
    screenEffects?: string[];
  };
}

// ==================== 场景状态类型 ====================

/**
 * 场景运行时状态
 */
export interface SceneRuntimeState {
  sceneId: SceneId;                   // 场景ID
  playerCount: number;                // 当前玩家数量
  maxPlayers: number;                 // 最大玩家数量
  serverLoad: number;                 // 服务器负载 (0-1)
  lastUpdate: number;                 // 最后更新时间
  activeEvents: Array<{               // 活跃事件
    eventId: string;
    startTime: number;
    endTime: number;
    effects: Record<string, any>;
  }>;
  weatherState?: {                    // 天气状态
    current: WeatherType;
    nextChange: number;
    forecast: Array<{
      weather: WeatherType;
      startTime: number;
      duration: number;
    }>;
  };
}

/**
 * 玩家场景状态
 */
export interface PlayerSceneState {
  playerId: string;                   // 玩家ID
  currentSceneId: SceneId;            // 当前场景ID
  position?: {                        // 位置信息
    x: number;
    y: number;
    nodeId?: string;
  };
  currentBehavior?: {                 // 当前行为
    behaviorId: string;
    startTime: number;
    endTime: number;
    progress: number;
  };
  sceneProgress: Record<SceneId, {    // 场景进度
    visitCount: number;
    totalTime: number;
    completedBehaviors: string[];
    unlockedFeatures: string[];
    bestRecords: Record<string, any>;
  }>;
  buffs: Array<{                      // 场景相关Buff
    buffId: string;
    source: "scene" | "weather" | "event";
    startTime: number;
    duration: number;
    effects: Record<string, any>;
  }>;
}

// ==================== 场景事件类型 ====================

/**
 * 场景事件类型枚举
 */
export enum SceneEventType {
  PLAYER_ENTER = "player_enter",      // 玩家进入
  PLAYER_LEAVE = "player_leave",      // 玩家离开
  BEHAVIOR_START = "behavior_start",  // 行为开始
  BEHAVIOR_END = "behavior_end",      // 行为结束
  WEATHER_CHANGE = "weather_change",  // 天气变化
  EVENT_START = "event_start",        // 事件开始
  EVENT_END = "event_end",            // 事件结束
  UNLOCK = "unlock",                  // 解锁
  LEVEL_UP = "level_up"               // 升级
}

/**
 * 场景事件数据
 */
export interface SceneEventData {
  eventType: SceneEventType;          // 事件类型
  sceneId: SceneId;                   // 场景ID
  playerId?: string;                  // 玩家ID（如果相关）
  timestamp: number;                  // 事件时间戳
  data: Record<string, any>;          // 事件数据
  metadata?: {                        // 元数据
    source: string;
    version: string;
    correlationId?: string;
  };
}

// ==================== 场景查询类型 ====================

/**
 * 场景查询条件
 */
export interface SceneQueryConditions {
  sceneIds?: SceneId[];               // 场景ID列表
  types?: SceneType[];                // 场景类型列表
  status?: SceneStatus[];             // 场景状态列表
  minLevel?: number;                  // 最小等级
  maxLevel?: number;                  // 最大等级
  unlocked?: boolean;                 // 是否已解锁
  available?: boolean;                // 是否可用
  tags?: string[];                    // 标签
  search?: string;                    // 搜索关键词
}

/**
 * 场景查询请求
 */
export interface SceneQueryRequest {
  conditions?: SceneQueryConditions;  // 查询条件
  config?: QueryConfig;               // 查询配置
  includeConfig?: boolean;            // 是否包含配置
  includeState?: boolean;             // 是否包含状态
  includeProgress?: boolean;          // 是否包含进度
}

/**
 * 场景查询响应
 */
export interface SceneQueryResponse {
  scenes: Array<{
    config: SceneConfig;
    state?: SceneRuntimeState;
    playerState?: PlayerSceneState;
  }>;
  total: number;                      // 总数
  filtered: number;                   // 过滤后数量
  metadata: {
    queryTime: number;
    cacheHit: boolean;
    version: string;
  };
}

// ==================== 地图功能专用类型 ====================

/**
 * 地图按钮数据
 */
export interface MapButtonData {
  locationId: SceneId;                // 地点ID
  buttonName: string;                 // 按钮名称
  displayName: string;                // 显示名称
  isUnlocked: boolean;                // 是否已解锁
  unlockProgress: number;             // 解锁进度 (0-1)
  buttonType: "enter" | "info" | "unlock"; // 按钮类型
  position?: {                        // 按钮位置
    x: number;
    y: number;
  };
  iconPath?: string;                  // 图标路径
  description?: string;               // 描述信息
}

/**
 * 地图切换请求数据
 */
export interface MapSwitchRequest {
  fromLocationId?: SceneId;           // 当前地点ID
  toLocationId: SceneId;              // 目标地点ID
  switchType: "normal" | "fast" | "instant"; // 切换类型
  validateUnlock: boolean;            // 是否验证解锁条件
  playerData?: {                      // 玩家数据
    playerId: string;
    level: number;
    currentLocation?: SceneId;
  };
  uiContext?: {                       // UI上下文
    componentId: string;
    buttonName: string;
    clickPosition?: { x: number; y: number };
  };
}

/**
 * 地图切换响应数据
 */
export interface MapSwitchResponse {
  success: boolean;                   // 是否成功
  locationId: SceneId;                // 地点ID
  switchId: string;                   // 切换操作ID
  sceneConfig?: SceneConfig;          // 场景配置
  unlockStatus?: {                    // 解锁状态
    isUnlocked: boolean;
    unlockProgress: number;
    unmetConditions: string[];
  };
  playerState?: PlayerSceneState;     // 玩家状态
  timing: {                           // 时间统计
    requestTime: number;
    processTime: number;
    totalTime: number;
  };
  errorInfo?: {                       // 错误信息
    code: string;
    message: string;
    details?: any;
  };
}
