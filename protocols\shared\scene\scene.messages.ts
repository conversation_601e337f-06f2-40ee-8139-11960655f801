/**
 * Copyright (c) 2025 "放置". All rights reserved.
 * 场景协议消息定义 - 地图功能相关消息协议
 */

import { BaseProtocolMessage, BaseProtocolResponse, ProtocolLayer, ProtocolMessageType } from '../core/base.types';
import { SceneId, MapButtonData, MapSwitchRequest, MapSwitchResponse, SceneConfig, PlayerSceneState } from './scene.types';

// ==================== 地图协议消息类型 ====================

/**
 * 地图协议消息类型枚举
 */
export enum MapProtocolType {
  // UI层消息
  MAP_BUTTON_CLICKED = "map.button.clicked",
  MAP_BUTTON_INFO_REQUESTED = "map.button.info.requested",
  MAP_AREA_UNLOCK_REQUESTED = "map.area.unlock.requested",
  
  // 业务层消息
  MAP_SWITCH_REQUESTED = "map.switch.requested",
  MAP_CONFIG_REQUESTED = "map.config.requested",
  MAP_UNLOCK_STATUS_REQUESTED = "map.unlock.status.requested",
  
  // 数据层消息
  MAP_DATA_LOAD_REQUESTED = "map.data.load.requested",
  MAP_CACHE_REFRESH_REQUESTED = "map.cache.refresh.requested",
  MAP_CONFIG_SYNC_REQUESTED = "map.config.sync.requested",
  
  // 网络层消息
  MAP_SERVER_SYNC_REQUESTED = "map.server.sync.requested",
  MAP_PLAYER_STATE_SYNC_REQUESTED = "map.player.state.sync.requested",
  
  // 响应消息
  MAP_SWITCH_RESPONSE = "map.switch.response",
  MAP_CONFIG_RESPONSE = "map.config.response",
  MAP_UNLOCK_STATUS_RESPONSE = "map.unlock.status.response",
  MAP_DATA_LOAD_RESPONSE = "map.data.load.response",
  
  // 事件消息
  MAP_SWITCHED_EVENT = "map.switched.event",
  MAP_UNLOCKED_EVENT = "map.unlocked.event",
  MAP_CONFIG_UPDATED_EVENT = "map.config.updated.event"
}

// ==================== UI层协议消息 ====================

/**
 * 地图按钮点击消息
 */
export interface MapButtonClickedMessage extends BaseProtocolMessage<MapButtonClickedPayload> {
  type: MapProtocolType.MAP_BUTTON_CLICKED;
  layer: ProtocolLayer.UI;
  messageType: ProtocolMessageType.REQUEST;
}

/**
 * 地图按钮点击载荷
 */
export interface MapButtonClickedPayload {
  buttonData: MapButtonData;          // 按钮数据
  playerData: {                       // 玩家数据
    playerId: string;
    level: number;
    currentLocationId?: SceneId;
  };
  uiContext: {                        // UI上下文
    componentId: string;
    buttonNode: string;
    clickPosition: { x: number; y: number };
    timestamp: number;
  };
}

/**
 * 地图按钮信息请求消息
 */
export interface MapButtonInfoRequestedMessage extends BaseProtocolMessage<MapButtonInfoRequestedPayload> {
  type: MapProtocolType.MAP_BUTTON_INFO_REQUESTED;
  layer: ProtocolLayer.UI;
  messageType: ProtocolMessageType.REQUEST;
}

/**
 * 地图按钮信息请求载荷
 */
export interface MapButtonInfoRequestedPayload {
  locationId: SceneId;                // 地点ID
  requestType: "basic" | "detailed" | "unlock_conditions"; // 请求类型
  playerData: {
    playerId: string;
    level: number;
    unlockedAreas: SceneId[];
  };
}

// ==================== 业务层协议消息 ====================

/**
 * 地图切换请求消息
 */
export interface MapSwitchRequestedMessage extends BaseProtocolMessage<MapSwitchRequest> {
  type: MapProtocolType.MAP_SWITCH_REQUESTED;
  layer: ProtocolLayer.BUSINESS;
  messageType: ProtocolMessageType.REQUEST;
}

/**
 * 地图配置请求消息
 */
export interface MapConfigRequestedMessage extends BaseProtocolMessage<MapConfigRequestedPayload> {
  type: MapProtocolType.MAP_CONFIG_REQUESTED;
  layer: ProtocolLayer.BUSINESS;
  messageType: ProtocolMessageType.REQUEST;
}

/**
 * 地图配置请求载荷
 */
export interface MapConfigRequestedPayload {
  locationId: SceneId;                // 地点ID
  version?: string;                   // 配置版本
  includePlayerState: boolean;        // 是否包含玩家状态
  includeUnlockStatus: boolean;       // 是否包含解锁状态
  cachePolicy?: "use_cache" | "force_refresh" | "cache_first";
}

/**
 * 地图解锁状态请求消息
 */
export interface MapUnlockStatusRequestedMessage extends BaseProtocolMessage<MapUnlockStatusRequestedPayload> {
  type: MapProtocolType.MAP_UNLOCK_STATUS_REQUESTED;
  layer: ProtocolLayer.BUSINESS;
  messageType: ProtocolMessageType.REQUEST;
}

/**
 * 地图解锁状态请求载荷
 */
export interface MapUnlockStatusRequestedPayload {
  locationIds: SceneId[];             // 地点ID列表
  playerId: string;                   // 玩家ID
  includeProgress: boolean;           // 是否包含进度信息
  includeConditions: boolean;         // 是否包含条件信息
}

// ==================== 数据层协议消息 ====================

/**
 * 地图数据加载请求消息
 */
export interface MapDataLoadRequestedMessage extends BaseProtocolMessage<MapDataLoadRequestedPayload> {
  type: MapProtocolType.MAP_DATA_LOAD_REQUESTED;
  layer: ProtocolLayer.DATA;
  messageType: ProtocolMessageType.REQUEST;
}

/**
 * 地图数据加载请求载荷
 */
export interface MapDataLoadRequestedPayload {
  locationIds: SceneId[];             // 地点ID列表
  dataTypes: Array<"config" | "state" | "player_progress">; // 数据类型
  source: "cache" | "server" | "local" | "auto"; // 数据源
  version?: string;                   // 数据版本
}

// ==================== 网络层协议消息 ====================

/**
 * 地图服务器同步请求消息
 */
export interface MapServerSyncRequestedMessage extends BaseProtocolMessage<MapServerSyncRequestedPayload> {
  type: MapProtocolType.MAP_SERVER_SYNC_REQUESTED;
  layer: ProtocolLayer.NETWORK;
  messageType: ProtocolMessageType.REQUEST;
}

/**
 * 地图服务器同步请求载荷
 */
export interface MapServerSyncRequestedPayload {
  syncType: "pull" | "push" | "bidirectional"; // 同步类型
  locationIds?: SceneId[];            // 指定地点ID（可选）
  playerData: {                       // 玩家数据
    playerId: string;
    currentLocationId?: SceneId;
    lastSyncTime?: number;
  };
  options: {
    timeout: number;                  // 超时时间
    retryCount: number;               // 重试次数
    priority: "high" | "normal" | "low";
  };
}

// ==================== 响应消息 ====================

/**
 * 地图切换响应消息
 */
export interface MapSwitchResponseMessage extends BaseProtocolResponse<MapSwitchResponse> {
  type: MapProtocolType.MAP_SWITCH_RESPONSE;
}

/**
 * 地图配置响应消息
 */
export interface MapConfigResponseMessage extends BaseProtocolResponse<MapConfigResponseData> {
  type: MapProtocolType.MAP_CONFIG_RESPONSE;
}

/**
 * 地图配置响应数据
 */
export interface MapConfigResponseData {
  locationId: SceneId;                // 地点ID
  config: SceneConfig;                // 场景配置
  playerState?: PlayerSceneState;     // 玩家状态
  unlockStatus?: {                    // 解锁状态
    isUnlocked: boolean;
    unlockProgress: number;
    unmetConditions: string[];
    nextUnlockHint?: string;
  };
  metadata: {                         // 元数据
    version: string;
    lastUpdated: number;
    dataSource: "cache" | "server" | "local";
    loadTime: number;
  };
}

/**
 * 地图解锁状态响应消息
 */
export interface MapUnlockStatusResponseMessage extends BaseProtocolResponse<MapUnlockStatusResponseData> {
  type: MapProtocolType.MAP_UNLOCK_STATUS_RESPONSE;
}

/**
 * 地图解锁状态响应数据
 */
export interface MapUnlockStatusResponseData {
  locationStatuses: Array<{
    locationId: SceneId;
    isUnlocked: boolean;
    unlockProgress: number;
    unmetConditions: string[];
    estimatedUnlockTime?: number;
    unlockHints: string[];
  }>;
  playerLevel: number;                // 玩家等级
  totalUnlockedAreas: number;         // 已解锁区域总数
  nextRecommendedArea?: SceneId;      // 推荐的下一个区域
}

// ==================== 事件消息 ====================

/**
 * 地图切换事件消息
 */
export interface MapSwitchedEventMessage extends BaseProtocolMessage<MapSwitchedEventPayload> {
  type: MapProtocolType.MAP_SWITCHED_EVENT;
  messageType: ProtocolMessageType.EVENT;
}

/**
 * 地图切换事件载荷
 */
export interface MapSwitchedEventPayload {
  fromLocationId?: SceneId;           // 原地点ID
  toLocationId: SceneId;              // 新地点ID
  switchId: string;                   // 切换操作ID
  playerId: string;                   // 玩家ID
  switchTime: number;                 // 切换时间
  switchDuration: number;             // 切换耗时
  success: boolean;                   // 是否成功
}

/**
 * 地图解锁事件消息
 */
export interface MapUnlockedEventMessage extends BaseProtocolMessage<MapUnlockedEventPayload> {
  type: MapProtocolType.MAP_UNLOCKED_EVENT;
  messageType: ProtocolMessageType.EVENT;
}

/**
 * 地图解锁事件载荷
 */
export interface MapUnlockedEventPayload {
  locationId: SceneId;                // 解锁的地点ID
  playerId: string;                   // 玩家ID
  unlockTime: number;                 // 解锁时间
  unlockMethod: "level" | "quest" | "item" | "manual"; // 解锁方式
  previousProgress: number;           // 之前的进度
  unlockRewards?: Array<{             // 解锁奖励
    type: "exp" | "gold" | "item";
    id: string;
    quantity: number;
  }>;
}

// ==================== 协议工具函数 ====================

/**
 * 地图协议工具类
 */
export class MapProtocolUtils {
  /**
   * 创建地图按钮点击消息
   */
  static createMapButtonClickedMessage(
    buttonData: MapButtonData,
    playerData: any,
    uiContext: any
  ): MapButtonClickedMessage {
    return {
      id: `map_btn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: MapProtocolType.MAP_BUTTON_CLICKED,
      messageType: ProtocolMessageType.REQUEST,
      layer: ProtocolLayer.UI,
      version: "1.0",
      payload: {
        buttonData,
        playerData,
        uiContext
      },
      timestamp: Date.now()
    };
  }

  /**
   * 创建地图切换请求消息
   */
  static createMapSwitchRequestedMessage(
    switchRequest: MapSwitchRequest
  ): MapSwitchRequestedMessage {
    return {
      id: `map_switch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: MapProtocolType.MAP_SWITCH_REQUESTED,
      messageType: ProtocolMessageType.REQUEST,
      layer: ProtocolLayer.BUSINESS,
      version: "1.0",
      payload: switchRequest,
      timestamp: Date.now()
    };
  }

  /**
   * 创建地图配置请求消息
   */
  static createMapConfigRequestedMessage(
    configRequest: MapConfigRequestedPayload
  ): MapConfigRequestedMessage {
    return {
      id: `map_config_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: MapProtocolType.MAP_CONFIG_REQUESTED,
      messageType: ProtocolMessageType.REQUEST,
      layer: ProtocolLayer.BUSINESS,
      version: "1.0",
      payload: configRequest,
      timestamp: Date.now()
    };
  }
}
