// Copyright (c) 2025 "放置". All rights reserved.
import { _decorator, Component, Node, input, Input, EventKeyboard, KeyCode, find, Button } from 'cc';
import { LocationConfigManager } from '../managers/LocationConfigManager';

const { ccclass, property } = _decorator;

/**
 * 地点切换测试组件
 * 用于测试MapPanel按钮点击和LinearBranchMapContainer地点切换功能
 */
@ccclass('LocationSwitchingTest')
export class LocationSwitchingTest extends Component {

    /**
     * 地点ID映射表
     */
    private readonly LOCATION_MAP: { [buttonName: string]: string } = {
        'map': 'Forest',
        'map-001': 'Desert', 
        'map-002': 'Mountain',
        'map-003': 'Cave',
        'map-004': 'Ocean'
    };

    protected onLoad(): void {
        console.log('🧪 LocationSwitchingTest: 地点切换测试组件加载');
        this.setupKeyboardEvents();
        this.testMapPanelButtons();
    }

    protected onDestroy(): void {
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
    }

    /**
     * 设置键盘事件
     */
    private setupKeyboardEvents(): void {
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log('⌨️ 键盘事件已设置');
    }

    /**
     * 键盘按下事件处理
     */
    private onKeyDown(event: EventKeyboard): void {
        switch (event.keyCode) {
            case KeyCode.DIGIT_1:
                this.testLocationSwitch('Forest');
                break;
            case KeyCode.DIGIT_2:
                this.testLocationSwitch('Desert');
                break;
            case KeyCode.DIGIT_3:
                this.testLocationSwitch('Mountain');
                break;
            case KeyCode.KEY_T:
                this.runAllTests();
                break;
            case KeyCode.KEY_H:
                this.showHelp();
                break;
        }
    }

    /**
     * 测试MapPanel按钮
     */
    private testMapPanelButtons(): void {
        console.log('🧪 ========== MapPanel按钮测试 ==========');
        
        // 查找MapPanel
        const mapPanel = find('Canvas/MainUI/MapPanel');
        if (!mapPanel) {
            console.error('❌ 未找到MapPanel节点');
            return;
        }
        console.log('✅ 找到MapPanel节点');

        // 查找Maplist
        const mapList = find('Canvas/MainUI/MapPanel/Maplist/list');
        if (!mapList) {
            console.error('❌ 未找到Maplist节点');
            return;
        }
        console.log('✅ 找到Maplist节点');

        // 检查按钮
        console.log(`📋 Maplist包含 ${mapList.children.length} 个子节点:`);
        for (let i = 0; i < mapList.children.length; i++) {
            const buttonNode = mapList.children[i];
            const button = buttonNode.getComponent(Button);
            const locationId = this.LOCATION_MAP[buttonNode.name];
            
            console.log(`  ${i + 1}. ${buttonNode.name} - Button组件: ${button ? '✅' : '❌'} - 地点: ${locationId || '未配置'}`);
            
            // 如果有Button组件，绑定测试事件
            if (button) {
                button.node.off(Button.EventType.CLICK); // 先解绑避免重复
                button.node.on(Button.EventType.CLICK, () => {
                    this.onMapButtonClickInternal(buttonNode.name);
                }, this);
            }
        }
        
        console.log('🧪 =====================================');
    }

    /**
     * 地图按钮点击处理（Button组件回调）
     */
    public async onMapButtonClick(event: any, customEventData: string): Promise<void> {
        const buttonName = customEventData || 'map';
        console.log(`🗺️ 测试: 地图按钮被点击 - ${buttonName}`);

        const locationId = this.LOCATION_MAP[buttonName];
        if (!locationId) {
            console.warn(`⚠️ 按钮 ${buttonName} 没有配置对应的地点ID`);
            return;
        }

        await this.testLocationSwitch(locationId);
    }

    /**
     * 内部地图按钮点击处理
     */
    private async onMapButtonClickInternal(buttonName: string): Promise<void> {
        console.log(`🗺️ 测试: 地图按钮被点击 - ${buttonName}`);

        const locationId = this.LOCATION_MAP[buttonName];
        if (!locationId) {
            console.warn(`⚠️ 按钮 ${buttonName} 没有配置对应的地点ID`);
            return;
        }

        await this.testLocationSwitch(locationId);
    }

    /**
     * 测试地点切换
     */
    private async testLocationSwitch(locationId: string): Promise<void> {
        console.log(`🧪 ========== 测试地点切换: ${locationId} ==========`);

        try {
            // 1. 测试LocationConfigManager
            console.log('📋 步骤1: 测试LocationConfigManager');
            const locationManager = LocationConfigManager.getInstance();
            if (!locationManager) {
                console.error('❌ LocationConfigManager实例获取失败');
                return;
            }
            console.log('✅ LocationConfigManager实例获取成功');

            // 2. 获取地点配置
            console.log(`📋 步骤2: 获取地点配置 - ${locationId}`);
            const config = await locationManager.getLocationConfig(locationId);
            if (!config) {
                console.error(`❌ 无法获取地点配置: ${locationId}`);
                return;
            }
            console.log(`✅ 地点配置获取成功: ${config.name}`);
            console.log('📄 配置详情:', {
                locationId: config.locationId,
                name: config.name,
                description: config.description,
                mapConfig: config.mapConfig
            });

            // 3. 查找LinearBranchMapContainer
            console.log('📋 步骤3: 查找LinearBranchMapContainer');
            const mapContainer = find('Canvas/MainUI/BackPanel/LinearBranchMapContainer');
            if (!mapContainer) {
                console.error('❌ 未找到LinearBranchMapContainer');
                return;
            }
            console.log('✅ 找到LinearBranchMapContainer');

            // 4. 查找LinearBranchMapPanel组件
            console.log('📋 步骤4: 查找LinearBranchMapPanel组件');
            const mapPanel = mapContainer.getComponentInChildren('LinearBranchMapPanel');
            if (!mapPanel) {
                console.warn('⚠️ 未找到LinearBranchMapPanel组件');
                // 尝试查找其他可能的组件
                const components = mapContainer.getComponentsInChildren(Component);
                console.log('🔍 容器中的组件:', components.map(c => c.constructor.name));
            } else {
                console.log('✅ 找到LinearBranchMapPanel组件');
                
                // 5. 应用配置
                console.log('📋 步骤5: 应用地点配置');
                await this.applyConfigToMapPanel(mapPanel, config);
            }

            console.log(`✅ 地点切换测试完成: ${locationId}`);
        } catch (error) {
            console.error(`❌ 地点切换测试失败: ${locationId}`, error);
        }
        
        console.log('🧪 =======================================');
    }

    /**
     * 应用配置到地图面板
     */
    private async applyConfigToMapPanel(mapPanel: any, config: any): Promise<void> {
        try {
            console.log(`🎨 应用地点配置: ${config.locationId}`);

            // 检查mapPanel的属性
            console.log('🔍 检查mapPanel属性:');
            console.log('  backgroundImagePath:', mapPanel.backgroundImagePath);
            console.log('  nodeSpacingX:', mapPanel.nodeSpacingX);
            console.log('  nodeSpacingY:', mapPanel.nodeSpacingY);
            console.log('  branchCount:', mapPanel.branchCount);
            console.log('  nodesPerBranch:', mapPanel.nodesPerBranch);

            // 应用地图配置
            if (config.mapConfig) {
                console.log('📝 应用地图配置:');
                
                if (mapPanel.backgroundImagePath !== undefined) {
                    mapPanel.backgroundImagePath = config.mapConfig.backgroundImagePath;
                    console.log(`  ✅ backgroundImagePath: ${config.mapConfig.backgroundImagePath}`);
                }
                
                if (mapPanel.nodeSpacingX !== undefined) {
                    mapPanel.nodeSpacingX = config.mapConfig.nodeSpacing.x;
                    console.log(`  ✅ nodeSpacingX: ${config.mapConfig.nodeSpacing.x}`);
                }
                
                if (mapPanel.nodeSpacingY !== undefined) {
                    mapPanel.nodeSpacingY = config.mapConfig.nodeSpacing.y;
                    console.log(`  ✅ nodeSpacingY: ${config.mapConfig.nodeSpacing.y}`);
                }
                
                if (mapPanel.branchCount !== undefined) {
                    mapPanel.branchCount = config.mapConfig.branchCount;
                    console.log(`  ✅ branchCount: ${config.mapConfig.branchCount}`);
                }
                
                if (mapPanel.nodesPerBranch !== undefined) {
                    mapPanel.nodesPerBranch = config.mapConfig.nodesPerBranch;
                    console.log(`  ✅ nodesPerBranch: ${config.mapConfig.nodesPerBranch}`);
                }
            }

            // 重新生成地图
            console.log('🔄 重新生成地图');
            if (mapPanel.regenerateMap) {
                mapPanel.regenerateMap();
                console.log('  ✅ 调用regenerateMap()');
            } else if (mapPanel.initializeMapData) {
                mapPanel.initializeMapData();
                console.log('  ✅ 调用initializeMapData()');
            } else {
                console.log('  ⚠️ 未找到地图重生成方法');
            }

            console.log(`✅ 地点配置应用完成: ${config.locationId}`);
        } catch (error) {
            console.error(`❌ 应用地点配置失败:`, error);
            throw error;
        }
    }

    /**
     * 运行所有测试
     */
    private async runAllTests(): Promise<void> {
        console.log('🧪 ========== 运行所有地点切换测试 ==========');
        
        const testLocations = ['Forest', 'Desert', 'Mountain'];
        
        for (const locationId of testLocations) {
            await this.testLocationSwitch(locationId);
            // 等待一秒再测试下一个
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        console.log('🧪 ========== 所有测试完成 ==========');
    }

    /**
     * 显示帮助信息
     */
    private showHelp(): void {
        console.log('🧪 ========== 地点切换测试帮助 ==========');
        console.log('⌨️ 键盘快捷键:');
        console.log('   按 1 键 - 测试切换到森林地点');
        console.log('   按 2 键 - 测试切换到沙漠地点');
        console.log('   按 3 键 - 测试切换到山脉地点');
        console.log('   按 T 键 - 运行所有地点切换测试');
        console.log('   按 H 键 - 显示帮助信息');
        console.log('🖱️ 鼠标操作:');
        console.log('   点击MapPanel中的地图按钮进行测试');
        console.log('🧪 =======================================');
    }
}
