# 智能意图检测系统使用说明

## 概述

智能意图检测系统是对 `DraggableMapContainer` 组件的增强功能，能够智能区分用户的点击和拖拽意图，解决拖拽缩放与按钮点击冲突的问题。

## 核心特性

### 1. 智能意图识别
- **时间检测**: 基于触摸持续时间判断用户意图
- **距离检测**: 基于移动距离判断是点击还是拖拽
- **快速拖拽检测**: 检测快速移动手势，立即确认为拖拽意图

### 2. 自动按钮触发
- 当检测到点击意图时，自动查找触摸位置的按钮并触发点击事件
- 支持多层级UI结构的按钮检测
- 兼容现有的按钮事件系统

### 3. 可配置参数
- `intentDetectionDelay`: 意图检测延迟时间（默认120ms）
- `clickDistanceThreshold`: 点击距离阈值（默认8px）
- `clickTimeThreshold`: 点击时间阈值（默认250ms）
- `dragStartThreshold`: 拖拽开始距离阈值（默认15px）

## 使用方法

### 1. 基础配置

在 Cocos Creator 编辑器中，选择带有 `DraggableMapContainer` 组件的节点，在属性面板中可以看到新增的意图检测配置项：

```typescript
// 启用智能意图检测
enableIntentDetection: true

// 意图检测延迟时间（毫秒）
intentDetectionDelay: 120

// 点击距离阈值（像素）
clickDistanceThreshold: 8

// 点击时间阈值（毫秒）
clickTimeThreshold: 250
```

### 2. 代码配置

```typescript
// 获取拖拽容器组件
const draggableContainer = this.node.getComponent(DraggableMapContainer);

// 配置意图检测参数
draggableContainer.setIntentDetectionConfig({
    enableIntentDetection: true,
    intentDetectionDelay: 100,
    clickDistanceThreshold: 10,
    clickTimeThreshold: 300,
    dragStartThreshold: 15,
    enableFastDragDetection: true,
    fastDragVelocityThreshold: 0.5
});

// 启用或禁用意图检测
draggableContainer.setIntentDetectionEnabled(true);
```

### 3. 状态查询

```typescript
// 检查当前用户意图
const currentIntent = draggableContainer.getCurrentIntent();
console.log('当前意图:', UserIntentType[currentIntent]);

// 检查是否正在进行意图检测
const isDetecting = draggableContainer.isDetectingIntent();
console.log('正在检测意图:', isDetecting);

// 获取当前配置
const config = draggableContainer.getIntentDetectionConfig();
console.log('当前配置:', config);
```

## 工作原理

### 1. 触摸开始阶段
- 用户触摸屏幕时，系统开始意图检测
- 记录触摸开始时间和位置
- 启动延迟检测定时器

### 2. 触摸移动阶段
- 实时计算移动距离
- 如果移动距离超过拖拽阈值，立即确认为拖拽意图
- 如果启用快速拖拽检测，根据移动速度判断

### 3. 意图确认阶段
- **点击意图**: 移动距离小且时间短
  - 查找触摸位置的按钮
  - 自动触发按钮点击事件
  - 不启动拖拽功能
  
- **拖拽意图**: 移动距离大或时间长
  - 启动正常的拖拽功能
  - 屏蔽按钮输入防止误触

## 参数调优建议

### 1. 针对不同设备
```typescript
// 手机端（触摸精度较低）
{
    clickDistanceThreshold: 12,
    clickTimeThreshold: 300,
    dragStartThreshold: 20
}

// 平板端（触摸精度较高）
{
    clickDistanceThreshold: 6,
    clickTimeThreshold: 200,
    dragStartThreshold: 12
}
```

### 2. 针对不同用户群体
```typescript
// 年轻用户（操作较快）
{
    intentDetectionDelay: 80,
    clickTimeThreshold: 200,
    enableFastDragDetection: true
}

// 年长用户（操作较慢）
{
    intentDetectionDelay: 150,
    clickTimeThreshold: 400,
    enableFastDragDetection: false
}
```

## 测试和调试

### 1. 使用测试器组件
```typescript
// 添加测试器组件
const tester = this.node.addComponent(IntentDetectionTester);
tester.draggableContainer = this.draggableContainerNode;
```

### 2. 查看调试日志
系统会输出详细的调试信息，包括：
- 意图检测开始/结束
- 移动距离和时间统计
- 最终意图确认结果
- 按钮查找和触发结果

### 3. 性能监控
```typescript
// 获取测试统计
const stats = tester.getTestStatistics();
console.log('测试统计:', stats);
```

## 注意事项

1. **兼容性**: 系统与现有按钮事件完全兼容，无需修改现有代码
2. **性能**: 意图检测的计算量很小，不会影响游戏性能
3. **精确度**: 建议根据实际测试结果调整参数，以获得最佳用户体验
4. **调试**: 开发阶段建议启用详细日志，发布时可以关闭

## 常见问题

### Q: 按钮点击响应变慢了？
A: 这是正常现象，系统需要短暂时间判断用户意图。可以通过减少 `intentDetectionDelay` 来缩短延迟。

### Q: 有些按钮无法被检测到？
A: 检查按钮是否有 `Button` 组件且 `interactable` 为 true，确保按钮在UI层级中可见。

### Q: 拖拽变得不够灵敏？
A: 可以减少 `dragStartThreshold` 或启用 `enableFastDragDetection` 来提高拖拽灵敏度。

### Q: 如何完全禁用意图检测？
A: 设置 `enableIntentDetection` 为 false，系统将回到原始的拖拽行为。
