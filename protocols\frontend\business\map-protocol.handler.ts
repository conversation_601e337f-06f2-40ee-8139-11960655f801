/**
 * Copyright (c) 2025 "放置". All rights reserved.
 * 地图协议处理器 - 业务层地图协议处理
 */

// import { _decorator } from 'cc';
// 临时注释掉导入，避免路径问题

// 临时类型定义
interface ProtocolHandler {
  getName(): string;
  getSupportedTypes(): string[];
  handle(message: any): Promise<any>;
}

interface ProtocolMessage {
  id: string;
  type: string;
  payload: any;
}

interface ProtocolResponse {
  id: string;
  success: boolean;
  data?: any;
  error?: any;
}

class ProtocolUtils {
  static createSuccessResponse(id: string, data: any): ProtocolResponse {
    return { id, success: true, data };
  }

  static createErrorResponse(id: string, code: string, message: string, details?: any): ProtocolResponse {
    return {
      id,
      success: false,
      error: { code, message, details }
    };
  }
}

// 地图协议类型
enum MapProtocolType {
  MAP_SWITCH_REQUESTED = "map.switch.requested",
  MAP_CONFIG_REQUESTED = "map.config.requested",
  MAP_UNLOCK_STATUS_REQUESTED = "map.unlock.status.requested"
}

const { ccclass } = _decorator;

/**
 * 地图业务协议处理器
 */
@ccclass('MapBusinessProtocolHandler')
export class MapBusinessProtocolHandler implements ProtocolHandler {
  private locationManager: LocationConfigManager;

  constructor() {
    this.locationManager = LocationConfigManager.getInstance();
  }

  /**
   * 获取处理器名称
   */
  getName(): string {
    return "MapBusinessProtocolHandler";
  }

  /**
   * 获取支持的消息类型
   */
  getSupportedTypes(): string[] {
    return [
      MapProtocolType.MAP_SWITCH_REQUESTED,
      MapProtocolType.MAP_CONFIG_REQUESTED,
      MapProtocolType.MAP_UNLOCK_STATUS_REQUESTED
    ];
  }

  /**
   * 处理协议消息
   */
  async handle(message: ProtocolMessage): Promise<ProtocolResponse> {
    console.log(`🗺️ MapBusinessProtocolHandler: 处理消息 ${message.type}`);

    try {
      switch (message.type) {
        case MapProtocolType.MAP_SWITCH_REQUESTED:
          return await this.handleMapSwitchRequest(message as MapSwitchRequestedMessage);
        
        case MapProtocolType.MAP_CONFIG_REQUESTED:
          return await this.handleMapConfigRequest(message as MapConfigRequestedMessage);
        
        case MapProtocolType.MAP_UNLOCK_STATUS_REQUESTED:
          return await this.handleMapUnlockStatusRequest(message as MapUnlockStatusRequestedMessage);
        
        default:
          throw new Error(`不支持的消息类型: ${message.type}`);
      }
    } catch (error) {
      console.error(`❌ MapBusinessProtocolHandler: 处理失败`, error);
      return ProtocolUtils.createErrorResponse(
        message.id,
        "MAP_BUSINESS_ERROR",
        `地图业务处理失败: ${error.message}`,
        error
      );
    }
  }

  /**
   * 处理地图切换请求
   */
  private async handleMapSwitchRequest(message: MapSwitchRequestedMessage): Promise<ProtocolResponse> {
    const startTime = Date.now();
    const { payload } = message;

    console.log(`🔄 处理地图切换请求: ${payload.fromLocationId} → ${payload.toLocationId}`);

    try {
      // 1. 验证解锁条件（如果需要）
      if (payload.validateUnlock) {
        const unlockStatus = await this.locationManager.checkLocationUnlockStatus(
          payload.toLocationId,
          await this.locationManager.getLocationConfig(payload.toLocationId)
        );

        if (!unlockStatus.isUnlocked) {
          const response: MapSwitchResponse = {
            success: false,
            locationId: payload.toLocationId,
            switchId: `switch_${Date.now()}`,
            unlockStatus: {
              isUnlocked: false,
              unlockProgress: unlockStatus.unlockProgress,
              unmetConditions: unlockStatus.unmetConditions
            },
            timing: {
              requestTime: startTime,
              processTime: Date.now() - startTime,
              totalTime: Date.now() - startTime
            },
            errorInfo: {
              code: "LOCATION_LOCKED",
              message: `地点 ${payload.toLocationId} 尚未解锁`,
              details: unlockStatus.unmetConditions
            }
          };

          return ProtocolUtils.createSuccessResponse(message.id, response);
        }
      }

      // 2. 获取目标地点配置
      const sceneConfig = await this.locationManager.getLocationConfig(payload.toLocationId);
      if (!sceneConfig) {
        throw new Error(`无法获取地点配置: ${payload.toLocationId}`);
      }

      // 3. 执行地图切换逻辑
      const switchSuccess = await this.performMapSwitch(payload);

      // 4. 构建响应
      const response: MapSwitchResponse = {
        success: switchSuccess,
        locationId: payload.toLocationId,
        switchId: `switch_${Date.now()}`,
        sceneConfig,
        unlockStatus: {
          isUnlocked: true,
          unlockProgress: 1.0,
          unmetConditions: []
        },
        timing: {
          requestTime: startTime,
          processTime: Date.now() - startTime,
          totalTime: Date.now() - startTime
        }
      };

      console.log(`✅ 地图切换${switchSuccess ? '成功' : '失败'}: ${payload.toLocationId}`);
      return ProtocolUtils.createSuccessResponse(message.id, response);

    } catch (error) {
      console.error(`❌ 地图切换处理失败: ${payload.toLocationId}`, error);
      
      const response: MapSwitchResponse = {
        success: false,
        locationId: payload.toLocationId,
        switchId: `switch_${Date.now()}`,
        timing: {
          requestTime: startTime,
          processTime: Date.now() - startTime,
          totalTime: Date.now() - startTime
        },
        errorInfo: {
          code: "SWITCH_FAILED",
          message: error.message,
          details: error
        }
      };

      return ProtocolUtils.createSuccessResponse(message.id, response);
    }
  }

  /**
   * 处理地图配置请求
   */
  private async handleMapConfigRequest(message: MapConfigRequestedMessage): Promise<ProtocolResponse> {
    const { payload } = message;
    const startTime = Date.now();

    console.log(`📋 处理地图配置请求: ${payload.locationId}`);

    try {
      // 获取地点配置
      const config = await this.locationManager.getLocationConfig(
        payload.locationId, 
        payload.version
      );

      if (!config) {
        throw new Error(`地点配置不存在: ${payload.locationId}`);
      }

      // 获取解锁状态（如果需要）
      let unlockStatus;
      if (payload.includeUnlockStatus) {
        unlockStatus = await this.locationManager.checkLocationUnlockStatus(payload.locationId, config);
      }

      // 构建响应数据
      const responseData: MapConfigResponseData = {
        locationId: payload.locationId,
        config: config as any, // 类型转换，因为ILocationConfig和SceneConfig结构相似
        unlockStatus: unlockStatus ? {
          isUnlocked: unlockStatus.isUnlocked,
          unlockProgress: unlockStatus.unlockProgress,
          unmetConditions: unlockStatus.unmetConditions,
          nextUnlockHint: this.generateUnlockHint(unlockStatus.unmetConditions)
        } : undefined,
        metadata: {
          version: payload.version || "latest",
          lastUpdated: Date.now(),
          dataSource: "cache", // 简化处理，实际应该根据缓存情况确定
          loadTime: Date.now() - startTime
        }
      };

      console.log(`✅ 地图配置获取成功: ${payload.locationId}`);
      return ProtocolUtils.createSuccessResponse(message.id, responseData);

    } catch (error) {
      console.error(`❌ 地图配置获取失败: ${payload.locationId}`, error);
      throw error;
    }
  }

  /**
   * 处理地图解锁状态请求
   */
  private async handleMapUnlockStatusRequest(message: MapUnlockStatusRequestedMessage): Promise<ProtocolResponse> {
    const { payload } = message;

    console.log(`🔓 处理地图解锁状态请求: ${payload.locationIds.join(', ')}`);

    try {
      const locationStatuses = [];
      let totalUnlockedAreas = 0;

      // 检查每个地点的解锁状态
      for (const locationId of payload.locationIds) {
        const config = await this.locationManager.getLocationConfig(locationId);
        if (!config) {
          console.warn(`⚠️ 地点配置不存在: ${locationId}`);
          continue;
        }

        const unlockStatus = await this.locationManager.checkLocationUnlockStatus(locationId, config);
        
        if (unlockStatus.isUnlocked) {
          totalUnlockedAreas++;
        }

        locationStatuses.push({
          locationId,
          isUnlocked: unlockStatus.isUnlocked,
          unlockProgress: unlockStatus.unlockProgress,
          unmetConditions: unlockStatus.unmetConditions,
          unlockHints: this.generateUnlockHints(unlockStatus.unmetConditions)
        });
      }

      // 构建响应数据
      const responseData: MapUnlockStatusResponseData = {
        locationStatuses,
        playerLevel: payload.playerId ? await this.getPlayerLevel(payload.playerId) : 1,
        totalUnlockedAreas,
        nextRecommendedArea: this.getNextRecommendedArea(locationStatuses)
      };

      console.log(`✅ 地图解锁状态获取成功，已解锁 ${totalUnlockedAreas}/${payload.locationIds.length} 个区域`);
      return ProtocolUtils.createSuccessResponse(message.id, responseData);

    } catch (error) {
      console.error(`❌ 地图解锁状态获取失败`, error);
      throw error;
    }
  }

  /**
   * 执行地图切换
   */
  private async performMapSwitch(switchRequest: any): Promise<boolean> {
    try {
      // 查找LinearBranchMapContainer
      const mapContainer = cc.find('Canvas/MainUI/BehaviorPanel/LinearBranchMapContainer');
      if (!mapContainer) {
        console.error('❌ 未找到LinearBranchMapContainer');
        return false;
      }

      // 应用地点配置到容器
      const success = await this.locationManager.applyLocationConfigToContainer(
        switchRequest.toLocationId,
        mapContainer
      );

      return success;
    } catch (error) {
      console.error('❌ 执行地图切换失败', error);
      return false;
    }
  }

  /**
   * 生成解锁提示
   */
  private generateUnlockHint(unmetConditions: string[]): string {
    if (unmetConditions.length === 0) {
      return "已解锁";
    }

    const condition = unmetConditions[0];
    if (condition.includes("level")) {
      return "提升等级以解锁此区域";
    } else if (condition.includes("quest")) {
      return "完成前置任务以解锁此区域";
    } else if (condition.includes("item")) {
      return "获得必需物品以解锁此区域";
    }

    return "满足解锁条件以解锁此区域";
  }

  /**
   * 生成解锁提示列表
   */
  private generateUnlockHints(unmetConditions: string[]): string[] {
    return unmetConditions.map(condition => this.generateUnlockHint([condition]));
  }

  /**
   * 获取玩家等级
   */
  private async getPlayerLevel(playerId: string): Promise<number> {
    // 简化实现，实际应该从玩家数据管理器获取
    return 1;
  }

  /**
   * 获取推荐的下一个区域
   */
  private getNextRecommendedArea(locationStatuses: any[]): string | undefined {
    // 找到第一个未解锁但进度最高的区域
    const lockedAreas = locationStatuses.filter(status => !status.isUnlocked);
    if (lockedAreas.length === 0) {
      return undefined;
    }

    lockedAreas.sort((a, b) => b.unlockProgress - a.unlockProgress);
    return lockedAreas[0].locationId;
  }
}
