/**
 * 地图系统测试脚本
 * 用于验证线性分支地图系统的功能
 */

import { _decorator, Component, Node, find, input, Input, EventKeyboard, KeyCode } from 'cc';
import { MainUIController } from '../ui/MainUIController';
import { BehaviorPanel } from '../ui/panels/BehaviorPanel';

const { ccclass, property } = _decorator;

@ccclass('MapSystemTest')
export class MapSystemTest extends Component {
    
    @property({ tooltip: '是否启用键盘测试' })
    public enableKeyboardTest: boolean = true;
    
    @property({ tooltip: '是否自动运行测试' })
    public autoRunTest: boolean = true;
    
    // 私有属性
    private _mainUIController: MainUIController | null = null;
    private _behaviorPanel: BehaviorPanel | null = null;

    protected onLoad(): void {
        console.log('🧪 MapSystemTest: 地图系统测试脚本加载');
        this.initializeComponents();
        
        if (this.enableKeyboardTest) {
            this.setupKeyboardTest();
        }
    }

    protected start(): void {
        if (this.autoRunTest) {
            this.scheduleOnce(() => {
                this.runBasicTest();
            }, 2.0); // 延迟2秒运行测试
        }
        
        this.showTestInstructions();
    }

    protected onDestroy(): void {
        if (this.enableKeyboardTest) {
            input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        }
    }

    /**
     * 初始化组件
     */
    private initializeComponents(): void {
        // 查找主UI控制器
        this._mainUIController = find('Canvas/MainUI')?.getComponent(MainUIController);
        
        // 查找行为面板
        this._behaviorPanel = find('Canvas/MainUI/BehaviorPanel')?.getComponent(BehaviorPanel);
        
        console.log('🧪 MapSystemTest: 组件初始化完成', {
            mainUIController: !!this._mainUIController,
            behaviorPanel: !!this._behaviorPanel
        });
    }

    /**
     * 设置键盘测试
     */
    private setupKeyboardTest(): void {
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log('⌨️ MapSystemTest: 键盘测试已启用');
    }

    /**
     * 显示测试说明
     */
    private showTestInstructions(): void {
        console.log('🧪 ========== 地图系统测试说明 ==========');
        console.log('🎮 这是线性分支地图系统的测试脚本');
        console.log('🗺️ 测试功能:');
        console.log('   • 地图拖拽和缩放');
        console.log('   • 节点点击交互');
        console.log('   • 行为系统集成');
        console.log('   • 递进解锁机制');
        console.log('⌨️ 键盘快捷键:');
        console.log('   按 T 键 - 运行基础测试');
        console.log('   按 1 键 - 模拟点击起始节点');
        console.log('   按 2 键 - 模拟点击战斗节点');
        console.log('   按 3 键 - 模拟点击采集节点');
        console.log('   按 4 键 - 模拟点击学习节点');
        console.log('   按 R 键 - 重置地图状态');
        console.log('   按 H 键 - 显示帮助信息');
        console.log('🖱️ 鼠标操作:');
        console.log('   • 拖拽地图移动视角');
        console.log('   • 点击节点进行交互');
        console.log('   • 滚轮缩放地图');
        console.log('🧪 =====================================');
    }

    /**
     * 键盘按键处理
     */
    private onKeyDown(event: EventKeyboard): void {
        switch (event.keyCode) {
            case KeyCode.KEY_T:
                this.runBasicTest();
                break;
            case KeyCode.DIGIT_1:
                this.simulateNodeClick('start');
                break;
            case KeyCode.DIGIT_2:
                this.simulateNodeClick('branchnode1');
                break;
            case KeyCode.DIGIT_3:
                this.simulateNodeClick('branchnode2');
                break;
            case KeyCode.DIGIT_4:
                this.simulateNodeClick('branchnode3');
                break;
            case KeyCode.KEY_R:
                this.resetMapState();
                break;
            case KeyCode.KEY_H:
                this.showTestInstructions();
                break;
        }
    }

    /**
     * 运行基础测试
     */
    private runBasicTest(): void {
        console.log('🧪 MapSystemTest: 开始运行基础测试');
        
        // 测试1: 检查组件是否正确初始化
        this.testComponentInitialization();
        
        // 测试2: 检查地图容器是否存在
        this.testMapContainer();
        
        // 测试3: 检查交互节点是否存在
        this.testInteractionNodes();
        
        // 测试4: 模拟节点点击
        this.scheduleOnce(() => {
            this.simulateNodeClick('start');
        }, 1.0);
        
        console.log('🧪 MapSystemTest: 基础测试完成');
    }

    /**
     * 测试组件初始化
     */
    private testComponentInitialization(): void {
        console.log('🧪 测试1: 组件初始化检查');
        
        if (this._mainUIController) {
            console.log('✅ MainUIController 初始化成功');
        } else {
            console.log('❌ MainUIController 初始化失败');
        }
        
        if (this._behaviorPanel) {
            console.log('✅ BehaviorPanel 初始化成功');
        } else {
            console.log('❌ BehaviorPanel 初始化失败');
        }
    }

    /**
     * 测试地图容器
     */
    private testMapContainer(): void {
        console.log('🧪 测试2: 地图容器检查');
        
        const mapContainer = find('Canvas/MainUI/BackPanel/LinearBranchMapContainer');
        if (mapContainer) {
            console.log('✅ 地图容器存在');
            
            const draggableComponent = mapContainer.getComponent('DraggableMapContainer');
            if (draggableComponent) {
                console.log('✅ 拖拽组件存在');
            } else {
                console.log('❌ 拖拽组件不存在');
            }
        } else {
            console.log('❌ 地图容器不存在');
        }
    }

    /**
     * 测试交互节点
     */
    private testInteractionNodes(): void {
        console.log('🧪 测试3: 交互节点检查');
        
        const nodeNames = ['StartNode', 'BranchNode1', 'BranchNode2', 'BranchNode3'];
        let foundNodes = 0;
        
        nodeNames.forEach(nodeName => {
            const node = find(`Canvas/MainUI/BackPanel/LinearBranchMapContainer/MapContainer/${nodeName}`);
            if (node) {
                foundNodes++;
                console.log(`✅ 找到节点: ${nodeName}`);
                
                const button = node.getComponent('cc.Button');
                if (button) {
                    console.log(`✅ ${nodeName} 有Button组件`);
                } else {
                    console.log(`❌ ${nodeName} 缺少Button组件`);
                }
            } else {
                console.log(`❌ 未找到节点: ${nodeName}`);
            }
        });
        
        console.log(`🧪 节点检查完成: ${foundNodes}/${nodeNames.length} 个节点存在`);
    }

    /**
     * 模拟节点点击
     */
    private simulateNodeClick(nodeId: string): void {
        console.log('🧪 MapSystemTest: 模拟节点点击', nodeId);
        
        if (this._mainUIController) {
            // 直接调用MainUIController的点击处理方法
            this._mainUIController.onMapNodeClick(null, nodeId);
        } else {
            console.warn('🧪 MapSystemTest: MainUIController不存在，无法模拟点击');
        }
    }

    /**
     * 重置地图状态
     */
    private resetMapState(): void {
        console.log('🧪 MapSystemTest: 重置地图状态');
        
        // 这里可以添加重置地图状态的逻辑
        // 比如重新锁定所有节点，只保留起始节点解锁
        
        console.log('🧪 地图状态已重置');
    }

    // ==================== 公共API ====================

    /**
     * 手动运行测试
     */
    public runTest(): void {
        this.runBasicTest();
    }

    /**
     * 获取测试结果
     */
    public getTestResults(): any {
        return {
            mainUIController: !!this._mainUIController,
            behaviorPanel: !!this._behaviorPanel,
            timestamp: Date.now()
        };
    }
}
