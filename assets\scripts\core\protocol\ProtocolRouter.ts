/**
 * Copyright (c) 2025 "放置". All rights reserved.
 * 协议路由系统 - 实现模块间解耦通信
 */

import { _decorator } from 'cc';

const { ccclass } = _decorator;

// ==================== 协议基础接口 ====================

/**
 * 协议消息基础接口
 */
export interface ProtocolMessage<T = any> {
  id: string;                          // 消息ID
  type: string;                        // 消息类型
  layer: ProtocolLayer;                // 协议层级
  payload: T;                          // 消息载荷
  metadata?: ProtocolMetadata;         // 元数据
  timestamp: number;                   // 时间戳
}

/**
 * 协议响应接口
 */
export interface ProtocolResponse<T = any> {
  id: string;                          // 对应的请求ID
  success: boolean;                    // 是否成功
  data?: T;                           // 响应数据
  error?: ProtocolError;              // 错误信息
  timestamp: number;                   // 响应时间戳
}

/**
 * 协议层级枚举
 */
export enum ProtocolLayer {
  UI = "ui",                          // UI层协议
  BUSINESS = "business",              // 业务层协议
  DATA = "data",                      // 数据层协议
  NETWORK = "network"                 // 网络层协议
}

/**
 * 协议元数据
 */
export interface ProtocolMetadata {
  userId?: string;                    // 用户ID
  sessionId?: string;                 // 会话ID
  priority?: "high" | "normal" | "low"; // 优先级
  timeout?: number;                   // 超时时间
  retryCount?: number;                // 重试次数
  correlationId?: string;             // 关联ID
}

/**
 * 协议错误
 */
export interface ProtocolError {
  code: string;                       // 错误码
  message: string;                    // 错误消息
  details?: any;                      // 错误详情
  retryable?: boolean;                // 是否可重试
}

// ==================== 协议处理器接口 ====================

/**
 * 协议处理器接口
 */
export interface ProtocolHandler {
  /**
   * 处理协议消息
   */
  handle(message: ProtocolMessage): Promise<ProtocolResponse>;
  
  /**
   * 获取支持的消息类型
   */
  getSupportedTypes(): string[];
  
  /**
   * 获取处理器名称
   */
  getName(): string;
}

/**
 * 协议中间件接口
 */
export interface ProtocolMiddleware {
  /**
   * 处理协议消息
   */
  process(message: ProtocolMessage, next: NextFunction): Promise<ProtocolResponse>;
  
  /**
   * 获取中间件名称
   */
  getName(): string;
  
  /**
   * 获取执行顺序
   */
  getOrder(): number;
}

/**
 * 下一个处理函数类型
 */
export type NextFunction = (message: ProtocolMessage) => Promise<ProtocolResponse>;

// ==================== 协议路由器实现 ====================

/**
 * 协议路由器 - 核心路由系统
 */
@ccclass('ProtocolRouter')
export class ProtocolRouter {
  private static _instance: ProtocolRouter;
  
  private handlers = new Map<string, ProtocolHandler>();
  private middleware: ProtocolMiddleware[] = [];
  private messageQueue = new Map<string, ProtocolMessage>();
  private responseCallbacks = new Map<string, (response: ProtocolResponse) => void>();
  
  /**
   * 获取单例实例
   */
  public static getInstance(): ProtocolRouter {
    if (!ProtocolRouter._instance) {
      ProtocolRouter._instance = new ProtocolRouter();
    }
    return ProtocolRouter._instance;
  }

  /**
   * 注册协议处理器
   */
  public register(handler: ProtocolHandler): void {
    const types = handler.getSupportedTypes();
    types.forEach(type => {
      if (this.handlers.has(type)) {
        console.warn(`⚠️ 协议处理器已存在: ${type}, 将被覆盖`);
      }
      this.handlers.set(type, handler);
      console.log(`✅ 注册协议处理器: ${type} → ${handler.getName()}`);
    });
  }

  /**
   * 注销协议处理器
   */
  public unregister(type: string): void {
    if (this.handlers.has(type)) {
      this.handlers.delete(type);
      console.log(`🗑️ 注销协议处理器: ${type}`);
    }
  }

  /**
   * 添加中间件
   */
  public use(middleware: ProtocolMiddleware): void {
    this.middleware.push(middleware);
    // 按执行顺序排序
    this.middleware.sort((a, b) => a.getOrder() - b.getOrder());
    console.log(`🔧 添加协议中间件: ${middleware.getName()} (顺序: ${middleware.getOrder()})`);
  }

  /**
   * 路由协议消息
   */
  public async route(message: ProtocolMessage): Promise<ProtocolResponse> {
    try {
      console.log(`📨 路由协议消息: ${message.type} (${message.id})`);
      
      // 验证消息格式
      this.validateMessage(message);
      
      // 查找处理器
      const handler = this.handlers.get(message.type);
      if (!handler) {
        throw new Error(`未找到协议处理器: ${message.type}`);
      }
      
      // 构建中间件链
      const middlewareChain = this.buildMiddlewareChain(handler);
      
      // 执行中间件链
      const response = await middlewareChain(message);
      
      console.log(`✅ 协议消息处理完成: ${message.type} (${response.success ? '成功' : '失败'})`);
      return response;
      
    } catch (error) {
      console.error(`❌ 协议消息处理失败: ${message.type}`, error);
      return this.createErrorResponse(message.id, error);
    }
  }

  /**
   * 发送协议消息（异步）
   */
  public async send(message: ProtocolMessage): Promise<ProtocolResponse> {
    return this.route(message);
  }

  /**
   * 发送协议消息（回调）
   */
  public sendWithCallback(
    message: ProtocolMessage, 
    callback: (response: ProtocolResponse) => void
  ): void {
    this.responseCallbacks.set(message.id, callback);
    
    this.route(message)
      .then(response => {
        const cb = this.responseCallbacks.get(message.id);
        if (cb) {
          cb(response);
          this.responseCallbacks.delete(message.id);
        }
      })
      .catch(error => {
        const cb = this.responseCallbacks.get(message.id);
        if (cb) {
          cb(this.createErrorResponse(message.id, error));
          this.responseCallbacks.delete(message.id);
        }
      });
  }

  /**
   * 广播协议消息
   */
  public broadcast(message: ProtocolMessage): void {
    // 找到所有支持该消息类型的处理器
    const supportedHandlers: ProtocolHandler[] = [];
    
    this.handlers.forEach(handler => {
      if (handler.getSupportedTypes().includes(message.type)) {
        supportedHandlers.push(handler);
      }
    });
    
    // 并行处理
    supportedHandlers.forEach(handler => {
      this.route(message).catch(error => {
        console.error(`广播消息处理失败: ${handler.getName()}`, error);
      });
    });
  }

  /**
   * 构建中间件链
   */
  private buildMiddlewareChain(handler: ProtocolHandler): NextFunction {
    let index = 0;
    
    const dispatch = async (message: ProtocolMessage): Promise<ProtocolResponse> => {
      if (index < this.middleware.length) {
        const middleware = this.middleware[index++];
        return middleware.process(message, dispatch);
      } else {
        // 执行最终处理器
        return handler.handle(message);
      }
    };
    
    return dispatch;
  }

  /**
   * 验证消息格式
   */
  private validateMessage(message: ProtocolMessage): void {
    if (!message.id) {
      throw new Error('消息ID不能为空');
    }
    if (!message.type) {
      throw new Error('消息类型不能为空');
    }
    if (!message.layer) {
      throw new Error('协议层级不能为空');
    }
    if (message.payload === undefined) {
      throw new Error('消息载荷不能为空');
    }
  }

  /**
   * 创建错误响应
   */
  private createErrorResponse(messageId: string, error: any): ProtocolResponse {
    return {
      id: messageId,
      success: false,
      error: {
        code: error.code || 'UNKNOWN_ERROR',
        message: error.message || '未知错误',
        details: error,
        retryable: false
      },
      timestamp: Date.now()
    };
  }

  /**
   * 获取路由器状态
   */
  public getStatus(): {
    handlerCount: number;
    middlewareCount: number;
    queueSize: number;
    callbackCount: number;
  } {
    return {
      handlerCount: this.handlers.size,
      middlewareCount: this.middleware.length,
      queueSize: this.messageQueue.size,
      callbackCount: this.responseCallbacks.size
    };
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    this.handlers.clear();
    this.middleware.length = 0;
    this.messageQueue.clear();
    this.responseCallbacks.clear();
    console.log('🧹 协议路由器已清理');
  }
}

// ==================== 协议工具类 ====================

/**
 * 协议工具类
 */
export class ProtocolUtils {
  /**
   * 生成消息ID
   */
  static generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 创建协议消息
   */
  static createMessage<T>(
    type: string,
    layer: ProtocolLayer,
    payload: T,
    metadata?: ProtocolMetadata
  ): ProtocolMessage<T> {
    return {
      id: this.generateMessageId(),
      type,
      layer,
      payload,
      metadata,
      timestamp: Date.now()
    };
  }

  /**
   * 创建成功响应
   */
  static createSuccessResponse<T>(messageId: string, data: T): ProtocolResponse<T> {
    return {
      id: messageId,
      success: true,
      data,
      timestamp: Date.now()
    };
  }

  /**
   * 创建错误响应
   */
  static createErrorResponse(
    messageId: string,
    code: string,
    message: string,
    details?: any
  ): ProtocolResponse {
    return {
      id: messageId,
      success: false,
      error: {
        code,
        message,
        details,
        retryable: false
      },
      timestamp: Date.now()
    };
  }
}
