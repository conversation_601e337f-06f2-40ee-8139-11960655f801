{"$schema": "https://schemastore.azurewebsites.net/schemas/json/tsconfig.json", "compilerOptions": {"target": "ES2017", "module": "CommonJS", "moduleResolution": "node", "inlineSourceMap": true, "inlineSources": true, "esModuleInterop": true, "skipLibCheck": true, "strict": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "outDir": "./dist", "rootDir": ".", "allowJs": true, "types": ["node", "@cocos/creator-types/editor"]}}