/**
 * 游戏核心类型定义
 * 东方妖怪捉宠放置游戏 - Cocos Creator版本
 */

// 游戏状态枚举
export enum GameState {
    LOADING = 'loading',
    MENU = 'menu',
    PLAYING = 'playing',
    PAUSED = 'paused',
    BATTLE = 'battle',
    SETTINGS = 'settings'
}

// 场景类型枚举
export enum SceneType {
    LAUNCH = 'Launch',
    MAIN = 'Main',
    BATTLE = 'Battle',
    PET_GARDEN = 'PetGarden',    // 妖怪花园
    MARKET = 'Market',
    PRODUCTION = 'Production'     // 生产区域
}

// 过渡效果类型
export enum TransitionType {
    FADE = 'fade',
    SLIDE_LEFT = 'slideLeft',
    SLIDE_RIGHT = 'slideRight',
    SLIDE_UP = 'slideUp',
    SLIDE_DOWN = 'slideDown'
}

// 网络状态枚举
export enum NetworkType {
    WIFI = 'wifi',
    CELLULAR = '2g/3g/4g/5g',
    NONE = 'none',
    UNKNOWN = 'unknown'
}

// 连接状态枚举
export enum ConnectionState {
    DISCONNECTED = 'disconnected',
    CONNECTING = 'connecting',
    CONNECTED = 'connected',
    RECONNECTING = 'reconnecting',
    ERROR = 'error'
}

// 滑动方向枚举
export enum SwipeDirection {
    UP = 'up',
    DOWN = 'down',
    LEFT = 'left',
    RIGHT = 'right'
}

// 妖怪相关类型
export enum PetType {
    WOOD = 'wood',           // 木系妖怪
    FIRE = 'fire',           // 火系妖怪
    WATER = 'water',         // 水系妖怪
    THUNDER = 'thunder',     // 雷系妖怪
    ICE = 'ice',            // 冰系妖怪
    DARK = 'dark'           // 暗系妖怪
}

export enum PetRarity {
    COMMON = 'common',       // 普通
    EXCELLENT = 'excellent', // 优秀
    RARE = 'rare',          // 稀有
    EPIC = 'epic',          // 史诗
    LEGENDARY = 'legendary'  // 传说
}

export enum SkillType {
    ATTACK = 'attack',        // 攻击技能
    DEFENSE = 'defense',      // 防御技能
    HEAL = 'heal',           // 治疗技能
    BUFF = 'buff',           // 增益技能
    DEBUFF = 'debuff',       // 减益技能
    CAPTURE = 'capture',     // 捕捉技能
    PRODUCTION = 'production' // 生产技能
}

export enum ItemType {
    WEAPON = 'weapon',        // 武器
    ARMOR = 'armor',          // 防具
    ACCESSORY = 'accessory',  // 饰品
    CONSUMABLE = 'consumable', // 消耗品
    MATERIAL = 'material',    // 材料
    CAPTURE_TOOL = 'capture_tool', // 捕捉道具
    PET_FOOD = 'pet_food',    // 妖怪食物
    EVOLUTION_STONE = 'evolution_stone' // 进化石
}

// 基础接口定义
export interface IGameConfig {
    version: string;
    debug: boolean;
    maxLevel: number;
    autoSaveInterval: number;
}

export interface ISceneData {
    sceneName: string;
    sceneType: SceneType;
    data?: any;
}

export interface INetworkRequest {
    id: string;
    url: string;
    method: 'GET' | 'POST' | 'PUT' | 'DELETE';
    data?: any;
    headers?: Record<string, string>;
    timeout?: number;
}

export interface INetworkError {
    code: number;
    message: string;
    details?: any;
}

// 事件相关接口
export interface IEventData {
    type: string;
    data?: any;
    timestamp: number;
}

// 妖怪捉宠游戏相关接口
export interface IPlayerData {
    id: string;
    name: string;
    level: number;
    experience: number;
    pets: IPetData[];
    skills: ISkillData[];
    items: IItemData[];
    stats: IPlayerStats;
    capturePresets: ICapturePreset[];
}

export interface ISkillData {
    id: string;
    name: string;
    type: SkillType;
    level: number;
    damage?: number;
    cooldown: number;
    description: string;
}

export interface IItemData {
    id: string;
    name: string;
    type: ItemType;
    quality: number;
    quantity: number;
    description: string;
}

export interface IPlayerStats {
    health: number;
    maxHealth: number;
    attack: number;
    defense: number;
    speed: number;
    criticalRate: number;
}

// 妖怪相关接口
export interface IPetData {
    id: string;
    templateId: string;
    name: string;
    type: PetType;
    rarity: PetRarity;
    level: number;
    experience: number;
    attributes: IPetAttributes;
    skills: IPetSkill[];
    status: PetStatus;
    evolutionLevel: number;
}

export interface IPetAttributes {
    hp: number;
    attack: number;
    defense: number;
    speed: number;
    intelligence: number;
    luck: number;
}

export interface IPetSkill {
    id: string;
    level: number;
    experience: number;
}

export enum PetStatus {
    IDLE = 'idle',           // 空闲
    BATTLE = 'battle',       // 战斗中
    PRODUCTION = 'production', // 生产中
    TRAINING = 'training',   // 训练中
    EVOLUTION = 'evolution'  // 进化中
}

export interface ICapturePreset {
    id: string;
    name: string;
    targets: ICaptureTarget[];
    skills: string[];
    items: string[];
    conditions: ICaptureCondition[];
    isActive: boolean;
}

export interface ICaptureTarget {
    monsterId: string;
    priority: number;
    quantity: number;
    minQuality: number;
}

export interface ICaptureCondition {
    type: 'hp' | 'status' | 'time' | 'team';
    operator: '<' | '>' | '=' | '!=';
    value: number | string;
    logic: 'AND' | 'OR';
}

// 行动条相关接口
export interface IActionBarData {
    playerId: string;
    actions: IGameAction[];
    currentTime: number;
}

export interface IGameAction {
    id: string;
    type: ActionType;
    category: ActionCategory;
    duration: number;
    completionTime?: number;
    data: any;
}

export enum ActionType {
    SERIAL = 'serial',     // 串行行动
    PARALLEL = 'parallel'  // 并行行动
}

export enum ActionCategory {
    BATTLE = 'battle',
    PRODUCTION = 'production',
    CAPTURE = 'capture',
    PET_TRAINING = 'pet_training',
    EVOLUTION = 'evolution'
}

// 回调函数类型
export type EventCallback = (...args: any[]) => void;
export type AsyncEventCallback = (...args: any[]) => Promise<void>;
export type RequestInterceptor = (request: INetworkRequest) => INetworkRequest;
export type ResponseInterceptor = (response: any) => any;
