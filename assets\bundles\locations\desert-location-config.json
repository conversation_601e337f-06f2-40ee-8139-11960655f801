{"locationId": "Desert", "name": "炽热沙漠", "description": "炎热干燥的沙漠地带，隐藏着古老的宝藏和危险的生物", "type": "exploration", "level": 5, "unlockConditions": {"playerLevel": 5, "requiredItems": ["desert_map"], "completedQuests": ["forest_exploration"]}, "mapConfig": {"backgroundImagePath": "bundles/locations/textures/desert_background", "mapSize": {"width": 1800, "height": 1400}, "nodeSpacing": {"x": 250, "y": 180}, "startPosition": {"x": -700, "y": 0}, "branchCount": 4, "nodesPerBranch": 6}, "environment": {"weather": "hot", "timeOfDay": "day", "backgroundMusic": "bundles/configs/audio/desert_ambient", "ambientSounds": ["wind", "sand"], "lightingColor": {"r": 1.0, "g": 0.8, "b": 0.4, "a": 1.0}}, "resources": {"monsters": [{"id": "desert_scorpion", "name": "沙漠蝎子", "level": 5, "spawnRate": 0.4, "maxCount": 6, "iconPath": "bundles/locations/textures/monsters/desert_scorpion_icon", "modelPath": "bundles/locations/prefabs/monsters/DesertScorpion"}, {"id": "desert_snake", "name": "沙漠蛇", "level": 7, "spawnRate": 0.2, "maxCount": 3, "iconPath": "bundles/locations/textures/monsters/desert_snake_icon", "modelPath": "bundles/locations/prefabs/monsters/DesertSnake"}, {"id": "sand_elemental", "name": "沙元素", "level": 10, "spawnRate": 0.05, "maxCount": 1, "iconPath": "bundles/locations/textures/monsters/sand_elemental_icon", "modelPath": "bundles/locations/prefabs/monsters/SandElemental"}], "collectibles": [{"id": "sand_crystal", "name": "沙晶", "spawnRate": 0.2, "maxCount": 5, "iconPath": "bundles/locations/textures/items/sand_crystal_icon"}, {"id": "cactus_fruit", "name": "仙人掌果", "spawnRate": 0.3, "maxCount": 7, "iconPath": "bundles/locations/textures/items/cactus_fruit_icon"}, {"id": "ancient_coin", "name": "古代金币", "spawnRate": 0.1, "maxCount": 3, "iconPath": "bundles/locations/textures/items/ancient_coin_icon"}]}, "rewards": {"baseExp": 25, "baseGold": 15, "dropTables": ["desert_common", "desert_rare", "desert_legendary"]}, "uiElements": [{"type": "button", "id": "explore_button", "position": {"x": 0, "y": -200}, "text": "探索沙漠", "iconPath": "bundles/locations/textures/ui/desert_explore_icon", "action": "startExploration"}, {"type": "button", "id": "dig_button", "position": {"x": 150, "y": -200}, "text": "挖掘宝藏", "iconPath": "bundles/locations/textures/ui/dig_icon", "action": "startDigging"}, {"type": "button", "id": "rest_button", "position": {"x": -150, "y": -200}, "text": "绿洲休息", "iconPath": "bundles/locations/textures/ui/rest_icon", "action": "restAtOasis"}, {"type": "temperature_gauge", "id": "heat_gauge", "position": {"x": 250, "y": 200}, "size": {"width": 30, "height": 150}, "fillImagePath": "bundles/locations/textures/ui/heat_fill"}], "nodeData": [{"nodeId": "desert_node_1", "position": {"x": -700, "y": 0}, "type": "start", "name": "沙漠边缘", "description": "进入炽热沙漠的起点", "unlocked": true, "iconPath": "bundles/locations/textures/nodes/desert_start_node"}, {"nodeId": "desert_node_2", "position": {"x": -450, "y": 0}, "type": "exploration", "name": "沙丘区域", "description": "起伏的沙丘地带", "unlocked": false, "iconPath": "bundles/locations/textures/nodes/dune_node"}, {"nodeId": "desert_node_3", "position": {"x": -200, "y": 0}, "type": "treasure", "name": "古代遗迹", "description": "埋藏着宝藏的古老遗迹", "unlocked": false, "iconPath": "bundles/locations/textures/nodes/ruins_node"}, {"nodeId": "desert_node_4", "position": {"x": 50, "y": 0}, "type": "oasis", "name": "绿洲", "description": "沙漠中的生命之源", "unlocked": false, "iconPath": "bundles/locations/textures/nodes/oasis_node"}]}