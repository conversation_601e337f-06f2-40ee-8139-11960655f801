{"// Copyright (c) 2025 \"放置\". All rights reserved.": "", "version": "1.0.0", "config_type": "server_configuration", "last_updated": "2025-01-10T00:00:00Z", "environments": {"development": {"name": "开发环境", "description": "本地开发服务器", "enabled": true, "priority": 10, "config": {"base_url": "http://localhost:3002", "timeout": 3000, "retry_count": 2, "api_version": "v1", "endpoints": {"location_config": "/api/v1/location/config", "location_list": "/api/v1/location/list", "behavior_config": "/api/v1/behavior/config", "reward_config": "/api/v1/reward/config", "version_check": "/api/v1/config/version", "user_progress": "/api/v1/user/progress", "unlock_validation": "/api/v1/location/unlock"}}}, "testing": {"name": "测试环境", "description": "阿里云测试服务器", "enabled": false, "priority": 90, "config": {"base_url": "https://test-api.yourgame.com", "timeout": 5000, "retry_count": 3, "api_version": "v1", "endpoints": {"location_config": "/api/v1/location/config", "location_list": "/api/v1/location/list", "behavior_config": "/api/v1/behavior/config", "reward_config": "/api/v1/reward/config", "version_check": "/api/v1/config/version", "user_progress": "/api/v1/user/progress", "unlock_validation": "/api/v1/location/unlock"}}}, "production": {"name": "生产环境", "description": "阿里云生产服务器", "enabled": false, "priority": 100, "config": {"base_url": "https://api.yourgame.com", "timeout": 8000, "retry_count": 3, "api_version": "v1", "endpoints": {"location_config": "/api/v1/location/config", "location_list": "/api/v1/location/list", "behavior_config": "/api/v1/behavior/config", "reward_config": "/api/v1/reward/config", "version_check": "/api/v1/config/version", "user_progress": "/api/v1/user/progress", "unlock_validation": "/api/v1/location/unlock"}}}}, "api_specifications": {"location_config": {"method": "GET", "path": "/api/v1/location/config/{locationId}", "parameters": {"locationId": "string (required)", "version": "string (optional, default: latest)", "include_behaviors": "boolean (optional, default: true)", "include_rewards": "boolean (optional, default: true)"}, "response_format": {"success": {"status": 200, "data": {"locationId": "string", "version": "string", "config": "object", "checksum": "string", "last_modified": "timestamp"}}, "error": {"status": "4xx/5xx", "error": {"code": "string", "message": "string", "details": "object"}}}}, "location_list": {"method": "GET", "path": "/api/v1/location/list", "parameters": {"version": "string (optional, default: latest)", "include_locked": "boolean (optional, default: false)", "user_id": "string (optional, for personalized data)"}, "response_format": {"success": {"status": 200, "data": {"version": "string", "locations": [{"locationId": "string", "name": "string", "description": "string", "type": "string", "level": "number", "unlocked": "boolean", "last_modified": "timestamp"}]}}}}, "version_check": {"method": "GET", "path": "/api/v1/config/version", "parameters": {"client_version": "string (optional)", "config_types": "array (optional, default: all)"}, "response_format": {"success": {"status": 200, "data": {"server_version": "string", "client_version": "string", "update_required": "boolean", "available_updates": [{"config_type": "string", "current_version": "string", "latest_version": "string", "update_size": "number", "priority": "string"}]}}}}, "unlock_validation": {"method": "POST", "path": "/api/v1/location/unlock", "parameters": {"user_id": "string (required)", "location_id": "string (required)", "client_data": "object (optional)"}, "response_format": {"success": {"status": 200, "data": {"unlocked": "boolean", "requirements_met": "object", "missing_requirements": "array", "unlock_timestamp": "timestamp"}}}}}, "cache_headers": {"location_config": {"cache_control": "public, max-age=1800", "etag_support": true, "last_modified_support": true}, "location_list": {"cache_control": "public, max-age=300", "etag_support": true}, "version_check": {"cache_control": "no-cache", "etag_support": false}}, "error_handling": {"network_timeout": {"action": "retry_with_backoff", "max_retries": 3, "fallback": "use_cached_data"}, "server_error_5xx": {"action": "retry_with_backoff", "max_retries": 2, "fallback": "use_cached_data"}, "client_error_4xx": {"action": "log_and_fallback", "fallback": "use_default_config"}, "config_validation_failed": {"action": "reject_and_fallback", "fallback": "use_previous_version"}}}