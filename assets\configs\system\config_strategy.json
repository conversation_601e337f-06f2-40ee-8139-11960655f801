{"// Copyright (c) 2025 \"放置\". All rights reserved.": "", "version": "1.0.0", "config_type": "configuration_strategy", "last_updated": "2025-01-10T00:00:00Z", "storage_strategy": {"description": "安全的配置表存储和获取策略", "client_safe_configs": {"description": "客户端安全配置，不涉及游戏逻辑和经济系统", "files": ["assets/configs/client/ui_layouts.json", "assets/configs/client/display_configs.json", "assets/configs/client/audio_configs.json", "assets/configs/client/basic_info.json", "assets/configs/system/servers.json"], "security_level": "public", "update_frequency": "with_game_version", "cache_duration": "permanent"}, "server_secure_configs": {"description": "服务器安全配置，包含游戏逻辑和经济数据", "security_level": "server_only", "endpoints": {"location_display": "/api/v1/location/display/{locationId}", "location_list": "/api/v1/location/list", "unlock_check": "/api/v1/location/unlock/check", "unlock_request": "/api/v1/location/unlock/request", "behavior_execute": "/api/v1/behavior/execute", "reward_claim": "/api/v1/reward/claim", "user_progress": "/api/v1/user/progress", "version_check": "/api/v1/config/version"}, "update_frequency": "real_time", "cache_duration": 300, "retry_policy": {"max_retries": 3, "retry_delay": 1000, "backoff_multiplier": 2}}, "cache_configs": {"description": "本地缓存的服务器配置", "location": "assets/configs/areas/dynamic/", "max_size": "50MB", "cleanup_policy": "lru", "compression": true}}, "data_flow": {"startup_sequence": [{"step": 1, "action": "load_local_configs", "description": "加载本地基础配置，确保游戏可启动", "timeout": 5000, "required": true}, {"step": 2, "action": "check_cache_validity", "description": "检查本地缓存的有效性", "timeout": 2000, "required": false}, {"step": 3, "action": "request_server_configs", "description": "异步请求服务器最新配置", "timeout": 10000, "required": false}, {"step": 4, "action": "merge_and_cache", "description": "合并配置并更新缓存", "timeout": 3000, "required": false}], "runtime_updates": [{"trigger": "user_action", "action": "validate_unlock_conditions", "description": "用户操作时验证解锁条件"}, {"trigger": "periodic_check", "interval": 300000, "action": "sync_server_configs", "description": "定期同步服务器配置"}, {"trigger": "version_mismatch", "action": "force_update_configs", "description": "版本不匹配时强制更新"}]}, "version_control": {"format": "semantic_versioning", "example": "1.2.3", "comparison_strategy": "server_authoritative", "rollback_support": true, "max_versions_cached": 3}, "fallback_strategy": {"server_unavailable": "use_local_configs", "config_corrupted": "use_backup_configs", "version_incompatible": "use_compatible_version", "cache_full": "cleanup_oldest_first"}, "security": {"config_validation": true, "checksum_verification": true, "encrypted_sensitive_data": false, "rate_limiting": {"max_requests_per_minute": 60, "burst_limit": 10}}}