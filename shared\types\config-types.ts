// Copyright (c) 2025 "放置". All rights reserved.

/**
 * 配置表基础接口
 */
export interface BaseConfig {
  version: string; // 配置版本号
  config_type: string; // 配置类型
  last_updated?: string; // 最后更新时间
  checksum?: string; // 校验和
}

/**
 * 前端UI布局配置
 */
export interface UILayoutConfig extends BaseConfig {
  scene_name: string;
  layout: {
    [key: string]: UIElement;
  };
  responsive?: {
    [breakpoint: string]: Partial<UIElement>;
  };
}

export interface UIElement {
  position?: { x: number | string; y: number | string };
  size?: { width: number | string; height: number | string };
  anchor?: string;
  background?: string;
  elements?: { [key: string]: UIElement };
  [key: string]: any;
}

/**
 * 动画配置
 */
export interface AnimationConfig extends BaseConfig {
  animations: {
    [name: string]: Animation;
  };
  presets?: {
    [name: string]: AnimationPreset;
  };
}

export interface Animation {
  type: 'scale' | 'position' | 'rotation' | 'opacity' | 'composite' | 'sequence' | 'loop';
  duration: number;
  from?: any;
  to?: any;
  easing?: string;
  delay?: number;
  auto_reverse?: boolean;
  animations?: Animation[];
}

export interface AnimationPreset {
  base: string;
  [key: string]: any;
}

/**
 * 资源配置
 */
export interface ResourceConfig extends BaseConfig {
  categories: {
    [category: string]: {
      [subcategory: string]: {
        [name: string]: string;
      };
    };
  };
  atlas_configs?: {
    [name: string]: AtlasConfig;
  };
}

export interface AtlasConfig {
  path: string;
  includes: string[];
}

/**
 * 本地化配置
 */
export interface LocalizationConfig extends BaseConfig {
  language: string;
  category: string;
  texts: {
    [namespace: string]: {
      [key: string]: string | LocalizationTexts;
    };
  };
}

export interface LocalizationTexts {
  [key: string]: string | LocalizationTexts;
}

/**
 * 角色数值配置
 */
export interface CharacterStatsConfig extends BaseConfig {
  base_stats: {
    [class_name: string]: CharacterStats;
  };
  level_growth: {
    [stat_name: string]: {
      [class_name: string]: number;
    };
  };
  stat_caps: {
    [stat_name: string]: number;
  };
  formulas: {
    [formula_name: string]: string;
  };
}

export interface CharacterStats {
  health: number;
  mana: number;
  attack: number;
  defense: number;
  speed: number;
  critical_rate: number;
  critical_damage: number;
  health_regen: number;
  mana_regen: number;
}

/**
 * 货币配置
 */
export interface CurrencyConfig extends BaseConfig {
  currencies: {
    [currency_id: string]: Currency;
  };
  idle_income: IdleIncomeConfig;
  conversion_rates: {
    [conversion_id: string]: ConversionRate;
  };
  anti_cheat: AntiCheatConfig;
}

export interface Currency {
  id: string;
  name: string;
  icon: string;
  max_amount: number;
  decimal_places: number;
  default_amount: number;
  can_purchase: boolean;
  can_trade: boolean;
  storage_type: 'server' | 'local';
  regeneration?: {
    enabled: boolean;
    rate: number;
    interval: number;
    max_regen: number;
  };
}

export interface IdleIncomeConfig {
  base_gold_per_second: number;
  base_exp_per_second: number;
  level_multiplier: {
    [currency: string]: number;
  };
  max_offline_hours: number;
  offline_efficiency: number;
  formulas: {
    [formula_name: string]: string;
  };
}

export interface ConversionRate {
  rate: number;
  daily_limit: number;
  cost_formula: string;
}

export interface AntiCheatConfig {
  max_income_per_hour: {
    [currency: string]: number;
  };
  suspicious_threshold: {
    [metric: string]: number;
  };
  validation_rules: string[];
}

/**
 * 游戏常量配置
 */
export interface GameConstantsConfig extends BaseConfig {
  game_info: GameInfo;
  system_limits: SystemLimits;
  time_constants: TimeConstants;
  ui_constants: UIConstants;
  network_constants: NetworkConstants;
  quality_levels: QualityLevels;
  character_classes: CharacterClasses;
  equipment_slots: EquipmentSlots;
  error_codes: ErrorCodes;
}

export interface GameInfo {
  name: string;
  version: string;
  build: string;
  min_client_version: string;
  api_version: string;
}

export interface SystemLimits {
  [limit_name: string]: number;
}

export interface TimeConstants {
  [constant_name: string]: number;
}

export interface UIConstants {
  animation_duration: {
    [speed: string]: number;
  };
  [constant_name: string]: any;
}

export interface NetworkConstants {
  [constant_name: string]: number;
}

export interface QualityLevels {
  [quality: string]: QualityLevel;
}

export interface QualityLevel {
  id: number;
  name: string;
  color: string;
}

export interface CharacterClasses {
  [class_id: string]: CharacterClass;
}

export interface CharacterClass {
  id: number;
  name: string;
  icon: string;
}

export interface EquipmentSlots {
  [slot_id: string]: EquipmentSlot;
}

export interface EquipmentSlot {
  id: number;
  name: string;
  icon: string;
}

export interface ErrorCodes {
  [error_name: string]: number;
}

/**
 * 版本清单配置
 */
export interface VersionManifestConfig extends BaseConfig {
  manifest_version: string;
  config_versions: {
    frontend: { [config_name: string]: string };
    backend: { [config_name: string]: string };
    shared: { [config_name: string]: string };
  };
  checksums: {
    frontend: { [config_name: string]: string };
    backend: { [config_name: string]: string };
    shared: { [config_name: string]: string };
  };
  update_info: UpdateInfo;
  compatibility: CompatibilityInfo;
  download_urls: DownloadUrls;
  sync_strategy: SyncStrategy;
  validation: ValidationConfig;
}

export interface UpdateInfo {
  critical_updates: string[];
  optional_updates: string[];
  deprecated_configs: string[];
  migration_required: boolean;
}

export interface CompatibilityInfo {
  min_client_version: string;
  min_server_version: string;
  supported_platforms: string[];
  breaking_changes: string[];
}

export interface DownloadUrls {
  base_url: string;
  cdn_urls: string[];
}

export interface SyncStrategy {
  check_interval: number;
  force_update_threshold: number;
  incremental_update: boolean;
  compression: boolean;
  cache_duration: number;
}

export interface ValidationConfig {
  signature_required: boolean;
  checksum_validation: boolean;
  version_validation: boolean;
  integrity_check: boolean;
}
