/**
 * 交互地图面板组件
 * 管理可拖拽的场景地图和交互节点
 * 集成到现有的UI系统架构中
 */

import { _decorator, Component, Node, Vec3, UITransform, input, Input, EventTouch, Sprite, Color, Camera } from 'cc';
import { EventManager } from '../../managers/EventManager';
import { BaseUIPanel } from '../base/BaseUIPanel';
import { UIPanelType } from '../types/UITypes';
import { BehaviorType, IBehaviorData } from './BehaviorPanel';
import { DraggableMapContainer } from '../components/DraggableMapContainer';
import { InteractionNode } from '../components/InteractionNode';

const { ccclass, property } = _decorator;

/**
 * 交互节点数据接口
 * 基于现有的BehaviorType扩展
 */
export interface IInteractionNodeData {
    /** 节点ID */
    id: string;

    /** 节点名称 */
    name: string;

    /** 节点描述 */
    description: string;

    /** 节点类型 - 基于现有的BehaviorType */
    type: BehaviorType;

    /** 世界坐标位置 */
    worldPosition: Vec3;

    /** 是否已解锁 */
    unlocked: boolean;

    /** 前置条件节点ID列表 */
    prerequisites: string[];

    /** 解锁后的节点ID列表 */
    unlocks: string[];

    /** 关联的行为数据 */
    behaviorData?: IBehaviorData;

    /** 节点图标资源路径 */
    iconPath?: string;

    /** 自定义数据 */
    customData?: any;
}

/**
 * 地图配置接口
 */
export interface IMapConfig {
    /** 地图大小 */
    mapSize: Vec3;
    
    /** 初始缩放 */
    initialScale: number;
    
    /** 最小缩放 */
    minScale: number;
    
    /** 最大缩放 */
    maxScale: number;
    
    /** 拖拽边界 */
    dragBounds: {
        left: number;
        right: number;
        top: number;
        bottom: number;
    };
    
    /** 背景图片路径 */
    backgroundImagePath?: string;
}

@ccclass('InteractiveMapPanel')
export class InteractiveMapPanel extends Component {
    
    @property({ type: Node, tooltip: '地图容器节点' })
    public mapContainer: Node | null = null;
    
    @property({ type: Node, tooltip: '背景节点' })
    public backgroundNode: Node | null = null;
    
    @property({ type: Node, tooltip: '交互节点容器' })
    public interactionContainer: Node | null = null;
    
    @property({ type: Camera, tooltip: '地图相机' })
    public mapCamera: Camera | null = null;
    
    @property({ tooltip: '是否启用拖拽' })
    public enableDrag: boolean = true;
    
    @property({ tooltip: '是否启用缩放' })
    public enableZoom: boolean = true;
    
    @property({ tooltip: '拖拽阻尼系数' })
    public dragDamping: number = 0.8;
    
    @property({ tooltip: '缩放敏感度' })
    public zoomSensitivity: number = 0.1;
    
    // 私有属性
    private _mapConfig: IMapConfig = {
        mapSize: new Vec3(2000, 1500, 0),
        initialScale: 1.0,
        minScale: 0.5,
        maxScale: 2.0,
        dragBounds: {
            left: -500,
            right: 500,
            top: 300,
            bottom: -300
        }
    };
    
    private _draggableContainer: DraggableMapContainer | null = null;
    private _interactionNodes: Map<string, InteractionNode> = new Map();
    private _nodeData: Map<string, IInteractionNodeData> = new Map();
    
    // 拖拽相关
    private _isDragging: boolean = false;
    private _lastTouchPosition: Vec3 = new Vec3();
    private _dragStartPosition: Vec3 = new Vec3();
    
    protected onLoad(): void {
        console.log('🗺️ InteractiveMapPanel: 交互地图面板加载');
        this.initializeComponents();
        this.setupInputEvents();
        this.initializeMapData();
    }

    protected start(): void {
        this.setupInitialView();
    }

    protected onDestroy(): void {
        this.cleanupInputEvents();
    }

    /**
     * 初始化组件
     */
    private initializeComponents(): void {
        // 自动查找地图容器
        if (!this.mapContainer) {
            this.mapContainer = this.node.getChildByName('MapContainer');
        }
        
        // 自动查找背景节点
        if (!this.backgroundNode) {
            this.backgroundNode = this.mapContainer?.getChildByName('Background') || null;
        }
        
        // 自动查找交互节点容器
        if (!this.interactionContainer) {
            this.interactionContainer = this.mapContainer?.getChildByName('InteractionNodes') || null;
        }
        
        // 获取或创建拖拽容器组件
        if (this.mapContainer) {
            this._draggableContainer = this.mapContainer.getComponent(DraggableMapContainer);
            if (!this._draggableContainer) {
                this._draggableContainer = this.mapContainer.addComponent(DraggableMapContainer);
            }
        }
        
        console.log('🗺️ InteractiveMapPanel: 组件初始化完成', {
            mapContainer: !!this.mapContainer,
            backgroundNode: !!this.backgroundNode,
            interactionContainer: !!this.interactionContainer,
            draggableContainer: !!this._draggableContainer
        });
    }

    /**
     * 设置输入事件
     */
    private setupInputEvents(): void {
        if (!this.enableDrag) return;
        
        this.node.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(Input.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
        
        console.log('🗺️ InteractiveMapPanel: 输入事件设置完成');
    }

    /**
     * 清理输入事件
     */
    private cleanupInputEvents(): void {
        this.node.off(Input.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.off(Input.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.off(Input.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
    }

    /**
     * 初始化地图数据
     */
    private initializeMapData(): void {
        // 创建示例交互节点数据
        const sampleNodes: IInteractionNodeData[] = [
            {
                id: 'start_village',
                name: '新手村',
                description: '冒险的起点',
                type: 'location',
                worldPosition: new Vec3(0, 0, 0),
                unlocked: true,
                prerequisites: [],
                unlocks: ['forest_entrance', 'blacksmith']
            },
            {
                id: 'forest_entrance',
                name: '森林入口',
                description: '神秘森林的入口',
                type: 'location',
                worldPosition: new Vec3(200, 100, 0),
                unlocked: false,
                prerequisites: ['start_village'],
                unlocks: ['deep_forest', 'herb_gathering']
            },
            {
                id: 'blacksmith',
                name: '铁匠铺',
                description: '可以锻造装备的地方',
                type: 'npc',
                worldPosition: new Vec3(-150, -80, 0),
                unlocked: false,
                prerequisites: ['start_village'],
                unlocks: ['weapon_craft', 'armor_craft']
            },
            {
                id: 'deep_forest',
                name: '森林深处',
                description: '危险的森林深处',
                type: 'location',
                worldPosition: new Vec3(400, 200, 0),
                unlocked: false,
                prerequisites: ['forest_entrance'],
                unlocks: ['boss_encounter']
            },
            {
                id: 'herb_gathering',
                name: '采药点',
                description: '收集草药的地方',
                type: 'resource',
                worldPosition: new Vec3(150, 250, 0),
                unlocked: false,
                prerequisites: ['forest_entrance'],
                unlocks: ['alchemy_lab']
            }
        ];
        
        // 存储节点数据
        sampleNodes.forEach(nodeData => {
            this._nodeData.set(nodeData.id, nodeData);
        });
        
        console.log('🗺️ InteractiveMapPanel: 地图数据初始化完成', this._nodeData.size);
    }

    /**
     * 设置初始视图
     */
    private setupInitialView(): void {
        if (!this.mapContainer) return;
        
        // 设置初始缩放
        this.mapContainer.setScale(this._mapConfig.initialScale, this._mapConfig.initialScale, 1);
        
        // 创建交互节点
        this.createInteractionNodes();
        
        // 设置初始位置（居中显示新手村）
        this.focusOnNode('start_village');
    }

    /**
     * 创建交互节点
     */
    private createInteractionNodes(): void {
        if (!this.interactionContainer) return;

        this._nodeData.forEach((nodeData, nodeId) => {
            this.createInteractionNode(nodeData);
        });

        console.log('🗺️ InteractiveMapPanel: 交互节点创建完成', this._interactionNodes.size);
    }

    /**
     * 创建单个交互节点
     */
    private createInteractionNode(nodeData: IInteractionNodeData): void {
        if (!this.interactionContainer) return;

        // 创建节点
        const nodeObj = new Node(nodeData.id);
        nodeObj.setParent(this.interactionContainer);
        nodeObj.setPosition(nodeData.worldPosition);

        // 添加UITransform组件
        const uiTransform = nodeObj.addComponent(UITransform);
        uiTransform.setContentSize(80, 80);

        // 添加InteractionNode组件
        const interactionNode = nodeObj.addComponent(InteractionNode);
        interactionNode.initialize(nodeData);

        // 绑定点击事件
        interactionNode.setOnClickCallback((clickedNodeData: IInteractionNodeData) => {
            this.onNodeClicked(clickedNodeData);
        });

        // 存储节点引用
        this._interactionNodes.set(nodeData.id, interactionNode);

        console.log('🗺️ InteractiveMapPanel: 创建交互节点', nodeData.name);
    }

    /**
     * 节点点击处理
     */
    private onNodeClicked(nodeData: IInteractionNodeData): void {
        console.log('🗺️ InteractiveMapPanel: 节点被点击', nodeData.name);

        if (!nodeData.unlocked) {
            console.warn('🗺️ InteractiveMapPanel: 节点未解锁', nodeData.name);
            this.showNodeLockedMessage(nodeData);
            return;
        }

        // 处理节点交互
        this.handleNodeInteraction(nodeData);

        // 检查并解锁新节点
        this.checkAndUnlockNodes(nodeData);

        // 发送事件
        EventManager.getInstance().emit('map_node_clicked', {
            nodeData: nodeData,
            nodeId: nodeData.id
        });
    }

    /**
     * 处理节点交互
     */
    private handleNodeInteraction(nodeData: IInteractionNodeData): void {
        switch (nodeData.type) {
            case 'action':
                this.handleActionNode(nodeData);
                break;
            case 'location':
                this.handleLocationNode(nodeData);
                break;
            case 'npc':
                this.handleNPCNode(nodeData);
                break;
            case 'quest':
                this.handleQuestNode(nodeData);
                break;
            case 'resource':
                this.handleResourceNode(nodeData);
                break;
        }
    }

    /**
     * 处理行动节点
     */
    private handleActionNode(nodeData: IInteractionNodeData): void {
        console.log('🎯 InteractiveMapPanel: 处理行动节点', nodeData.name);

        // 发送行动事件
        EventManager.getInstance().emit('action_node_activated', {
            actionType: nodeData.customData?.actionType || 'default',
            nodeData: nodeData
        });
    }

    /**
     * 处理位置节点
     */
    private handleLocationNode(nodeData: IInteractionNodeData): void {
        console.log('📍 InteractiveMapPanel: 处理位置节点', nodeData.name);

        // 发送位置事件
        EventManager.getInstance().emit('location_node_activated', {
            locationId: nodeData.id,
            nodeData: nodeData
        });
    }

    /**
     * 处理NPC节点
     */
    private handleNPCNode(nodeData: IInteractionNodeData): void {
        console.log('👤 InteractiveMapPanel: 处理NPC节点', nodeData.name);

        // 发送NPC交互事件
        EventManager.getInstance().emit('npc_node_activated', {
            npcId: nodeData.id,
            nodeData: nodeData
        });
    }

    /**
     * 处理任务节点
     */
    private handleQuestNode(nodeData: IInteractionNodeData): void {
        console.log('📜 InteractiveMapPanel: 处理任务节点', nodeData.name);

        // 发送任务事件
        EventManager.getInstance().emit('quest_node_activated', {
            questId: nodeData.id,
            nodeData: nodeData
        });
    }

    /**
     * 处理资源节点
     */
    private handleResourceNode(nodeData: IInteractionNodeData): void {
        console.log('💎 InteractiveMapPanel: 处理资源节点', nodeData.name);

        // 发送资源收集事件
        EventManager.getInstance().emit('resource_node_activated', {
            resourceType: nodeData.customData?.resourceType || 'generic',
            nodeData: nodeData
        });
    }

    /**
     * 检查并解锁新节点
     */
    private checkAndUnlockNodes(triggeredNode: IInteractionNodeData): void {
        triggeredNode.unlocks.forEach(unlockNodeId => {
            const nodeData = this._nodeData.get(unlockNodeId);
            if (nodeData && !nodeData.unlocked) {
                // 检查前置条件是否满足
                const prerequisitesMet = nodeData.prerequisites.every(prereqId => {
                    const prereqNode = this._nodeData.get(prereqId);
                    return prereqNode && prereqNode.unlocked;
                });

                if (prerequisitesMet) {
                    this.unlockNode(unlockNodeId);
                }
            }
        });
    }

    /**
     * 解锁节点
     */
    private unlockNode(nodeId: string): void {
        const nodeData = this._nodeData.get(nodeId);
        const interactionNode = this._interactionNodes.get(nodeId);

        if (nodeData && interactionNode) {
            nodeData.unlocked = true;
            interactionNode.setUnlocked(true);

            console.log('🔓 InteractiveMapPanel: 节点已解锁', nodeData.name);

            // 发送解锁事件
            EventManager.getInstance().emit('map_node_unlocked', {
                nodeId: nodeId,
                nodeData: nodeData
            });
        }
    }

    /**
     * 显示节点锁定消息
     */
    private showNodeLockedMessage(nodeData: IInteractionNodeData): void {
        console.log('🔒 InteractiveMapPanel: 节点被锁定', nodeData.name);

        // 这里可以显示提示UI
        EventManager.getInstance().emit('show_node_locked_message', {
            nodeData: nodeData,
            prerequisites: nodeData.prerequisites
        });
    }

    // ==================== 触摸事件处理 ====================

    /**
     * 触摸开始
     */
    private onTouchStart(event: EventTouch): void {
        if (!this.enableDrag || !this.mapContainer) return;

        this._isDragging = true;
        this._lastTouchPosition = event.getUILocation();
        this._dragStartPosition.set(this._lastTouchPosition);

        console.log('🗺️ InteractiveMapPanel: 开始拖拽');
    }

    /**
     * 触摸移动
     */
    private onTouchMove(event: EventTouch): void {
        if (!this._isDragging || !this.mapContainer) return;

        const currentPosition = event.getUILocation();
        const deltaPosition = new Vec3(
            currentPosition.x - this._lastTouchPosition.x,
            currentPosition.y - this._lastTouchPosition.y,
            0
        );

        // 应用阻尼
        deltaPosition.multiplyScalar(this.dragDamping);

        // 更新地图位置
        const currentMapPosition = this.mapContainer.getPosition();
        const newPosition = new Vec3(
            currentMapPosition.x + deltaPosition.x,
            currentMapPosition.y + deltaPosition.y,
            currentMapPosition.z
        );

        // 应用边界限制
        newPosition.x = Math.max(this._mapConfig.dragBounds.left,
                       Math.min(this._mapConfig.dragBounds.right, newPosition.x));
        newPosition.y = Math.max(this._mapConfig.dragBounds.bottom,
                       Math.min(this._mapConfig.dragBounds.top, newPosition.y));

        this.mapContainer.setPosition(newPosition);
        this._lastTouchPosition = currentPosition;
    }

    /**
     * 触摸结束
     */
    private onTouchEnd(event: EventTouch): void {
        if (!this._isDragging) return;

        this._isDragging = false;
        console.log('🗺️ InteractiveMapPanel: 结束拖拽');
    }

    // ==================== 公共API ====================

    /**
     * 聚焦到指定节点
     */
    public focusOnNode(nodeId: string): void {
        const nodeData = this._nodeData.get(nodeId);
        if (!nodeData || !this.mapContainer) return;

        // 计算目标位置（将节点移动到屏幕中心）
        const targetPosition = new Vec3(-nodeData.worldPosition.x, -nodeData.worldPosition.y, 0);

        // 应用边界限制
        targetPosition.x = Math.max(this._mapConfig.dragBounds.left,
                          Math.min(this._mapConfig.dragBounds.right, targetPosition.x));
        targetPosition.y = Math.max(this._mapConfig.dragBounds.bottom,
                          Math.min(this._mapConfig.dragBounds.top, targetPosition.y));

        this.mapContainer.setPosition(targetPosition);

        console.log('🗺️ InteractiveMapPanel: 聚焦到节点', nodeData.name);
    }

    /**
     * 设置地图缩放
     */
    public setMapScale(scale: number): void {
        if (!this.mapContainer) return;

        scale = Math.max(this._mapConfig.minScale, Math.min(this._mapConfig.maxScale, scale));
        this.mapContainer.setScale(scale, scale, 1);

        console.log('🗺️ InteractiveMapPanel: 设置地图缩放', scale);
    }

    /**
     * 获取当前地图缩放
     */
    public getMapScale(): number {
        return this.mapContainer ? this.mapContainer.getScale().x : 1.0;
    }

    /**
     * 添加交互节点
     */
    public addInteractionNode(nodeData: IInteractionNodeData): void {
        this._nodeData.set(nodeData.id, nodeData);
        this.createInteractionNode(nodeData);

        console.log('🗺️ InteractiveMapPanel: 添加交互节点', nodeData.name);
    }

    /**
     * 移除交互节点
     */
    public removeInteractionNode(nodeId: string): void {
        const interactionNode = this._interactionNodes.get(nodeId);
        if (interactionNode) {
            interactionNode.node.destroy();
            this._interactionNodes.delete(nodeId);
        }

        this._nodeData.delete(nodeId);

        console.log('🗺️ InteractiveMapPanel: 移除交互节点', nodeId);
    }

    /**
     * 获取节点数据
     */
    public getNodeData(nodeId: string): IInteractionNodeData | undefined {
        return this._nodeData.get(nodeId);
    }

    /**
     * 获取所有节点数据
     */
    public getAllNodeData(): Map<string, IInteractionNodeData> {
        return new Map(this._nodeData);
    }

    /**
     * 更新地图配置
     */
    public updateMapConfig(config: Partial<IMapConfig>): void {
        this._mapConfig = { ...this._mapConfig, ...config };
        console.log('🗺️ InteractiveMapPanel: 地图配置已更新', this._mapConfig);
    }

    /**
     * 显示面板
     */
    public showPanel(): void {
        this.node.active = true;
        console.log('🗺️ InteractiveMapPanel: 显示面板');
    }

    /**
     * 隐藏面板
     */
    public hidePanel(): void {
        this.node.active = false;
        console.log('🗺️ InteractiveMapPanel: 隐藏面板');
    }
}
