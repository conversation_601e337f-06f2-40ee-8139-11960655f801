{"extends": "./temp/tsconfig.cocos.json", "compilerOptions": {"strict": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "target": "ES2020", "module": "ES2020", "moduleResolution": "node", "baseUrl": "./", "paths": {"@/*": ["assets/client/scripts/*"], "@core/*": ["assets/client/scripts/core/*"], "@systems/*": ["assets/client/scripts/systems/*"], "@ui/*": ["assets/client/scripts/ui/*"], "@data/*": ["assets/client/scripts/data/*"], "@scenes/*": ["assets/client/scripts/scenes/*"], "@shared/*": ["shared/*"], "@shared/types/*": ["shared/types/*"], "@shared/configs/*": ["shared/configs/*"]}}, "include": ["assets/client/scripts/**/*.ts", "shared/**/*.ts"], "exclude": ["node_modules", "temp", "library", "build", "backend", "tools"]}