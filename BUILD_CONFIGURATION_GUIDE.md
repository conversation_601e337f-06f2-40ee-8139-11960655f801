# Cocos Creator 分包构建配置指南

## 概述

本文档详细说明了如何在Cocos Creator 3.8中配置微信小游戏的分包策略，实现方案A的混合分包架构。

## 分包架构设计

### 主包 (≤4M)
- 核心引擎框架 (~2.5M)
- 启动场景和基础UI (~1M)
- 底部菜单组件
- 核心管理器 (SceneManager, ConfigManager)

### 小游戏分包1 - 场景包 (scenes)
- Forest.scene (森林场景)
- Desert.scene (沙漠场景)
- Mountain.scene (山脉场景)
- Battle.scene (战斗场景)

### 小游戏分包2 - UI功能包 (ui-features)
- CharacterPanel.prefab (角色界面)
- EquipmentPanel.prefab (装备界面)
- 其他UI预制件

### 远程分包 - 资源包 (remote-resources)
- 大型贴图资源
- 音频文件
- 配置JSON文件

## 构建配置步骤

### 1. Asset Bundle配置

#### 1.1 场景分包配置
1. 在资源管理器中选择 `assets/bundles/scenes` 文件夹
2. 在属性检查器中勾选 "配置为 Bundle"
3. 配置参数：
   - Bundle名称: `scenes`
   - 目标平台: `微信小游戏`
   - 压缩类型: `小游戏分包`
   - 优先级: `1`
   - 配置为远程包: `否`

#### 1.2 UI功能分包配置
1. 选择 `assets/bundles/ui-features` 文件夹
2. 配置参数：
   - Bundle名称: `ui-features`
   - 目标平台: `微信小游戏`
   - 压缩类型: `小游戏分包`
   - 优先级: `2`
   - 配置为远程包: `否`

#### 1.3 远程资源分包配置
1. 选择 `assets/bundles/remote-resources` 文件夹
2. 配置参数：
   - Bundle名称: `remote-resources`
   - 目标平台: `微信小游戏`
   - 压缩类型: `无压缩`
   - 优先级: `3`
   - 配置为远程包: `是`

### 2. 构建发布配置

#### 2.1 基本设置
1. 打开 `项目 -> 构建发布`
2. 选择发布平台: `微信小游戏`
3. 配置参数：
   - 游戏名称: `IdleGame`
   - 游戏包名: `com.yourcompany.idlegame`
   - 游戏版本: `1.0.0`
   - 主包压缩类型: `小游戏分包`

#### 2.2 分包设置
1. 在构建面板中找到 "分包配置" 选项
2. 确认以下分包已正确识别：
   - `scenes` (小游戏分包)
   - `ui-features` (小游戏分包)
   - `remote-resources` (远程分包)

#### 2.3 优化设置
1. 启用以下优化选项：
   - MD5缓存: `开启`
   - 合并图集: `开启`
   - 压缩纹理: `开启`
   - 内联所有SpriteFrame: `关闭`
   - 合并初始场景依赖的所有JSON: `开启`

### 3. 微信开发者工具配置

#### 3.1 项目配置
在构建完成后，使用微信开发者工具打开项目：

1. 打开 `build/wechatgame` 目录
2. 检查 `game.json` 文件中的分包配置：

```json
{
  "deviceOrientation": "portrait",
  "showStatusBar": false,
  "networkTimeout": {
    "request": 60000,
    "connectSocket": 60000,
    "uploadFile": 60000,
    "downloadFile": 60000
  },
  "subpackages": [
    {
      "name": "scenes",
      "root": "subpackages/scenes/"
    },
    {
      "name": "ui-features", 
      "root": "subpackages/ui-features/"
    }
  ]
}
```

#### 3.2 分包大小检查
1. 在微信开发者工具中查看 "详情 -> 基本信息"
2. 确认分包大小符合限制：
   - 主包: ≤ 4MB
   - 总包大小: ≤ 30MB (开通虚拟支付)
   - 单个分包: 无限制

### 4. 远程资源配置

#### 4.1 CDN设置
1. 将 `remote-resources` 分包上传到CDN
2. 在 `assets/bundles/remote-resources/remote-config.json` 中配置CDN地址：

```json
{
  "cdn": {
    "baseUrl": "https://your-cdn-domain.com/idlegame/",
    "fallbackUrl": "https://backup-cdn-domain.com/idlegame/",
    "version": "v1.0.0"
  }
}
```

#### 4.2 远程资源加载测试
在代码中测试远程资源加载：

```typescript
// 加载远程分包
assetManager.loadBundle('remote-resources', (err, bundle) => {
    if (err) {
        console.error('远程分包加载失败:', err);
        return;
    }
    console.log('远程分包加载成功');
});
```

## 性能优化建议

### 1. 分包加载策略
- 主包只包含启动必需的资源
- 按需加载分包，避免一次性加载所有内容
- 实现分包预加载机制

### 2. 资源优化
- 压缩纹理格式选择合适的压缩比
- 音频文件使用适当的压缩格式
- 移除未使用的资源

### 3. 代码优化
- 使用Tree Shaking移除未使用的代码
- 合理使用动态导入
- 优化脚本执行顺序

## 测试验证

### 1. 分包加载测试
使用以下代码测试分包加载：

```typescript
// 测试场景分包加载
const sceneManager = SceneManager.getInstance();
await sceneManager.switchScene('Forest');
```

### 2. 性能监控
监控以下指标：
- 首屏加载时间
- 分包加载时间
- 内存使用情况
- 网络请求耗时

### 3. 兼容性测试
在不同设备上测试：
- 低端Android设备
- iOS设备
- 不同网络环境

## 常见问题解决

### 1. 分包加载失败
- 检查Bundle配置是否正确
- 确认网络连接正常
- 验证CDN地址可访问

### 2. 主包超出4MB限制
- 移除非必需资源到分包
- 优化纹理压缩
- 检查重复资源

### 3. 场景切换卡顿
- 实现分包预加载
- 优化场景资源大小
- 使用加载进度提示

## 部署清单

构建完成后，确认以下文件结构：

```
build/wechatgame/
├── game.js                 # 主包代码
├── game.json              # 游戏配置
├── project.config.json    # 项目配置
├── subpackages/           # 分包目录
│   ├── scenes/           # 场景分包
│   └── ui-features/      # UI功能分包
└── remote/               # 远程资源（需上传CDN）
```

完成以上配置后，您的挂机放置类小程序游戏将具备完整的分包加载能力，支持云函数配置数据管理，实现最优的用户体验和性能表现。
