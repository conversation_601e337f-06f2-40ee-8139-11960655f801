# IdleGame 后端快速参考卡片

> **状态**: ✅ 完全可用 | **端口**: 3002 | **更新**: 2025-08-10

## 🚀 快速启动

```bash
cd backend
npm run dev
```

## 🌐 关键地址

| 服务 | 地址 | 说明 |
|------|------|------|
| 主服务 | http://localhost:3002 | 后端API服务 |
| 健康检查 | http://localhost:3002/health | 服务状态检查 |
| API文档 | http://localhost:3002/api/docs | Swagger文档 |
| API信息 | http://localhost:3002/api/info | 基本信息 |

## 📊 服务状态

| 组件 | 状态 | 配置 |
|------|------|------|
| Node.js | ✅ 运行中 | TypeScript + Express |
| MongoDB | ✅ 已连接 | localhost:27017/idlegame_dev |
| Redis | ✅ 已连接 | 内存实现 (开发环境) |
| 路由系统 | ✅ 正常 | 9个路由已启用 |

## 🔧 常用命令

```bash
# 开发相关
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run test         # 运行测试

# 环境管理
npm run env:local    # 切换本地环境
npm run env:status   # 查看环境状态

# 数据库
npm run db:migrate   # 数据库迁移
npm run db:seed      # 数据填充
```

## 📁 核心目录

```
backend/src/
├── server.ts        # 主入口
├── controllers/     # 控制器
├── models/         # 数据模型
├── routes/         # 路由定义
├── services/       # 业务服务
├── middleware/     # 中间件
└── utils/          # 工具类
```

## 🔌 主要API端点

### 认证相关
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/logout` - 用户登出

### 用户管理
- `GET /api/v1/users/profile` - 获取用户资料
- `PUT /api/v1/users/profile` - 更新用户资料
- `GET /api/v1/users/list` - 获取用户列表

### 角色系统
- `POST /api/v1/characters` - 创建角色
- `GET /api/v1/characters/:id` - 获取角色详情
- `PUT /api/v1/characters/:id` - 更新角色信息

### 物品系统
- `GET /api/v1/items/list` - 获取物品列表
- `POST /api/v1/items/equip` - 装备物品
- `POST /api/v1/items/unequip` - 卸下装备

### 地点系统
- `GET /api/v1/locations/list` - 获取地点列表
- `GET /api/v1/locations/config/:id` - 获取地点配置

## 🔒 认证说明

### JWT Token
- **获取**: 通过 `/api/v1/auth/login` 登录获取
- **使用**: 请求头添加 `Authorization: Bearer <token>`
- **过期**: 7天 (可配置)

### 权限级别
- `user` - 普通用户
- `admin` - 管理员
- `moderator` - 版主

## 🐛 故障排除

### 常见问题

**服务无法启动**
```bash
# 检查端口占用
netstat -ano | findstr :3002
# 或更换端口
PORT=3003 npm run dev
```

**数据库连接失败**
```bash
# 检查MongoDB服务
mongod --version
# 启动MongoDB服务
net start MongoDB
```

**TypeScript编译错误**
```bash
# 检查编译错误
npm run build
# 清理并重新安装
rm -rf node_modules package-lock.json
npm install
```

### 日志查看
```bash
# 实时查看日志
tail -f logs/combined.log

# 查看错误日志
tail -f logs/error.log

# 查看业务日志
tail -f logs/business.log
```

## 📞 联系方式

- **开发团队**: 爱豆 (Idle Team)
- **技术栈**: TypeScript + Express + MongoDB + Redis
- **文档位置**: `/Reports/backend-server-status-report.md`

## ⚡ 性能指标

| 指标 | 当前值 | 目标值 |
|------|--------|--------|
| 启动时间 | ~3秒 | <5秒 |
| 内存使用 | ~150MB | <200MB |
| 响应时间 | <100ms | <200ms |
| 并发连接 | 1000+ | 5000+ |

## 🔄 最近更新

**2025-08-10**
- ✅ 修复413个TypeScript编译错误
- ✅ 服务器端口调整为3002
- ✅ 清理临时JS版本文件
- ✅ 完善API文档和监控

---

**快速参考版本**: v1.0.0  
**最后更新**: 2025-08-10 00:25  
**状态**: ✅ 服务正常运行
