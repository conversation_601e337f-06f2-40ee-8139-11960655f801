// Copyright (c) 2025 "放置". All rights reserved.
import dotenv = require('dotenv');
import { Logger } from '../utils/logger';

// 加载环境变量
dotenv.config();

/**
 * 部署环境枚举
 */
export enum DeploymentEnvironment {
    LOCAL = 'local',
    REMOTE = 'remote', 
    PRODUCTION = 'production'
}

/**
 * 环境配置接口
 */
export interface IEnvironmentConfig {
    // 基础配置
    nodeEnv: string;
    port: number;
    host: string;
    deploymentEnv: DeploymentEnvironment;
    
    // API配置
    apiBaseUrl: string;
    corsOrigin: string;
    
    // 数据库配置
    mongodbUri: string;
    redisConfig: {
        host: string;
        port: number;
        password?: string;
        db: number;
    };
    
    // 安全配置
    jwtSecret: string;
    jwtExpiresIn: string;
    
    // 游戏配置
    locationConfigPath: string;
    locationConfigCacheTtl: number;
    enableCacheRefresh: boolean;
    
    // 监控配置
    enableMetrics: boolean;
    logLevel: string;
}

/**
 * 环境配置管理器
 */
export class EnvironmentConfig {
    private static instance: EnvironmentConfig;
    private config: IEnvironmentConfig;

    private constructor() {
        this.config = this.loadConfiguration();
        this.validateConfiguration();
    }

    public static getInstance(): EnvironmentConfig {
        if (!EnvironmentConfig.instance) {
            EnvironmentConfig.instance = new EnvironmentConfig();
        }
        return EnvironmentConfig.instance;
    }

    /**
     * 加载配置
     */
    private loadConfiguration(): IEnvironmentConfig {
        const deploymentEnv = this.getDeploymentEnvironment();
        
        return {
            // 基础配置
            nodeEnv: process.env['NODE_ENV'] || 'development',
            port: parseInt(process.env['PORT'] || '3000', 10),
            host: process.env['HOST'] || 'localhost',
            deploymentEnv,
            
            // API配置
            apiBaseUrl: this.getApiBaseUrl(deploymentEnv),
            corsOrigin: this.getCorsOrigin(deploymentEnv),
            
            // 数据库配置
            mongodbUri: this.getMongodbUri(deploymentEnv),
            redisConfig: this.getRedisConfig(deploymentEnv),
            
            // 安全配置
            jwtSecret: process.env['JWT_SECRET'] || 'default-jwt-secret-change-in-production',
            jwtExpiresIn: process.env['JWT_EXPIRES_IN'] || '7d',
            
            // 游戏配置
            locationConfigPath: process.env['LOCATION_CONFIG_PATH'] || 'assets/bundles/locations',
            locationConfigCacheTtl: parseInt(process.env['LOCATION_CONFIG_CACHE_TTL'] || '3600', 10),
            enableCacheRefresh: process.env['ENABLE_CACHE_REFRESH'] === 'true',
            
            // 监控配置
            enableMetrics: process.env['ENABLE_METRICS'] === 'true',
            logLevel: process.env['LOG_LEVEL'] || 'info'
        };
    }

    /**
     * 获取部署环境
     */
    private getDeploymentEnvironment(): DeploymentEnvironment {
        const env = process.env['DEPLOYMENT_ENV'] || 'local';
        
        switch (env.toLowerCase()) {
            case 'remote':
                return DeploymentEnvironment.REMOTE;
            case 'production':
                return DeploymentEnvironment.PRODUCTION;
            default:
                return DeploymentEnvironment.LOCAL;
        }
    }

    /**
     * 获取API基础URL
     */
    private getApiBaseUrl(deploymentEnv: DeploymentEnvironment): string {
        switch (deploymentEnv) {
            case DeploymentEnvironment.PRODUCTION:
            case DeploymentEnvironment.REMOTE:
                return process.env['API_BASE_URL_PROD'] || 'https://your-aliyun-server.com';
            default:
                return process.env['API_BASE_URL'] || 'http://localhost:3000';
        }
    }

    /**
     * 获取CORS配置
     */
    private getCorsOrigin(deploymentEnv: DeploymentEnvironment): string {
        switch (deploymentEnv) {
            case DeploymentEnvironment.PRODUCTION:
                return process.env['CORS_ORIGIN_PROD'] || 'https://your-frontend-domain.com';
            case DeploymentEnvironment.REMOTE:
                return process.env['CORS_ORIGIN'] || '*';
            default:
                return '*'; // 本地开发允许所有来源
        }
    }

    /**
     * 获取MongoDB URI
     */
    private getMongodbUri(deploymentEnv: DeploymentEnvironment): string {
        switch (deploymentEnv) {
            case DeploymentEnvironment.PRODUCTION:
            case DeploymentEnvironment.REMOTE:
                return process.env['MONGODB_URI_PROD'] || process.env['MONGODB_URI'] || 'mongodb://localhost:27017/idlegame_prod';
            default:
                return process.env['MONGODB_URI'] || 'mongodb://localhost:27017/idlegame_dev';
        }
    }

    /**
     * 获取Redis配置
     */
    private getRedisConfig(deploymentEnv: DeploymentEnvironment): any {
        switch (deploymentEnv) {
            case DeploymentEnvironment.PRODUCTION:
            case DeploymentEnvironment.REMOTE:
                return {
                    host: process.env['REDIS_HOST_PROD'] || process.env['REDIS_HOST'] || 'localhost',
                    port: parseInt(process.env['REDIS_PORT_PROD'] || process.env['REDIS_PORT'] || '6379', 10),
                    password: process.env['REDIS_PASSWORD_PROD'] || process.env['REDIS_PASSWORD'] || undefined,
                    db: parseInt(process.env['REDIS_DB_PROD'] || process.env['REDIS_DB'] || '0', 10)
                };
            default:
                return {
                    host: process.env['REDIS_HOST'] || 'localhost',
                    port: parseInt(process.env['REDIS_PORT'] || '6379', 10),
                    password: process.env['REDIS_PASSWORD'] || undefined,
                    db: parseInt(process.env['REDIS_DB'] || '0', 10)
                };
        }
    }

    /**
     * 验证配置
     */
    private validateConfiguration(): void {
        const errors: string[] = [];

        // 验证必需的配置
        if (!this.config.jwtSecret || this.config.jwtSecret === 'default-jwt-secret-change-in-production') {
            if (this.config.deploymentEnv !== DeploymentEnvironment.LOCAL) {
                errors.push('JWT_SECRET 必须在生产环境中设置');
            }
        }

        if (!this.config.mongodbUri) {
            errors.push('MONGODB_URI 必须设置');
        }

        if (this.config.port < 1 || this.config.port > 65535) {
            errors.push('PORT 必须在 1-65535 范围内');
        }

        // 验证URL格式
        if (this.config.deploymentEnv !== DeploymentEnvironment.LOCAL) {
            try {
                new URL(this.config.apiBaseUrl);
            } catch {
                errors.push('API_BASE_URL 格式不正确');
            }
        }

        if (errors.length > 0) {
            Logger.error('环境配置验证失败', { errors });
            throw new Error(`环境配置错误: ${errors.join(', ')}`);
        }

        Logger.info('环境配置验证通过', {
            deploymentEnv: this.config.deploymentEnv,
            nodeEnv: this.config.nodeEnv,
            port: this.config.port
        });
    }

    /**
     * 获取配置
     */
    public getConfig(): IEnvironmentConfig {
        return { ...this.config };
    }

    /**
     * 获取特定配置项
     */
    public get<K extends keyof IEnvironmentConfig>(key: K): IEnvironmentConfig[K] {
        return this.config[key];
    }

    /**
     * 是否为本地环境
     */
    public isLocal(): boolean {
        return this.config.deploymentEnv === DeploymentEnvironment.LOCAL;
    }

    /**
     * 是否为远程环境
     */
    public isRemote(): boolean {
        return this.config.deploymentEnv === DeploymentEnvironment.REMOTE;
    }

    /**
     * 是否为生产环境
     */
    public isProduction(): boolean {
        return this.config.deploymentEnv === DeploymentEnvironment.PRODUCTION;
    }

    /**
     * 获取环境信息摘要
     */
    public getEnvironmentSummary(): any {
        return {
            deploymentEnv: this.config.deploymentEnv,
            nodeEnv: this.config.nodeEnv,
            port: this.config.port,
            apiBaseUrl: this.config.apiBaseUrl,
            corsOrigin: this.config.corsOrigin,
            mongodbUri: this.config.mongodbUri.replace(/\/\/.*@/, '//***:***@'), // 隐藏密码
            redisHost: this.config.redisConfig.host,
            redisPort: this.config.redisConfig.port,
            enableMetrics: this.config.enableMetrics,
            logLevel: this.config.logLevel
        };
    }

    /**
     * 打印环境配置信息
     */
    public printEnvironmentInfo(): void {
        const summary = this.getEnvironmentSummary();
        
        Logger.info('🌐 ========== 环境配置信息 ==========');
        Logger.info(`📋 部署环境: ${summary.deploymentEnv}`);
        Logger.info(`🔧 Node环境: ${summary.nodeEnv}`);
        Logger.info(`🚀 服务端口: ${summary.port}`);
        Logger.info(`🌍 API地址: ${summary.apiBaseUrl}`);
        Logger.info(`🔒 CORS配置: ${summary.corsOrigin}`);
        Logger.info(`💾 数据库: ${summary.mongodbUri}`);
        Logger.info(`⚡ Redis: ${summary.redisHost}:${summary.redisPort}`);
        Logger.info(`📊 监控: ${summary.enableMetrics ? '启用' : '禁用'}`);
        Logger.info(`📝 日志级别: ${summary.logLevel}`);
        Logger.info('🌐 ===================================');
    }
}

// 导出单例实例
export const envConfig = EnvironmentConfig.getInstance();
