/**
 * Copyright (c) 2025 "放置". All rights reserved.
 * 场景切换协议 (Scene Switch Protocol - SSP) v1.0
 * 类型定义和接口规范
 */

// ==================== 基础协议结构 ====================

/**
 * SSP协议基础消息格式
 */
export interface SSPMessage<T = any> {
  protocol: "SSP";                    // 协议标识
  version: string;                    // 协议版本
  messageId: string;                  // 消息唯一ID
  timestamp: number;                  // 时间戳
  type: SSPMessageType;               // 消息类型
  payload: T;                         // 消息载荷
  metadata?: SSPMetadata;             // 元数据
}

/**
 * SSP协议元数据
 */
export interface SSPMetadata {
  userId?: string;                    // 用户ID
  sessionId?: string;                 // 会话ID
  clientVersion?: string;             // 客户端版本
  platform?: string;                 // 平台信息
  retry?: number;                     // 重试次数
  correlationId?: string;             // 关联ID
}

/**
 * SSP消息类型枚举
 */
export enum SSPMessageType {
  // 请求类型
  SCENE_DATA_REQUEST = "scene_data_request",
  SCENE_SWITCH_REQUEST = "scene_switch_request",
  SCENE_CONFIG_REQUEST = "scene_config_request",
  SCENE_LIST_REQUEST = "scene_list_request",
  
  // 响应类型
  SCENE_DATA_RESPONSE = "scene_data_response",
  SCENE_SWITCH_RESPONSE = "scene_switch_response",
  SCENE_CONFIG_RESPONSE = "scene_config_response",
  SCENE_LIST_RESPONSE = "scene_list_response",
  
  // 事件类型
  SCENE_SWITCH_EVENT = "scene_switch_event",
  SCENE_UPDATE_EVENT = "scene_update_event",
  SCENE_UNLOCK_EVENT = "scene_unlock_event",
  
  // 错误类型
  ERROR_RESPONSE = "error_response"
}

// ==================== 场景数据请求协议 ====================

/**
 * 缓存策略枚举
 */
export enum CachePolicy {
  USE_CACHE = "use_cache",             // 优先使用缓存
  FORCE_REFRESH = "force_refresh",     // 强制刷新
  CACHE_FIRST = "cache_first",         // 缓存优先
  SERVER_FIRST = "server_first"        // 服务器优先
}

/**
 * 场景数据请求
 */
export interface SceneDataRequest {
  sceneId: string;                     // 场景ID
  version?: string;                    // 请求的配置版本
  includeServerData: boolean;          // 是否包含服务器数据
  includeXMLConfig: boolean;           // 是否包含XML配置
  cachePolicy?: CachePolicy;           // 缓存策略
  fields?: string[];                   // 请求的字段列表
}

/**
 * 场景数据响应
 */
export interface SceneDataResponse {
  sceneId: string;                     // 场景ID
  version: string;                     // 配置版本
  data: {
    xmlConfig?: XMLSceneConfig;        // XML配置数据
    serverData?: ServerSceneData;      // 服务器数据
    combinedConfig: CombinedSceneConfig; // 合并后的配置
  };
  metadata: {
    dataSource: "cache" | "server" | "hybrid";
    lastUpdated: number;
    cacheExpiry?: number;
    hasXMLConfig: boolean;
    hasServerData: boolean;
    loadTime: number;                  // 加载耗时
  };
}

// ==================== 场景切换请求协议 ====================

/**
 * 切换类型枚举
 */
export enum SwitchType {
  NORMAL = "normal",                   // 普通切换
  FAST = "fast",                      // 快速切换
  PRELOAD = "preload",                // 预加载切换
  INSTANT = "instant"                 // 即时切换
}

/**
 * 过渡配置
 */
export interface TransitionConfig {
  type: "fade" | "slide" | "zoom" | "none";
  duration: number;                    // 持续时间（毫秒）
  easing?: string;                     // 缓动函数
  direction?: "left" | "right" | "up" | "down";
}

/**
 * 场景切换请求
 */
export interface SceneSwitchRequest {
  fromSceneId?: string;                // 当前场景ID
  toSceneId: string;                   // 目标场景ID
  switchType: SwitchType;              // 切换类型
  playerData?: PlayerData;             // 玩家数据
  transitionConfig?: TransitionConfig; // 过渡配置
  validateUnlock?: boolean;            // 是否验证解锁条件
}

/**
 * 场景切换响应
 */
export interface SceneSwitchResponse {
  success: boolean;                    // 是否成功
  sceneId: string;                     // 场景ID
  switchId: string;                    // 切换操作ID
  sceneData: SceneDataResponse;        // 场景数据
  playerState?: PlayerState;           // 更新后的玩家状态
  unlockStatus?: UnlockStatus;         // 解锁状态
  timing: {
    requestTime: number;               // 请求时间
    processTime: number;               // 处理时间
    totalTime: number;                 // 总时间
  };
  nextRecommendedScenes?: string[];    // 推荐的下一个场景
}

// ==================== 错误处理协议 ====================

/**
 * SSP错误码枚举
 */
export enum SSPErrorCode {
  // 客户端错误 (4xxx)
  INVALID_REQUEST = "SSP_4001",
  INVALID_SCENE_ID = "SSP_4002",
  SCENE_LOCKED = "SSP_4003",
  SCENE_NOT_FOUND = "SSP_4004",
  INVALID_PARAMETERS = "SSP_4005",
  
  // 服务器错误 (5xxx)
  SERVER_ERROR = "SSP_5001",
  CONFIG_LOAD_FAILED = "SSP_5002",
  DATABASE_ERROR = "SSP_5003",
  CACHE_ERROR = "SSP_5004",
  TIMEOUT_ERROR = "SSP_5005",
  
  // 业务错误 (6xxx)
  PLAYER_LEVEL_TOO_LOW = "SSP_6001",
  MISSING_REQUIRED_ITEMS = "SSP_6002",
  QUEST_NOT_COMPLETED = "SSP_6003",
  INSUFFICIENT_ENERGY = "SSP_6004",
  SCENE_COOLDOWN = "SSP_6005"
}

/**
 * SSP错误响应
 */
export interface SSPErrorResponse {
  errorCode: SSPErrorCode;             // 错误码
  errorMessage: string;                // 错误消息
  errorDetails?: any;                  // 错误详情
  retryable: boolean;                  // 是否可重试
  retryAfter?: number;                 // 重试延迟（毫秒）
  suggestedAction?: string;            // 建议的处理方式
  timestamp: number;                   // 错误发生时间
}

// ==================== 数据结构定义 ====================

/**
 * XML场景配置
 */
export interface XMLSceneConfig {
  sceneId: string;
  basicInfo: {
    name: string;
    description: string;
    type: string;
    level: number;
  };
  unlockConditions: {
    playerLevel: number;
    requiredItems: string[];
    completedQuests: string[];
    requiredAreas: string[];
  };
  mapConfig: {
    backgroundImagePath: string;
    mapSize: { width: number; height: number };
    nodeSpacing: { x: number; y: number };
    startPosition: { x: number; y: number };
    branchCount: number;
    nodesPerBranch: number;
  };
  environment: {
    weather: string;
    timeOfDay: string;
    backgroundMusic: string;
    ambientSounds: string[];
    lightingColor: { r: number; g: number; b: number; a: number };
  };
  behaviors: Record<string, BehaviorConfig>;
  uiLayout: {
    behaviorButtonsPosition: { x: number; y: number };
    infoDisplayPosition: { x: number; y: number };
    customElements: CustomElement[];
  };
}

/**
 * 服务器场景数据
 */
export interface ServerSceneData {
  sceneId: string;
  playerProgress?: {
    visitCount: number;
    lastVisitTime: number;
    completedBehaviors: string[];
    unlockedFeatures: string[];
  };
  dynamicConfig?: {
    eventMultipliers: Record<string, number>;
    temporaryUnlocks: string[];
    specialOffers: any[];
  };
  serverState?: {
    playerCount: number;
    serverLoad: number;
    maintenanceMode: boolean;
  };
}

/**
 * 合并后的场景配置
 */
export interface CombinedSceneConfig extends XMLSceneConfig {
  serverData: ServerSceneData;
  computed: {
    isUnlocked: boolean;
    canAccess: boolean;
    recommendedLevel: number;
    estimatedRewards: any;
  };
}

/**
 * 行为配置
 */
export interface BehaviorConfig {
  behaviorId: string;
  name: string;
  description: string;
  type: string;
  duration: number;
  cooldown: number;
  energyCost: number;
  requirements: {
    items: string[];
    skills: string[];
    level: number;
  };
  rewards: {
    baseExp: number;
    items: ItemReward[];
    currency: CurrencyReward;
  };
}

/**
 * 物品奖励
 */
export interface ItemReward {
  id: string;
  probability: number;
  minQuantity: number;
  maxQuantity: number;
}

/**
 * 货币奖励
 */
export interface CurrencyReward {
  gold?: { min: number; max: number };
  gems?: { min: number; max: number };
}

/**
 * 自定义元素
 */
export interface CustomElement {
  type: string;
  position: { x: number; y: number };
  config: Record<string, any>;
}

/**
 * 玩家数据
 */
export interface PlayerData {
  playerId: string;
  level: number;
  exp: number;
  gold: number;
  energy: number;
  currentSceneId?: string;
  inventory: Record<string, number>;
  completedQuests: string[];
  unlockedScenes: string[];
}

/**
 * 玩家状态
 */
export interface PlayerState {
  currentSceneId: string;
  position?: { x: number; y: number };
  lastAction?: string;
  actionEndTime?: number;
  buffs: string[];
  debuffs: string[];
}

/**
 * 解锁状态
 */
export interface UnlockStatus {
  sceneId: string;
  isUnlocked: boolean;
  unlockProgress: number;
  missingRequirements: {
    level?: number;
    items?: string[];
    quests?: string[];
    scenes?: string[];
  };
}

// ==================== 协议版本信息 ====================

/**
 * SSP版本信息
 */
export interface SSPVersionInfo {
  current: string;                     // 当前版本
  supported: string[];                 // 支持的版本列表
  deprecated: string[];                // 已弃用的版本
  breaking: string[];                  // 破坏性变更版本
}

/**
 * SSP扩展接口
 */
export interface SSPExtension {
  name: string;                        // 扩展名称
  version: string;                     // 扩展版本
  data: any;                          // 扩展数据
}

// ==================== 协议常量 ====================

/**
 * SSP协议常量
 */
export const SSP_CONSTANTS = {
  PROTOCOL_NAME: "SSP" as const,
  CURRENT_VERSION: "1.0" as const,
  MAX_MESSAGE_SIZE: 1024 * 1024,      // 1MB
  DEFAULT_TIMEOUT: 30000,              // 30秒
  MAX_RETRY_COUNT: 3,
  CACHE_TTL: 300000,                   // 5分钟
  HEARTBEAT_INTERVAL: 30000            // 30秒
} as const;

/**
 * 协议工具函数
 */
export class SSPUtils {
  /**
   * 生成消息ID
   */
  static generateMessageId(): string {
    return `ssp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 创建SSP消息
   */
  static createMessage<T>(
    type: SSPMessageType,
    payload: T,
    metadata?: SSPMetadata
  ): SSPMessage<T> {
    return {
      protocol: "SSP",
      version: SSP_CONSTANTS.CURRENT_VERSION,
      messageId: this.generateMessageId(),
      timestamp: Date.now(),
      type,
      payload,
      metadata
    };
  }

  /**
   * 验证消息格式
   */
  static validateMessage(message: any): message is SSPMessage {
    return (
      message &&
      message.protocol === "SSP" &&
      typeof message.version === "string" &&
      typeof message.messageId === "string" &&
      typeof message.timestamp === "number" &&
      typeof message.type === "string" &&
      message.payload !== undefined
    );
  }

  /**
   * 创建错误响应
   */
  static createErrorResponse(
    errorCode: SSPErrorCode,
    errorMessage: string,
    errorDetails?: any,
    retryable: boolean = false
  ): SSPErrorResponse {
    return {
      errorCode,
      errorMessage,
      errorDetails,
      retryable,
      timestamp: Date.now()
    };
  }
}
