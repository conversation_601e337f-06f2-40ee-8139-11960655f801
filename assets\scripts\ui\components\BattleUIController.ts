import { _decorator, Component, Node, Label, find } from 'cc';
import { CharacterUnit } from './CharacterUnit';
const { ccclass, property } = _decorator;

/**
 * 战斗UI控制器
 * 负责管理战斗场景的UI更新和角色单元管理
 */
@ccclass('BattleUIController')
export class BattleUIController extends Component {
    
    @property(Label)
    roundLabel: Label = null!;
    
    @property(Label)
    timeLabel: Label = null!;
    
    @property(Node)
    playerArea: Node = null!;
    
    @property(Node)
    enemyArea: Node = null!;
    
    // 战斗状态
    private battleActive: boolean = false;
    private battleTime: number = 0;
    private currentRound: number = 1;
    
    // 角色单元数组
    private playerUnits: CharacterUnit[] = [];
    private enemyUnits: CharacterUnit[] = [];
    
    start() {
        this.initializeBattle();
        this.autoFindUIComponents();
    }
    
    update(deltaTime: number) {
        if (this.battleActive) {
            this.battleTime += deltaTime;
            this.updateTimeDisplay();
            this.updateCharacterUnits(deltaTime);
        }
    }
    
    /**
     * 自动查找UI组件
     */
    private autoFindUIComponents() {
        // 查找回合标签
        if (!this.roundLabel) {
            const roundNode = find('Canvas/TopInfoPanel/RoundLabel');
            if (roundNode) {
                this.roundLabel = roundNode.getComponent(Label);
            }
        }
        
        // 查找时间标签
        if (!this.timeLabel) {
            const timeNode = find('Canvas/TopInfoPanel/TimeLabel');
            if (timeNode) {
                this.timeLabel = timeNode.getComponent(Label);
            }
        }
        
        // 查找玩家区域
        if (!this.playerArea) {
            this.playerArea = find('Canvas/PlayerArea');
        }
        
        // 查找敌人区域
        if (!this.enemyArea) {
            this.enemyArea = find('Canvas/EnemyArea');
        }
    }
    
    /**
     * 初始化战斗
     */
    private initializeBattle() {
        this.battleActive = true;
        this.battleTime = 0;
        this.currentRound = 1;
        
        // 更新UI显示
        this.updateRoundDisplay();
        this.updateTimeDisplay();
        
        // 延迟收集角色单元，确保所有组件都已初始化
        this.scheduleOnce(() => {
            this.collectCharacterUnits();
            this.setupCharacterData();
        }, 0.1);
        
        console.log('⚔️ 战斗初始化完成');
    }
    
    /**
     * 收集场景中的角色单元
     */
    private collectCharacterUnits() {
        this.playerUnits = [];
        this.enemyUnits = [];
        
        // 收集玩家单元
        if (this.playerArea) {
            const playerNodes = this.playerArea.children;
            for (let node of playerNodes) {
                const unit = node.getComponent(CharacterUnit);
                if (unit) {
                    this.playerUnits.push(unit);
                    unit.setTeam('player');
                }
            }
        }
        
        // 收集敌人单元
        if (this.enemyArea) {
            const enemyNodes = this.enemyArea.children;
            for (let node of enemyNodes) {
                const unit = node.getComponent(CharacterUnit);
                if (unit) {
                    this.enemyUnits.push(unit);
                    unit.setTeam('enemy');
                }
            }
        }
        
        console.log(`📊 收集到 ${this.playerUnits.length} 个玩家单元，${this.enemyUnits.length} 个敌人单元`);
    }
    
    /**
     * 设置角色数据
     */
    private setupCharacterData() {
        // 设置玩家数据
        for (let i = 0; i < this.playerUnits.length; i++) {
            const unit = this.playerUnits[i];
            unit.setCharacterData('战士', 210, 20);
        }
        
        // 设置敌人数据
        const enemyNames = ['哥布林', '兽人', '哥布林'];
        const enemyHPs = [60, 500, 60];
        const enemyMPs = [20, 290, 20];
        
        for (let i = 0; i < this.enemyUnits.length; i++) {
            const unit = this.enemyUnits[i];
            const nameIndex = i % enemyNames.length;
            unit.setCharacterData(
                enemyNames[nameIndex], 
                enemyHPs[nameIndex], 
                enemyMPs[nameIndex]
            );
        }
    }
    
    /**
     * 更新回合显示
     */
    private updateRoundDisplay() {
        if (this.roundLabel) {
            this.roundLabel.string = `回合: ${this.currentRound}`;
        }
    }
    
    /**
     * 更新时间显示
     */
    private updateTimeDisplay() {
        if (this.timeLabel) {
            const minutes = Math.floor(this.battleTime / 60);
            const seconds = Math.floor(this.battleTime % 60);
            this.timeLabel.string = `时间: ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }
    
    /**
     * 更新角色单元
     */
    private updateCharacterUnits(deltaTime: number) {
        // 更新玩家单元
        for (let unit of this.playerUnits) {
            unit.updateUnit(deltaTime);
        }
        
        // 更新敌人单元
        for (let unit of this.enemyUnits) {
            unit.updateUnit(deltaTime);
        }
    }
    
    /**
     * 下一回合
     */
    public nextRound() {
        this.currentRound++;
        this.updateRoundDisplay();
        console.log(`🔄 进入第 ${this.currentRound} 回合`);
    }
    
    /**
     * 结束战斗
     */
    public endBattle(victory: boolean) {
        this.battleActive = false;
        
        if (victory) {
            console.log('🎉 战斗胜利！');
        } else {
            console.log('💀 战斗失败');
        }
    }
    
    /**
     * 获取战斗状态
     */
    public getBattleStatus() {
        return {
            active: this.battleActive,
            time: this.battleTime,
            round: this.currentRound,
            playerCount: this.playerUnits.length,
            enemyCount: this.enemyUnits.length
        };
    }
    
    /**
     * 模拟伤害测试
     */
    public testDamage() {
        // 对第一个敌人造成伤害
        if (this.enemyUnits.length > 0) {
            this.enemyUnits[0].takeDamage(50);
        }
        
        // 对玩家造成伤害
        if (this.playerUnits.length > 0) {
            this.playerUnits[0].takeDamage(30);
        }
    }
}
