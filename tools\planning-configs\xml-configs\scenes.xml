<?xml version="1.0" encoding="UTF-8"?>
<!-- Copyright (c) 2025 "放置". All rights reserved. -->
<!-- 场景配置表 - 策划专用XML格式 -->
<GameScenes version="1.0.0" lastUpdated="2025-01-10T00:00:00Z">
  
  <!-- 鱼塘场景 -->
  <Scene id="fishpond">
    <BasicInfo>
      <Name>鱼塘</Name>
      <Description>宁静的鱼塘，适合钓鱼放松</Description>
      <Type>fishing</Type>
      <Level>1</Level>
    </BasicInfo>
    
    <UnlockConditions>
      <PlayerLevel>1</PlayerLevel>
      <RequiredItems></RequiredItems>
      <CompletedQuests></CompletedQuests>
      <RequiredAreas></RequiredAreas>
    </UnlockConditions>
    
    <MapConfig>
      <BackgroundImagePath>areas/fishpond/background</BackgroundImagePath>
      <MapSize width="1600" height="1200" />
      <NodeSpacing x="200" y="150" />
      <StartPosition x="-600" y="0" />
      <BranchCount>3</BranchCount>
      <NodesPerBranch>5</NodesPerBranch>
    </MapConfig>
    
    <Environment>
      <Weather>sunny</Weather>
      <TimeOfDay>morning</TimeOfDay>
      <BackgroundMusic>audio/bgm/peaceful_pond</BackgroundMusic>
      <AmbientSounds>
        <Sound>water_ripple</Sound>
        <Sound>bird_chirping</Sound>
      </AmbientSounds>
      <LightingColor r="255" g="248" b="220" a="255" />
    </Environment>
    
    <Rewards>
      <BaseExp>10</BaseExp>
      <BaseGold>5</BaseGold>
      <DropTables>
        <DropTable>fishing_drops</DropTable>
      </DropTables>
    </Rewards>
    
    <Behaviors>
      <!-- 基础钓鱼行为 -->
      <Behavior id="fishing_basic">
        <Name>基础钓鱼</Name>
        <Description>使用基础鱼竿进行钓鱼</Description>
        <Type>fishing</Type>
        <Duration>30</Duration>
        <Cooldown>5</Cooldown>
        <EnergyCost>10</EnergyCost>
        
        <Requirements>
          <Items>
            <Item>fishing_rod_basic</Item>
          </Items>
          <Skills></Skills>
          <Level>1</Level>
        </Requirements>
        
        <Rewards>
          <BaseExp>10</BaseExp>
          <Items>
            <Item id="fish_common" probability="0.7" minQuantity="1" maxQuantity="3" />
            <Item id="fish_rare" probability="0.2" minQuantity="1" maxQuantity="1" />
            <Item id="treasure_box" probability="0.1" minQuantity="1" maxQuantity="1" />
          </Items>
          <Currency>
            <Gold min="5" max="15" />
          </Currency>
        </Rewards>
      </Behavior>
      
      <!-- 高级钓鱼行为 -->
      <Behavior id="fishing_advanced">
        <Name>高级钓鱼</Name>
        <Description>使用高级鱼竿进行钓鱼，获得更好的收益</Description>
        <Type>fishing</Type>
        <Duration>60</Duration>
        <Cooldown>10</Cooldown>
        <EnergyCost>20</EnergyCost>
        
        <Requirements>
          <Items>
            <Item>fishing_rod_advanced</Item>
          </Items>
          <Skills>
            <Skill>fishing_skill</Skill>
          </Skills>
          <Level>10</Level>
        </Requirements>
        
        <Rewards>
          <BaseExp>25</BaseExp>
          <Items>
            <Item id="fish_rare" probability="0.5" minQuantity="1" maxQuantity="2" />
            <Item id="fish_epic" probability="0.3" minQuantity="1" maxQuantity="1" />
            <Item id="treasure_box_rare" probability="0.2" minQuantity="1" maxQuantity="1" />
          </Items>
          <Currency>
            <Gold min="15" max="35" />
          </Currency>
        </Rewards>
      </Behavior>
    </Behaviors>
    
    <UILayout>
      <BehaviorButtonsPosition x="0" y="-300" />
      <InfoDisplayPosition x="-400" y="250" />
      <CustomElements>
        <Element type="weather_display" x="350" y="250">
          <Config showTemperature="true" showWind="false" />
        </Element>
      </CustomElements>
    </UILayout>
  </Scene>
  
  <!-- 农场场景 -->
  <Scene id="farm">
    <BasicInfo>
      <Name>农场</Name>
      <Description>肥沃的农场，适合种植各种作物</Description>
      <Type>farming</Type>
      <Level>2</Level>
    </BasicInfo>
    
    <UnlockConditions>
      <PlayerLevel>5</PlayerLevel>
      <RequiredItems></RequiredItems>
      <CompletedQuests>
        <Quest>tutorial_fishing</Quest>
      </CompletedQuests>
      <RequiredAreas>
        <Area>fishpond</Area>
      </RequiredAreas>
    </UnlockConditions>
    
    <MapConfig>
      <BackgroundImagePath>areas/farm/background</BackgroundImagePath>
      <MapSize width="1800" height="1400" />
      <NodeSpacing x="180" y="160" />
      <StartPosition x="-700" y="0" />
      <BranchCount>4</BranchCount>
      <NodesPerBranch>6</NodesPerBranch>
    </MapConfig>
    
    <Environment>
      <Weather>sunny</Weather>
      <TimeOfDay>afternoon</TimeOfDay>
      <BackgroundMusic>audio/bgm/farm_life</BackgroundMusic>
      <AmbientSounds>
        <Sound>wind_through_crops</Sound>
        <Sound>farm_animals</Sound>
      </AmbientSounds>
      <LightingColor r="255" g="235" b="200" a="255" />
    </Environment>
    
    <Rewards>
      <BaseExp>15</BaseExp>
      <BaseGold>8</BaseGold>
      <DropTables>
        <DropTable>farming_drops</DropTable>
      </DropTables>
    </Rewards>
    
    <Behaviors>
      <!-- 基础种植行为 -->
      <Behavior id="planting_basic">
        <Name>基础种植</Name>
        <Description>种植基础作物</Description>
        <Type>farming</Type>
        <Duration>45</Duration>
        <Cooldown>0</Cooldown>
        <EnergyCost>15</EnergyCost>
        
        <Requirements>
          <Items>
            <Item>seeds_basic</Item>
          </Items>
          <Skills></Skills>
          <Level>5</Level>
        </Requirements>
        
        <Rewards>
          <BaseExp>15</BaseExp>
          <Items>
            <Item id="crop_wheat" probability="0.8" minQuantity="2" maxQuantity="4" />
            <Item id="crop_corn" probability="0.6" minQuantity="1" maxQuantity="3" />
          </Items>
          <Currency>
            <Gold min="8" max="20" />
          </Currency>
        </Rewards>
      </Behavior>
    </Behaviors>
    
    <UILayout>
      <BehaviorButtonsPosition x="0" y="-320" />
      <InfoDisplayPosition x="-420" y="280" />
      <CustomElements>
        <Element type="crop_status_display" x="380" y="280">
          <Config showGrowthTime="true" showYield="true" />
        </Element>
      </CustomElements>
    </UILayout>
  </Scene>
  
</GameScenes>
