import fs = require('fs');
import path = require('path');
import readline = require('readline');
import { Logger, LogLevel, LogType } from './logger';

/**
 * 日志查询条件接口
 */
export interface LogQueryCondition {
  level?: LogLevel | LogLevel[];
  type?: LogType | LogType[];
  startTime?: Date;
  endTime?: Date;
  userId?: string;
  requestId?: string;
  message?: string;
  operation?: string;
  statusCode?: number | number[];
  duration?: { min?: number; max?: number };
  limit?: number;
  offset?: number;
}

/**
 * 日志条目接口
 */
export interface LogEntry {
  timestamp: string;
  level: string;
  message: string;
  metadata?: any;
  [key: string]: any;
}

/**
 * 日志统计结果接口
 */
export interface LogStatistics {
  totalLogs: number;
  byLevel: Record<string, number>;
  byType: Record<string, number>;
  byHour: Record<string, number>;
  byStatusCode: Record<string, number>;
  averageResponseTime: number;
  errorRate: number;
  topErrors: Array<{ message: string; count: number }>;
  topUsers: Array<{ userId: string; count: number }>;
  topOperations: Array<{ operation: string; count: number }>;
}

/**
 * 日志告警规则接口
 */
export interface LogAlertRule {
  id: string;
  name: string;
  condition: LogQueryCondition;
  threshold: number;
  timeWindow: number; // 时间窗口（分钟）
  enabled: boolean;
  actions: string[]; // 告警动作
}

/**
 * 日志分析器类
 */
export class LogAnalyzer {
  private static instance: LogAnalyzer;
  private logDir: string;
  private alertRules: Map<string, LogAlertRule> = new Map();

  private constructor() {
    this.logDir = process.env['LOG_DIR'] || path.join(process.cwd(), 'logs');
  }

  public static getInstance(): LogAnalyzer {
    if (!LogAnalyzer.instance) {
      LogAnalyzer.instance = new LogAnalyzer();
    }
    return LogAnalyzer.instance;
  }

  /**
   * 查询日志
   */
  public async queryLogs(condition: LogQueryCondition): Promise<LogEntry[]> {
    const results: LogEntry[] = [];
    const logFiles = await this.getLogFiles();

    for (const logFile of logFiles) {
      const entries = await this.readLogFile(logFile);
      const filtered = this.filterLogs(entries, condition);
      results.push(...filtered);
    }

    // 排序和分页
    results.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    
    const offset = condition.offset || 0;
    const limit = condition.limit || 100;
    
    return results.slice(offset, offset + limit);
  }

  /**
   * 获取日志文件列表
   */
  private async getLogFiles(): Promise<string[]> {
    const files: string[] = [];
    
    const scanDirectory = (dir: string) => {
      if (!fs.existsSync(dir)) return;
      
      const items = fs.readdirSync(dir);
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          scanDirectory(fullPath);
        } else if (item.endsWith('.log')) {
          files.push(fullPath);
        }
      }
    };

    scanDirectory(this.logDir);
    return files;
  }

  /**
   * 读取日志文件
   */
  private async readLogFile(filePath: string): Promise<LogEntry[]> {
    const entries: LogEntry[] = [];
    
    const fileStream = fs.createReadStream(filePath);
    const rl = readline.createInterface({
      input: fileStream,
      crlfDelay: Infinity,
    });

    for await (const line of rl) {
      try {
        const entry = JSON.parse(line);
        entries.push(entry);
      } catch (error) {
        // 跳过无效的JSON行
        continue;
      }
    }

    return entries;
  }

  /**
   * 过滤日志
   */
  private filterLogs(logs: LogEntry[], condition: LogQueryCondition): LogEntry[] {
    return logs.filter(log => {
      // 级别过滤
      if (condition.level) {
        const levels = Array.isArray(condition.level) ? condition.level : [condition.level];
        if (!levels.includes(log.level as LogLevel)) {
          return false;
        }
      }

      // 类型过滤
      if (condition.type) {
        const types = Array.isArray(condition.type) ? condition.type : [condition.type];
        if (!types.includes(log.metadata?.type)) {
          return false;
        }
      }

      // 时间过滤
      if (condition.startTime || condition.endTime) {
        const logTime = new Date(log.timestamp);
        if (condition.startTime && logTime < condition.startTime) {
          return false;
        }
        if (condition.endTime && logTime > condition.endTime) {
          return false;
        }
      }

      // 用户ID过滤
      if (condition.userId && log.metadata?.userId !== condition.userId) {
        return false;
      }

      // 请求ID过滤
      if (condition.requestId && log.metadata?.requestId !== condition.requestId) {
        return false;
      }

      // 消息过滤
      if (condition.message && !log.message.includes(condition.message)) {
        return false;
      }

      // 操作过滤
      if (condition.operation && log.metadata?.operation !== condition.operation) {
        return false;
      }

      // 状态码过滤
      if (condition.statusCode) {
        const statusCodes = Array.isArray(condition.statusCode) ? condition.statusCode : [condition.statusCode];
        if (!statusCodes.includes(log.metadata?.statusCode)) {
          return false;
        }
      }

      // 响应时间过滤
      if (condition.duration) {
        const duration = log.metadata?.duration;
        if (duration !== undefined) {
          if (condition.duration.min && duration < condition.duration.min) {
            return false;
          }
          if (condition.duration.max && duration > condition.duration.max) {
            return false;
          }
        }
      }

      return true;
    });
  }

  /**
   * 生成日志统计
   */
  public async generateStatistics(condition: LogQueryCondition = {}): Promise<LogStatistics> {
    const logs = await this.queryLogs({ ...condition, limit: 10000 });
    
    const stats: LogStatistics = {
      totalLogs: logs.length,
      byLevel: {},
      byType: {},
      byHour: {},
      byStatusCode: {},
      averageResponseTime: 0,
      errorRate: 0,
      topErrors: [],
      topUsers: [],
      topOperations: [],
    };

    let totalResponseTime = 0;
    let responseTimeCount = 0;
    let errorCount = 0;
    
    const errorMessages: Map<string, number> = new Map();
    const users: Map<string, number> = new Map();
    const operations: Map<string, number> = new Map();

    for (const log of logs) {
      // 按级别统计
      stats.byLevel[log.level] = (stats.byLevel[log.level] || 0) + 1;

      // 按类型统计
      const type = log.metadata?.type || 'unknown';
      stats.byType[type] = (stats.byType[type] || 0) + 1;

      // 按小时统计
      const hour = new Date(log.timestamp).getHours();
      stats.byHour[hour] = (stats.byHour[hour] || 0) + 1;

      // 按状态码统计
      if (log.metadata?.statusCode) {
        const statusCode = log.metadata.statusCode.toString();
        stats.byStatusCode[statusCode] = (stats.byStatusCode[statusCode] || 0) + 1;
      }

      // 响应时间统计
      if (log.metadata?.duration) {
        totalResponseTime += log.metadata.duration;
        responseTimeCount++;
      }

      // 错误统计
      if (log.level === LogLevel.ERROR) {
        errorCount++;
        const message = log.message;
        errorMessages.set(message, (errorMessages.get(message) || 0) + 1);
      }

      // 用户统计
      if (log.metadata?.userId) {
        const userId = log.metadata.userId;
        users.set(userId, (users.get(userId) || 0) + 1);
      }

      // 操作统计
      if (log.metadata?.operation) {
        const operation = log.metadata.operation;
        operations.set(operation, (operations.get(operation) || 0) + 1);
      }
    }

    // 计算平均响应时间
    stats.averageResponseTime = responseTimeCount > 0 ? totalResponseTime / responseTimeCount : 0;

    // 计算错误率
    stats.errorRate = logs.length > 0 ? (errorCount / logs.length) * 100 : 0;

    // 生成Top列表
    stats.topErrors = Array.from(errorMessages.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([message, count]) => ({ message, count }));

    stats.topUsers = Array.from(users.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([userId, count]) => ({ userId, count }));

    stats.topOperations = Array.from(operations.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([operation, count]) => ({ operation, count }));

    return stats;
  }

  /**
   * 添加告警规则
   */
  public addAlertRule(rule: LogAlertRule): void {
    this.alertRules.set(rule.id, rule);
    Logger.info('日志告警规则已添加', { ruleId: rule.id, ruleName: rule.name });
  }

  /**
   * 删除告警规则
   */
  public removeAlertRule(ruleId: string): void {
    this.alertRules.delete(ruleId);
    Logger.info('日志告警规则已删除', { ruleId });
  }

  /**
   * 获取所有告警规则
   */
  public getAlertRules(): LogAlertRule[] {
    return Array.from(this.alertRules.values());
  }

  /**
   * 检查告警
   */
  public async checkAlerts(): Promise<void> {
    for (const rule of this.alertRules.values()) {
      if (!rule.enabled) continue;

      try {
        await this.checkSingleAlert(rule);
      } catch (error) {
        Logger.error('告警检查失败', { ruleId: rule.id, error });
      }
    }
  }

  /**
   * 检查单个告警规则
   */
  private async checkSingleAlert(rule: LogAlertRule): Promise<void> {
    const endTime = new Date();
    const startTime = new Date(endTime.getTime() - rule.timeWindow * 60 * 1000);

    const condition: LogQueryCondition = {
      ...rule.condition,
      startTime,
      endTime,
      limit: 10000,
    };

    const logs = await this.queryLogs(condition);

    if (logs.length >= rule.threshold) {
      Logger.warn('日志告警触发', {
        ruleId: rule.id,
        ruleName: rule.name,
        threshold: rule.threshold,
        actualCount: logs.length,
        timeWindow: rule.timeWindow,
      });

      // 执行告警动作
      for (const action of rule.actions) {
        await this.executeAlertAction(action, rule, logs);
      }
    }
  }

  /**
   * 执行告警动作
   */
  private async executeAlertAction(action: string, rule: LogAlertRule, logs: LogEntry[]): Promise<void> {
    switch (action) {
      case 'log':
        Logger.warn(`告警: ${rule.name}`, {
          ruleId: rule.id,
          count: logs.length,
          threshold: rule.threshold,
        });
        break;
      
      case 'email':
        // 这里可以集成邮件发送服务
        Logger.info('发送告警邮件', { ruleId: rule.id });
        break;
      
      case 'webhook':
        // 这里可以发送Webhook通知
        Logger.info('发送告警Webhook', { ruleId: rule.id });
        break;
      
      default:
        Logger.warn('未知的告警动作', { action, ruleId: rule.id });
    }
  }

  /**
   * 生成日志报告
   */
  public async generateReport(startTime: Date, endTime: Date): Promise<any> {
    const condition: LogQueryCondition = {
      startTime,
      endTime,
      limit: 50000,
    };

    const [logs, stats] = await Promise.all([
      this.queryLogs(condition),
      this.generateStatistics(condition),
    ]);

    const report = {
      period: {
        start: startTime.toISOString(),
        end: endTime.toISOString(),
        duration: endTime.getTime() - startTime.getTime(),
      },
      summary: {
        totalLogs: stats.totalLogs,
        errorRate: stats.errorRate,
        averageResponseTime: stats.averageResponseTime,
      },
      statistics: stats,
      recentErrors: logs
        .filter(log => log.level === LogLevel.ERROR)
        .slice(0, 10),
      recommendations: this.generateRecommendations(stats),
    };

    return report;
  }

  /**
   * 生成优化建议
   */
  private generateRecommendations(stats: LogStatistics): string[] {
    const recommendations: string[] = [];

    if (stats.errorRate > 5) {
      recommendations.push('错误率较高，建议检查应用程序稳定性');
    }

    if (stats.averageResponseTime > 1000) {
      recommendations.push('平均响应时间较长，建议优化性能');
    }

    if (stats.byLevel[LogLevel.WARN] > stats.totalLogs * 0.1) {
      recommendations.push('警告日志较多，建议检查潜在问题');
    }

    if (stats.topErrors.length > 0) {
      recommendations.push(`最常见错误: ${stats.topErrors[0].message}`);
    }

    return recommendations;
  }

  /**
   * 清理旧日志
   */
  public async cleanupLogs(retentionDays: number = 30): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    const logFiles = await this.getLogFiles();
    let deletedCount = 0;

    for (const logFile of logFiles) {
      const stats = fs.statSync(logFile);
      if (stats.mtime < cutoffDate) {
        fs.unlinkSync(logFile);
        deletedCount++;
        Logger.info('已删除旧日志文件', { file: logFile });
      }
    }

    Logger.info('日志清理完成', { deletedCount, retentionDays });
  }

  /**
   * 导出日志
   */
  public async exportLogs(condition: LogQueryCondition, format: 'json' | 'csv' = 'json'): Promise<string> {
    const logs = await this.queryLogs(condition);

    if (format === 'csv') {
      return this.exportToCSV(logs);
    } else {
      return JSON.stringify(logs, null, 2);
    }
  }

  /**
   * 导出为CSV格式
   */
  private exportToCSV(logs: LogEntry[]): string {
    if (logs.length === 0) return '';

    const headers = ['timestamp', 'level', 'message', 'type', 'userId', 'requestId', 'statusCode', 'duration'];
    const csvLines = [headers.join(',')];

    for (const log of logs) {
      const row = headers.map(header => {
        let value = '';
        if (header === 'timestamp' || header === 'level' || header === 'message') {
          value = log[header] || '';
        } else {
          value = log.metadata?.[header] || '';
        }
        return `"${value.toString().replace(/"/g, '""')}"`;
      });
      csvLines.push(row.join(','));
    }

    return csvLines.join('\n');
  }
}
