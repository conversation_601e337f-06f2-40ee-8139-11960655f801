// Copyright (c) 2025 "放置". All rights reserved.
import express = require('express');
import fs = require('fs');
import path = require('path');
import { Logger } from '../utils/logger';
import xml2js = require('xml2js');

const router = express.Router();

/**
 * 地点配置路由模块
 * 
 * 功能：
 * - 获取单个地点配置
 * - 批量获取地点配置
 * - 获取地点列表
 * - 地点配置缓存管理
 */

// 地点配置缓存
const locationConfigCache = new Map<string, any>();
let cacheInitialized = false;

// XML解析器
const xmlParser = new xml2js.Parser({
    explicitArray: false,
    mergeAttrs: true,
    normalize: true,
    normalizeTags: true,
    trim: true
});

// XML场景数据缓存
const xmlSceneDataCache = new Map<string, any>();
let xmlCacheInitialized = false;

/**
 * 初始化XML场景数据缓存
 */
function initializeXMLSceneCache(): void {
    if (xmlCacheInitialized) return;

    Logger.info('🗺️ 初始化XML场景数据缓存...');

    try {
        // 读取XML配置文件
        const xmlConfigPath = path.join(__dirname, '../../../tools/planning-configs/xml-configs/scenes.xml');

        if (!fs.existsSync(xmlConfigPath)) {
            Logger.warn(`⚠️ XML场景配置文件不存在: ${xmlConfigPath}`);
            return;
        }

        const xmlContent = fs.readFileSync(xmlConfigPath, 'utf8');

        // 解析XML
        xmlParser.parseString(xmlContent, (err: any, result: any) => {
            if (err) {
                Logger.error('❌ XML解析失败', err);
                return;
            }

            if (result && result.gamescenes && result.gamescenes.scene) {
                const scenes = Array.isArray(result.gamescenes.scene) ? result.gamescenes.scene : [result.gamescenes.scene];

                scenes.forEach((scene: any) => {
                    if (scene && scene.id) {
                        xmlSceneDataCache.set(scene.id, {
                            sceneId: scene.id,
                            version: result.gamescenes.version || 'v1.0.0',
                            data: scene,
                            description: `${scene.basicinfo?.name || scene.id} XML场景配置`,
                            createdAt: new Date(),
                            updatedAt: new Date()
                        });

                        Logger.info(`✅ XML场景配置已缓存: ${scene.id} (${scene.basicinfo?.name || 'Unknown'})`);
                    }
                });

                xmlCacheInitialized = true;
                Logger.info(`📋 XML场景数据缓存初始化完成，共加载 ${xmlSceneDataCache.size} 个场景`);
            }
        });

    } catch (error) {
        Logger.error('❌ 初始化XML场景数据缓存失败', error);
    }
}

/**
 * 初始化地点配置缓存
 */
function initializeLocationCache(): void {
    if (cacheInitialized) return;

    Logger.info('🗺️ 初始化地点配置缓存...');

    try {
        // 尝试多个可能的路径加载game_areas.json
        const possiblePaths = [
            path.join(__dirname, '../../../assets/configs/areas/game_areas.json'),
            path.join(process.cwd(), 'assets/configs/areas/game_areas.json'),
            path.join(__dirname, '../../assets/configs/areas/game_areas.json')
        ];

        let configPath = '';
        for (const testPath of possiblePaths) {
            if (fs.existsSync(testPath)) {
                configPath = testPath;
                break;
            }
        }

        if (!configPath) {
            Logger.error('❌ 地点配置文件不存在: game_areas.json');
            return;
        }

        const configData = fs.readFileSync(configPath, 'utf8');
        const gameAreasConfig = JSON.parse(configData);

        if (!gameAreasConfig.locations) {
            Logger.error('❌ 地点配置格式错误: 缺少locations字段');
            return;
        }

        // 遍历所有地点配置
        for (const [locationId, config] of Object.entries(gameAreasConfig.locations)) {
            try {
                locationConfigCache.set(locationId, {
                    locationId: locationId,
                    version: gameAreasConfig.version || 'v1.0.0',
                    data: config,
                    description: `${(config as any).name} 地点配置`,
                    createdAt: new Date(),
                    updatedAt: new Date()
                });

                Logger.info(`✅ 地点配置已缓存: ${locationId} (${(config as any).name})`);
            } catch (error) {
                Logger.error(`❌ 处理地点配置失败: ${locationId}`, error);
            }
        }

        cacheInitialized = true;
        Logger.info(`📋 地点配置缓存初始化完成，共加载 ${locationConfigCache.size} 个地点`);
    } catch (error) {
        Logger.error('❌ 初始化地点配置缓存失败', error);
    }
}

/**
 * @route GET /api/v1/locations/config/:locationId
 * @desc 获取单个地点配置
 * @access Public
 */
router.get('/config/:locationId', async (req, res) => {
    try {
        Logger.info('🗺️ 收到地点配置请求', { locationId: req.params.locationId, url: req.url, path: req.path });
        initializeLocationCache();

        const { locationId } = req.params;
        const { version = 'latest' } = req.query;

        Logger.info(`📋 请求地点配置: ${locationId} (版本: ${version})`);

        if (!locationId) {
            return res.status(400).json({
                success: false,
                message: '缺少地点ID',
                error: 'MISSING_LOCATION_ID'
            });
        }

        const config = locationConfigCache.get(locationId);
        if (!config) {
            Logger.warn(`⚠️ 地点配置不存在: ${locationId}`);
            return res.status(404).json({
                success: false,
                message: '地点配置不存在',
                error: 'LOCATION_CONFIG_NOT_FOUND'
            });
        }

        Logger.info(`✅ 返回地点配置: ${locationId}`);
        return res.json({
            success: true,
            message: '获取地点配置成功',
            data: {
                locationId: config.locationId,
                version: config.version,
                config: config.data,
                lastUpdated: config.updatedAt,
                cacheExpiry: new Date(Date.now() + 30 * 60 * 1000) // 30分钟缓存
            }
        });
    } catch (error) {
        Logger.error('获取地点配置失败', error);
        return res.status(500).json({
            success: false,
            message: '获取地点配置失败',
            error: 'INTERNAL_SERVER_ERROR'
        });
    }
});

/**
 * @route POST /api/v1/locations/config/batch
 * @desc 批量获取地点配置
 * @access Public
 */
router.post('/config/batch', async (req, res) => {
    try {
        initializeLocationCache();
        
        const { locationIds, version = 'latest' } = req.body;

        Logger.info(`📋 批量请求地点配置: ${locationIds?.join(', ')} (版本: ${version})`);

        if (!locationIds || !Array.isArray(locationIds)) {
            return res.status(400).json({
                success: false,
                message: '缺少地点ID列表',
                error: 'MISSING_LOCATION_IDS'
            });
        }

        const configs: Record<string, any> = {};
        const notFound: string[] = [];

        for (const locationId of locationIds) {
            const config = locationConfigCache.get(locationId);
            if (config) {
                configs[locationId] = {
                    locationId: config.locationId,
                    version: config.version,
                    config: config.data,
                    lastUpdated: config.updatedAt
                };
            } else {
                notFound.push(locationId);
            }
        }

        Logger.info(`✅ 返回 ${Object.keys(configs).length} 个地点配置`);
        if (notFound.length > 0) {
            Logger.warn(`⚠️ 未找到的地点: ${notFound.join(', ')}`);
        }

        return res.json({
            success: true,
            message: '批量获取地点配置成功',
            data: {
                configs,
                notFound,
                cacheExpiry: new Date(Date.now() + 30 * 60 * 1000)
            }
        });
    } catch (error) {
        Logger.error('批量获取地点配置失败', error);
        return res.status(500).json({
            success: false,
            message: '批量获取地点配置失败',
            error: 'INTERNAL_SERVER_ERROR'
        });
    }
});

/**
 * @route GET /api/v1/locations/list
 * @desc 获取地点列表
 * @access Public
 */
// 简单测试路由
router.get('/test', (req, res) => {
    Logger.info('🗺️ 测试路由被调用！', { url: req.url, path: req.path });
    res.json({ success: true, message: '测试路由工作正常！', timestamp: new Date().toISOString() });
});

router.get('/list', async (req, res) => {
    try {
        Logger.info('🗺️ 收到地点列表请求', { url: req.url, path: req.path });
        initializeLocationCache();

        Logger.info('📋 请求地点列表');

        const locations = Array.from(locationConfigCache.values()).map(config => ({
            locationId: config.locationId,
            name: config.data.name,
            description: config.data.description,
            level: config.data.level,
            type: config.data.type,
            version: config.version,
            lastUpdated: config.updatedAt
        }));

        Logger.info(`✅ 返回 ${locations.length} 个地点`);
        res.json({
            success: true,
            message: '获取地点列表成功',
            data: {
                locations,
                total: locations.length
            }
        });
    } catch (error) {
        Logger.error('获取地点列表失败', error);
        res.status(500).json({
            success: false,
            message: '获取地点列表失败',
            error: 'INTERNAL_SERVER_ERROR'
        });
    }
});

/**
 * @route POST /api/v1/locations/cache/refresh
 * @desc 刷新地点配置缓存
 * @access Public
 */
router.post('/cache/refresh', async (_req, res) => {
    try {
        Logger.info('🔄 刷新地点配置缓存');
        
        // 清空缓存
        locationConfigCache.clear();
        cacheInitialized = false;
        
        // 重新初始化
        initializeLocationCache();
        
        res.json({
            success: true,
            message: '地点配置缓存刷新成功',
            data: {
                cachedLocations: locationConfigCache.size,
                refreshedAt: new Date().toISOString()
            }
        });
    } catch (error) {
        Logger.error('刷新地点配置缓存失败', error);
        res.status(500).json({
            success: false,
            message: '刷新地点配置缓存失败',
            error: 'INTERNAL_SERVER_ERROR'
        });
    }
});

/**
 * @route GET /api/v1/locations/cache/status
 * @desc 获取缓存状态
 * @access Public
 */
router.get('/cache/status', async (_req, res) => {
    try {
        res.json({
            success: true,
            message: '获取缓存状态成功',
            data: {
                initialized: cacheInitialized,
                cachedLocations: locationConfigCache.size,
                locations: Array.from(locationConfigCache.keys()),
                lastInitialized: cacheInitialized ? new Date().toISOString() : null
            }
        });
    } catch (error) {
        Logger.error('获取缓存状态失败', error);
        res.status(500).json({
            success: false,
            message: '获取缓存状态失败',
            error: 'INTERNAL_SERVER_ERROR'
        });
    }
});

/**
 * @route GET /api/v1/locations/scene/:sceneId
 * @desc 获取场景数据（结合XML配置和后端数据）
 * @access Public
 */
router.get('/scene/:sceneId', async (req, res) => {
    try {
        const { sceneId } = req.params;
        Logger.info(`🗺️ 收到场景数据请求: ${sceneId}`);

        // 初始化缓存
        initializeLocationCache();
        initializeXMLSceneCache();

        // 获取XML场景配置
        const xmlSceneData = xmlSceneDataCache.get(sceneId);
        if (!xmlSceneData) {
            Logger.warn(`⚠️ 未找到场景XML配置: ${sceneId}`);
            return res.status(404).json({
                success: false,
                message: `场景配置不存在: ${sceneId}`,
                error: 'SCENE_NOT_FOUND'
            });
        }

        // 获取地点配置数据
        const locationConfig = locationConfigCache.get(sceneId);

        // 结合XML配置和后端数据
        const sceneData = {
            sceneId: sceneId,
            version: xmlSceneData.version,
            xmlConfig: xmlSceneData.data,
            serverData: locationConfig ? locationConfig.data : null,
            metadata: {
                hasXMLConfig: !!xmlSceneData,
                hasServerData: !!locationConfig,
                lastUpdated: xmlSceneData.updatedAt,
                description: xmlSceneData.description
            }
        };

        Logger.info(`✅ 返回场景数据: ${sceneId}`);
        res.json({
            success: true,
            message: '获取场景数据成功',
            data: sceneData
        });

    } catch (error) {
        Logger.error(`获取场景数据失败: ${req.params.sceneId}`, error);
        res.status(500).json({
            success: false,
            message: '获取场景数据失败',
            error: 'INTERNAL_SERVER_ERROR'
        });
    }
});

export { router as locationRoutes };
