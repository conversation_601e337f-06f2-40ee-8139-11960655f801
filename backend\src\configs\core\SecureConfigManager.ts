/**
 * Copyright (c) 2025 "放置". All rights reserved.
 * 安全配置管理器 - 多人在线游戏核心
 */

import { Redis } from 'ioredis';
import { Logger } from '../../utils/Logger';
import { 
  ConfigType, 
  ConfigSecurityLevel, 
  CacheStrategyConfig,
  ConfigVersion,
  AntiCheatConfig 
} from '../../../shared/configs/types/config-types';

/**
 * 安全配置管理器
 * 负责管理所有游戏配置，确保多人游戏的安全性和一致性
 */
export class SecureConfigManager {
  private static instance: SecureConfigManager;
  private redis: Redis;
  private memoryCache: Map<string, any> = new Map();
  private configVersions: Map<string, ConfigVersion> = new Map();
  private antiCheatConfig: AntiCheatConfig;

  private constructor() {
    this.initializeRedis();
    this.loadAntiCheatConfig();
  }

  public static getInstance(): SecureConfigManager {
    if (!SecureConfigManager.instance) {
      SecureConfigManager.instance = new SecureConfigManager();
    }
    return SecureConfigManager.instance;
  }

  /**
   * 初始化Redis连接
   */
  private initializeRedis(): void {
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '0'),
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true
    });

    this.redis.on('error', (error) => {
      Logger.error('Redis连接错误', { error });
    });

    this.redis.on('connect', () => {
      Logger.info('Redis连接成功');
    });
  }

  /**
   * 加载防作弊配置
   */
  private async loadAntiCheatConfig(): Promise<void> {
    try {
      this.antiCheatConfig = await this.getConfig('security/anti-cheat', ConfigSecurityLevel.PRIVATE);
    } catch (error) {
      Logger.error('加载防作弊配置失败', { error });
      // 使用默认配置
      this.antiCheatConfig = this.getDefaultAntiCheatConfig();
    }
  }

  /**
   * 获取配置（多层缓存策略）
   */
  public async getConfig<T = any>(
    configPath: string, 
    securityLevel: ConfigSecurityLevel = ConfigSecurityLevel.PUBLIC,
    userId?: string
  ): Promise<T | null> {
    try {
      // 安全检查
      if (!this.validateConfigAccess(configPath, securityLevel, userId)) {
        Logger.warn('配置访问被拒绝', { configPath, securityLevel, userId });
        return null;
      }

      const cacheKey = this.generateCacheKey(configPath, securityLevel, userId);

      // L1缓存：内存缓存（最快）
      if (this.memoryCache.has(cacheKey)) {
        Logger.debug('从内存缓存获取配置', { configPath });
        return this.memoryCache.get(cacheKey);
      }

      // L2缓存：Redis缓存
      const redisValue = await this.redis.get(cacheKey);
      if (redisValue) {
        const config = JSON.parse(redisValue);
        // 更新内存缓存
        this.memoryCache.set(cacheKey, config);
        Logger.debug('从Redis缓存获取配置', { configPath });
        return config;
      }

      // L3缓存：数据库/文件系统
      const config = await this.loadConfigFromSource(configPath, securityLevel);
      if (config) {
        // 更新所有缓存层
        await this.setCacheValue(cacheKey, config, this.getCacheTTL(configPath));
        Logger.debug('从源加载配置', { configPath });
        return config;
      }

      Logger.warn('配置未找到', { configPath });
      return null;

    } catch (error) {
      Logger.error('获取配置失败', { configPath, error });
      return null;
    }
  }

  /**
   * 设置配置（安全更新）
   */
  public async setConfig(
    configPath: string,
    config: any,
    securityLevel: ConfigSecurityLevel,
    operatorId: string,
    version?: string
  ): Promise<boolean> {
    try {
      // 权限检查
      if (!this.validateConfigWrite(configPath, securityLevel, operatorId)) {
        Logger.warn('配置写入权限不足', { configPath, operatorId });
        return false;
      }

      // 数据验证
      if (!this.validateConfigData(configPath, config)) {
        Logger.warn('配置数据验证失败', { configPath });
        return false;
      }

      // 版本控制
      const newVersion = version || this.generateVersion();
      const configVersion: ConfigVersion = {
        version: newVersion,
        timestamp: Date.now(),
        checksum: this.calculateChecksum(config),
        changes: [], // 这里应该计算变更
        compatibility: {
          minClientVersion: '1.0.0'
        },
        rollback: {
          enabled: true,
          autoRollbackOnError: true
        }
      };

      // 保存到数据库/文件系统
      await this.saveConfigToSource(configPath, config, configVersion);

      // 更新缓存
      const cacheKey = this.generateCacheKey(configPath, securityLevel);
      await this.setCacheValue(cacheKey, config, this.getCacheTTL(configPath));

      // 记录版本信息
      this.configVersions.set(configPath, configVersion);

      // 通知其他服务器节点（如果是集群）
      await this.notifyConfigUpdate(configPath, config, configVersion);

      Logger.info('配置更新成功', { configPath, version: newVersion, operatorId });
      return true;

    } catch (error) {
      Logger.error('设置配置失败', { configPath, error });
      return false;
    }
  }

  /**
   * 批量获取配置（优化性能）
   */
  public async getBatchConfigs(
    configPaths: string[],
    securityLevel: ConfigSecurityLevel = ConfigSecurityLevel.PUBLIC,
    userId?: string
  ): Promise<Record<string, any>> {
    const results: Record<string, any> = {};
    
    try {
      // 并行获取所有配置
      const promises = configPaths.map(async (path) => {
        const config = await this.getConfig(path, securityLevel, userId);
        return { path, config };
      });

      const configs = await Promise.all(promises);
      
      configs.forEach(({ path, config }) => {
        if (config !== null) {
          results[path] = config;
        }
      });

      Logger.debug('批量获取配置完成', { 
        requested: configPaths.length, 
        found: Object.keys(results).length 
      });

    } catch (error) {
      Logger.error('批量获取配置失败', { configPaths, error });
    }

    return results;
  }

  /**
   * 验证用户操作（防作弊）
   */
  public async validateUserAction(
    userId: string,
    action: string,
    data: any,
    timestamp: number
  ): Promise<{ valid: boolean; reason?: string }> {
    try {
      if (!this.antiCheatConfig.enabled) {
        return { valid: true };
      }

      // 时间验证
      if (this.antiCheatConfig.checks.timeValidation.enabled) {
        const timeDiff = Math.abs(Date.now() - timestamp);
        if (timeDiff > this.antiCheatConfig.checks.timeValidation.maxClockSkew) {
          return { valid: false, reason: 'time_desync' };
        }
      }

      // 操作频率验证
      const actionKey = `action:${userId}:${action}`;
      const lastActionTime = await this.redis.get(actionKey);
      if (lastActionTime) {
        const interval = timestamp - parseInt(lastActionTime);
        if (interval < this.antiCheatConfig.checks.timeValidation.minActionInterval) {
          return { valid: false, reason: 'action_too_frequent' };
        }
      }

      // 记录操作时间
      await this.redis.setex(actionKey, 60, timestamp.toString());

      // 并发验证
      if (this.antiCheatConfig.checks.concurrencyValidation.enabled) {
        const concurrentKey = `concurrent:${userId}`;
        const concurrent = await this.redis.incr(concurrentKey);
        await this.redis.expire(concurrentKey, 10);
        
        if (concurrent > this.antiCheatConfig.checks.concurrencyValidation.maxConcurrentActions) {
          return { valid: false, reason: 'too_many_concurrent_actions' };
        }
      }

      return { valid: true };

    } catch (error) {
      Logger.error('验证用户操作失败', { userId, action, error });
      return { valid: false, reason: 'validation_error' };
    }
  }

  /**
   * 清理过期缓存
   */
  public async cleanupExpiredCache(): Promise<void> {
    try {
      // 清理内存缓存（简单的LRU策略）
      if (this.memoryCache.size > 1000) {
        const entries = Array.from(this.memoryCache.entries());
        const toDelete = entries.slice(0, entries.length - 800);
        toDelete.forEach(([key]) => this.memoryCache.delete(key));
      }

      Logger.debug('缓存清理完成', { memorySize: this.memoryCache.size });

    } catch (error) {
      Logger.error('缓存清理失败', { error });
    }
  }

  // ==================== 私有方法 ====================

  private validateConfigAccess(
    configPath: string, 
    securityLevel: ConfigSecurityLevel, 
    userId?: string
  ): boolean {
    // 根据安全级别验证访问权限
    switch (securityLevel) {
      case ConfigSecurityLevel.PUBLIC:
        return true;
      case ConfigSecurityLevel.PROTECTED:
        return !!userId;
      case ConfigSecurityLevel.PRIVATE:
      case ConfigSecurityLevel.SECRET:
        return false; // 客户端不能访问私有配置
      default:
        return false;
    }
  }

  private validateConfigWrite(
    configPath: string, 
    securityLevel: ConfigSecurityLevel, 
    operatorId: string
  ): boolean {
    // 这里应该实现更复杂的权限检查
    return !!operatorId && securityLevel !== ConfigSecurityLevel.SECRET;
  }

  private validateConfigData(configPath: string, config: any): boolean {
    // 这里应该实现配置数据的schema验证
    return config !== null && config !== undefined;
  }

  private generateCacheKey(
    configPath: string, 
    securityLevel: ConfigSecurityLevel, 
    userId?: string
  ): string {
    const parts = ['config', configPath, securityLevel];
    if (userId) parts.push(userId);
    return parts.join(':');
  }

  private getCacheTTL(configPath: string): number {
    // 根据配置类型返回不同的TTL
    if (configPath.includes('static')) return 3600; // 1小时
    if (configPath.includes('dynamic')) return 300;  // 5分钟
    if (configPath.includes('user')) return 1800;    // 30分钟
    return 600; // 默认10分钟
  }

  private async setCacheValue(key: string, value: any, ttl: number): Promise<void> {
    // 更新内存缓存
    this.memoryCache.set(key, value);
    
    // 更新Redis缓存
    await this.redis.setex(key, ttl, JSON.stringify(value));
  }

  private async loadConfigFromSource(
    configPath: string, 
    securityLevel: ConfigSecurityLevel
  ): Promise<any> {
    // 这里应该从数据库或文件系统加载配置
    // 暂时返回null，需要具体实现
    return null;
  }

  private async saveConfigToSource(
    configPath: string, 
    config: any, 
    version: ConfigVersion
  ): Promise<void> {
    // 这里应该保存到数据库或文件系统
    // 需要具体实现
  }

  private async notifyConfigUpdate(
    configPath: string, 
    config: any, 
    version: ConfigVersion
  ): Promise<void> {
    // 通知其他服务器节点配置更新
    await this.redis.publish('config:update', JSON.stringify({
      configPath,
      version: version.version,
      timestamp: Date.now()
    }));
  }

  private generateVersion(): string {
    return `${Date.now()}.${Math.random().toString(36).substr(2, 9)}`;
  }

  private calculateChecksum(data: any): string {
    // 简单的校验和计算，实际应该使用更安全的算法
    return Buffer.from(JSON.stringify(data)).toString('base64').substr(0, 16);
  }

  private getDefaultAntiCheatConfig(): AntiCheatConfig {
    return {
      enabled: true,
      checks: {
        timeValidation: {
          enabled: true,
          maxClockSkew: 5000,
          minActionInterval: 100
        },
        progressValidation: {
          enabled: true,
          maxProgressPerSecond: 100,
          validateRewards: true
        },
        behaviorValidation: {
          enabled: true,
          checkDuration: true,
          checkCooldown: true,
          checkRequirements: true
        },
        concurrencyValidation: {
          enabled: true,
          maxConcurrentActions: 5,
          maxSessionsPerUser: 3
        }
      },
      penalties: {
        warning: { threshold: 3, action: 'log' },
        tempBan: { threshold: 10, duration: 3600 },
        permBan: { threshold: 50 }
      },
      logging: {
        logSuspicious: true,
        logLevel: 'warn',
        retentionDays: 30
      }
    };
  }
}
