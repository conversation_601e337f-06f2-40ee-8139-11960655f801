# Copyright (c) 2025 "放置". All rights reserved.

# 挂机放置类游戏配置表结构设计文档

## 📋 文档信息
- **文档版本**: v1.0.0
- **创建日期**: 2025-01-10
- **最后更新**: 2025-01-10
- **适用项目**: 挂机放置类微信小程序游戏
- **技术栈**: Cocos Creator 3.8.6 + Node.js后端

## 🎯 设计方案概述

### 采用方案：改良版混合模式（方案B）
经过深度调研和技术评估，我们采用改良版混合模式来设计配置表结构，该方案在保证数据安全的前提下，最大化地满足挂机游戏特性和微信小程序的技术需求。

### 核心设计原则
1. **职责分离**：前端负责展示，后端负责逻辑，共享负责基础
2. **数据安全**：敏感数值和计算公式完全在后端
3. **性能优化**：合理的缓存策略和Asset Bundle分包
4. **挂机适配**：支持长时间离线体验和自动计算
5. **版本控制**：完整的配置版本管理和热更新机制

## 🏗️ 整体架构图

```
配置表分层架构：
┌─────────────────────────────────────────────────────────────┐
│                    前端配置层 (Client)                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  UI配置     │ │  资源配置   │ │  本地化配置 │ │ 动画配置│ │
│  │ 界面布局    │ │ 图片路径    │ │ 多语言文本  │ │ 特效配置│ │
│  │ 按钮样式    │ │ 音频资源    │ │ 错误提示    │ │ 动画参数│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              ↕ 数据同步
┌─────────────────────────────────────────────────────────────┐
│                   共享配置层 (Shared)                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐             │
│  │  基础配置   │ │  版本控制   │ │  解锁条件   │             │
│  │ 游戏常量    │ │ 配置版本    │ │ 功能解锁    │             │
│  │ 系统限制    │ │ 兼容性检查  │ │ 内容解锁    │             │
│  └─────────────┘ └─────────────┘ └─────────────┘             │
└─────────────────────────────────────────────────────────────┘
                              ↕ 安全验证
┌─────────────────────────────────────────────────────────────┐
│                    后端配置层 (Server)                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  数值配置   │ │  经济配置   │ │  玩法配置   │ │ 安全配置│ │
│  │ 角色属性    │ │ 货币系统    │ │ 挂机机制    │ │ 防作弊  │ │
│  │ 装备数值    │ │ 商店定价    │ │ 战斗逻辑    │ │ 数据校验│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 📁 目录结构详解

### 前端配置表 (`assets/configs/`)
```
assets/configs/
├── README.md                    # 前端配置说明文档
├── ui/                          # UI相关配置
│   ├── layouts/                 # 界面布局配置
│   │   └── main_scene_layout.json
│   ├── animations/              # 动画配置
│   │   └── ui_animations.json
│   ├── effects/                 # 特效配置
│   └── themes/                  # 主题配置
├── resources/                   # 资源配置
│   ├── images/                  # 图片资源配置
│   │   └── image_resources.json
│   ├── audio/                   # 音频资源配置
│   └── fonts/                   # 字体资源配置
├── localization/                # 本地化配置
│   ├── zh-CN/                   # 中文配置
│   │   └── ui_text.json
│   ├── en-US/                   # 英文配置
│   └── common/                  # 通用文本配置
└── shared/                      # 前后端共享配置（本地缓存）
    ├── base/                    # 基础配置缓存
    ├── version/                 # 版本控制缓存
    └── unlock/                  # 解锁条件缓存
```

### 后端配置表 (`backend/src/configs/`)
```
backend/src/configs/
├── README.md                    # 后端配置说明文档
├── numerical/                   # 数值配置
│   ├── character/               # 角色数值配置
│   │   └── character_stats.json
│   ├── equipment/               # 装备数值配置
│   ├── skills/                  # 技能数值配置
│   └── battle/                  # 战斗数值配置
├── economic/                    # 经济配置
│   ├── currency/                # 货币配置
│   │   └── currency_config.json
│   ├── shop/                    # 商店配置
│   ├── rewards/                 # 奖励配置
│   └── pricing/                 # 定价配置
├── gameplay/                    # 游戏玩法配置
│   ├── idle/                    # 挂机配置
│   │   └── idle_mechanics.json
│   ├── adventure/               # 冒险配置
│   ├── quests/                  # 任务配置
│   └── achievements/            # 成就配置
├── balance/                     # 平衡性配置
│   ├── formulas/                # 计算公式
│   ├── curves/                  # 成长曲线
│   └── limits/                  # 数值限制
└── security/                    # 安全配置
    ├── validation/              # 数据验证
    ├── anti_cheat/              # 防作弊
    └── rate_limits/             # 频率限制
```

### 共享配置表 (`shared/configs/`)
```
shared/configs/
├── README.md                    # 共享配置说明文档
├── base/                        # 基础配置
│   ├── game_constants/          # 游戏常量
│   │   └── game_constants.json
│   ├── feature_flags/           # 功能开关
│   └── system_limits/           # 系统限制
├── version/                     # 版本控制
│   ├── config_versions/         # 配置版本
│   │   └── version_manifest.json
│   ├── compatibility/           # 兼容性配置
│   └── migration/               # 迁移配置
└── unlock/                      # 解锁条件
    ├── features/                # 功能解锁
    │   └── feature_unlock.json
    ├── content/                 # 内容解锁
    └── progression/             # 进度解锁
```

## 🔧 配置管理工具 (`tools/config-manager/`)
```
tools/config-manager/
├── README.md                    # 工具说明文档
├── package.json                 # 依赖配置
├── src/                         # 源代码
│   ├── commands/                # 命令行命令
│   ├── validators/              # 数据校验器
│   ├── sync/                    # 同步机制
│   ├── utils/                   # 工具函数
│   └── types/                   # 类型定义
├── templates/                   # 配置模板
├── scripts/                     # 脚本文件
└── config/                      # 工具配置
```

## 📝 配置文件规范

### 通用规范
1. **文件格式**: 统一使用JSON格式
2. **编码格式**: UTF-8编码
3. **命名规范**: 小写字母+下划线格式 (snake_case)
4. **版权声明**: 每个文件顶部包含版权信息

### 必需字段
所有配置文件必须包含以下字段：
```json
{
  "// Copyright (c) 2025 \"放置\". All rights reserved.": "",
  "version": "1.0.0",
  "config_type": "配置类型",
  "last_updated": "2025-01-10T00:00:00Z",
  "checksum": "校验和"
}
```

### 数据类型规范
- **数值类型**: 使用number，避免字符串数字
- **布尔类型**: 使用boolean，避免0/1
- **时间格式**: 使用ISO 8601格式
- **颜色格式**: 使用十六进制格式 (#RRGGBB)

## 🔄 数据同步机制

### 版本控制流程
1. **版本检查**: 客户端启动时检查配置版本
2. **差异对比**: 对比本地和服务器配置版本
3. **增量更新**: 只下载变更的配置文件
4. **完整性校验**: 验证下载文件的完整性
5. **热更新应用**: 动态应用新配置

### 缓存策略
- **前端缓存**: 使用Asset Bundle进行资源分包
- **本地存储**: 利用微信小程序本地存储API
- **内存缓存**: 运行时配置数据缓存
- **过期策略**: 基于版本号的缓存失效机制

## 🛡️ 安全设计

### 数据安全原则
1. **敏感数据隔离**: 所有数值计算和经济参数仅存储在后端
2. **传输加密**: 配置数据传输使用HTTPS加密
3. **完整性校验**: 每个配置文件都有MD5校验和
4. **签名验证**: 重要配置文件支持数字签名验证
5. **访问控制**: 后端配置需要管理员权限才能修改

### 防作弊机制
- **数值验证**: 所有客户端提交的数据都在服务器端重新计算验证
- **频率限制**: 对关键操作设置频率限制
- **异常检测**: 监控异常的数据变化模式
- **日志记录**: 完整记录所有配置访问和修改日志

## 📊 配置示例

### 前端UI布局配置示例
```json
{
  "version": "1.0.0",
  "config_type": "ui_layout",
  "scene_name": "main_scene",
  "layout": {
    "header": {
      "height": 120,
      "background": "ui/backgrounds/header_bg",
      "elements": {
        "user_info": {
          "position": { "x": 20, "y": 20 },
          "size": { "width": 200, "height": 80 }
        }
      }
    }
  }
}
```

### 后端角色数值配置示例
```json
{
  "version": "1.0.0",
  "config_type": "character_stats",
  "base_stats": {
    "warrior": {
      "health": 100,
      "attack": 20,
      "defense": 15
    }
  },
  "formulas": {
    "total_health": "base_health + (level - 1) * health_per_level"
  }
}
```

### 共享游戏常量配置示例
```json
{
  "version": "1.0.0",
  "config_type": "game_constants",
  "system_limits": {
    "max_character_level": 100,
    "max_inventory_slots": 100
  },
  "quality_levels": {
    "common": { "id": 1, "name": "普通", "color": "#FFFFFF" },
    "rare": { "id": 3, "name": "稀有", "color": "#0080FF" }
  }
}
```

## 🚀 开发工作流

### 配置开发流程
1. **需求分析**: 确定配置需求和数据结构
2. **类型定义**: 在shared/types中定义TypeScript接口
3. **配置编写**: 按照规范编写配置文件
4. **数据校验**: 使用配置管理工具进行校验
5. **版本更新**: 更新版本号和校验和
6. **测试验证**: 在开发环境测试配置效果
7. **部署上线**: 通过配置管理工具部署到生产环境

### 团队协作规范
- **前端开发**: 负责UI配置、资源配置、本地化配置
- **后端开发**: 负责数值配置、经济配置、安全配置
- **策划人员**: 提供配置需求和数值设计
- **测试人员**: 验证配置的正确性和完整性

## 🔧 工具使用指南

### 配置管理工具命令
```bash
# 校验所有配置
npm run validate

# 生成版本清单
npm run generate-manifest

# 同步配置到服务器
npm run sync

# 创建配置备份
npm run backup

# 校验特定配置文件
node dist/cli.js validate --file assets/configs/ui/layouts/main_scene_layout.json

# 生成配置差异报告
node dist/cli.js diff --from v1.0.0 --to v1.1.0
```

### IDE配置建议
- **VSCode插件**: 安装JSON Schema插件进行配置校验
- **代码格式化**: 使用Prettier统一JSON格式
- **类型检查**: 使用TypeScript进行类型检查
- **Git钩子**: 配置pre-commit钩子自动校验配置

## 📈 性能优化

### 加载优化
- **按需加载**: 只加载当前需要的配置
- **预加载**: 预加载下一个场景的配置
- **压缩传输**: 使用gzip压缩配置数据
- **CDN加速**: 使用CDN分发配置文件

### 内存优化
- **延迟解析**: 延迟解析大型配置文件
- **内存池**: 复用配置对象减少GC压力
- **数据压缩**: 使用紧凑的数据结构
- **缓存清理**: 及时清理不需要的配置缓存

## 🐛 常见问题

### Q1: 配置文件版本不匹配怎么办？
A: 客户端会自动检测版本差异并下载最新配置，如果下载失败会使用本地缓存的配置。

### Q2: 如何添加新的配置类型？
A: 1) 在shared/types中定义接口 2) 创建配置文件 3) 更新校验器 4) 更新版本清单

### Q3: 配置热更新失败怎么处理？
A: 检查网络连接、配置文件格式、版本兼容性，必要时重启应用。

### Q4: 如何保证配置数据的安全性？
A: 使用HTTPS传输、MD5校验、数字签名、访问控制等多重安全机制。

## 📞 联系方式

如有配置相关问题，请联系：
- **技术负责人**: [技术负责人姓名]
- **配置管理**: [配置管理员姓名]
- **文档维护**: [文档维护人姓名]

---

**文档状态**: ✅ 已完成
**下次更新**: 根据项目进度和需求变更进行更新
