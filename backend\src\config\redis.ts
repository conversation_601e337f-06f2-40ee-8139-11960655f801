import Redis from 'ioredis';
import { Logger } from '../utils/logger';

/**
 * 内存Redis实现（开发环境使用）
 * 生产环境应该使用真实的Redis服务
 */
export class MemoryRedis {
    private store: Map<string, { value: any; expiry?: number }> = new Map();
    private timers: Map<string, NodeJS.Timeout> = new Map();

    /**
     * 设置键值对
     */
    public async set(key: string, value: any, ttl?: number): Promise<void> {
        try {
            // 清除现有的过期定时器
            if (this.timers.has(key)) {
                clearTimeout(this.timers.get(key)!);
                this.timers.delete(key);
            }

            const serializedValue = JSON.stringify(value);
            const expiry = ttl ? Date.now() + ttl * 1000 : undefined;
            
            this.store.set(key, { value: serializedValue, expiry: expiry || undefined });

            // 设置过期定时器
            if (ttl) {
                const timer = setTimeout(() => {
                    this.store.delete(key);
                    this.timers.delete(key);
                }, ttl * 1000);
                this.timers.set(key, timer);
            }

            Logger.debug(`Redis SET: ${key}`, { ttl });
        } catch (error) {
            Logger.error('Redis SET操作失败', { key, error });
            throw error;
        }
    }

    /**
     * 获取值
     */
    public async get<T>(key: string): Promise<T | null> {
        try {
            const item = this.store.get(key);
            
            if (!item) {
                return null;
            }

            // 检查是否过期
            if (item.expiry && Date.now() > item.expiry) {
                this.store.delete(key);
                if (this.timers.has(key)) {
                    clearTimeout(this.timers.get(key)!);
                    this.timers.delete(key);
                }
                return null;
            }

            const value = JSON.parse(item.value);
            Logger.debug(`Redis GET: ${key}`, { found: true });
            return value;
        } catch (error) {
            Logger.error('Redis GET操作失败', { key, error });
            return null;
        }
    }

    /**
     * 删除键
     */
    public async del(key: string): Promise<void> {
        try {
            this.store.delete(key);
            
            if (this.timers.has(key)) {
                clearTimeout(this.timers.get(key)!);
                this.timers.delete(key);
            }

            Logger.debug(`Redis DEL: ${key}`);
        } catch (error) {
            Logger.error('Redis DEL操作失败', { key, error });
            throw error;
        }
    }

    /**
     * 检查键是否存在
     */
    public async exists(key: string): Promise<boolean> {
        const item = this.store.get(key);
        
        if (!item) {
            return false;
        }

        // 检查是否过期
        if (item.expiry && Date.now() > item.expiry) {
            this.store.delete(key);
            if (this.timers.has(key)) {
                clearTimeout(this.timers.get(key)!);
                this.timers.delete(key);
            }
            return false;
        }

        return true;
    }

    /**
     * 设置过期时间
     */
    public async expire(key: string, ttl: number): Promise<void> {
        const item = this.store.get(key);
        
        if (!item) {
            return;
        }

        // 清除现有定时器
        if (this.timers.has(key)) {
            clearTimeout(this.timers.get(key)!);
            this.timers.delete(key);
        }

        // 更新过期时间
        item.expiry = Date.now() + ttl * 1000;

        // 设置新的定时器
        const timer = setTimeout(() => {
            this.store.delete(key);
            this.timers.delete(key);
        }, ttl * 1000);
        this.timers.set(key, timer);
    }

    /**
     * 清空所有数据
     */
    public async flushall(): Promise<void> {
        this.store.clear();
        
        // 清除所有定时器
        this.timers.forEach((timer) => {
            clearTimeout(timer);
        });
        this.timers.clear();

        Logger.debug('Redis FLUSHALL: 清空所有数据');
    }

    /**
     * 获取所有键
     */
    public async keys(pattern: string = '*'): Promise<string[]> {
        const allKeys = Array.from(this.store.keys());
        
        if (pattern === '*') {
            return allKeys;
        }

        // 简单的模式匹配（支持*通配符）
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        return allKeys.filter(key => regex.test(key));
    }
}

export class RedisConfig {
    private static instance: RedisConfig;
    private client: MemoryRedis;
    private isConnected: boolean = false;

    private constructor() {
        this.client = new MemoryRedis();
    }

    public static getInstance(): RedisConfig {
        if (!RedisConfig.instance) {
            RedisConfig.instance = new RedisConfig();
        }
        return RedisConfig.instance;
    }

    /**
     * 连接Redis（内存实现）
     */
    public async connect(): Promise<void> {
        try {
            this.isConnected = true;
            Logger.info('Redis缓存服务连接成功（内存实现）');
        } catch (error) {
            Logger.error('Redis连接失败', error);
            this.isConnected = false;
            throw error;
        }
    }

    /**
     * 断开连接
     */
    public async disconnect(): Promise<void> {
        try {
            await this.client.flushall();
            this.isConnected = false;
            Logger.info('Redis缓存服务连接已断开');
        } catch (error) {
            Logger.error('断开Redis连接失败', error);
            throw error;
        }
    }

    /**
     * 获取Redis客户端
     */
    public getClient(): MemoryRedis {
        return this.client;
    }

    /**
     * 获取连接状态
     */
    public getConnectionStatus(): boolean {
        return this.isConnected;
    }

    /**
     * 健康检查
     */
    public async healthCheck(): Promise<boolean> {
        try {
            if (!this.isConnected) {
                return false;
            }

            // 执行简单的ping操作
            await this.client.set('health_check', 'ok', 1);
            const result = await this.client.get('health_check');
            await this.client.del('health_check');
            
            return result === 'ok';
        } catch (error) {
            Logger.error('Redis健康检查失败', error);
            return false;
        }
    }
}

/**
 * Redis连接状态枚举
 */
export enum RedisConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
}

/**
 * Redis配置选项接口
 */
export interface RedisConfigOptions {
  host?: string;
  port?: number;
  password?: string;
  db?: number;
  family?: 4 | 6;
  connectTimeout?: number;
  commandTimeout?: number;
  retryDelayOnFailover?: number;
  enableReadyCheck?: boolean;
  maxRetriesPerRequest?: number;
  lazyConnect?: boolean;
  keepAlive?: number;
  keyPrefix?: string;
}

/**
 * Redis集群配置选项接口
 */
export interface RedisClusterOptions {
  nodes: Array<{ host: string; port: number }>;
  password?: string;
  redisOptions?: RedisConfigOptions;
  enableOfflineQueue?: boolean;
  readOnly?: boolean;
  maxRedirections?: number;
  retryDelayOnFailover?: number;
  slotsRefreshTimeout?: number;
  slotsRefreshInterval?: number;
}

/**
 * Redis统计信息接口
 */
export interface RedisStats {
  connectionState: RedisConnectionState;
  connectedAt?: Date;
  lastPingAt?: Date;
  commandsExecuted: number;
  errorsCount: number;
  reconnectCount: number;
  isCluster: boolean;
  memoryUsage?: string;
  connectedClients?: number;
}

/**
 * 真实Redis配置类
 */
export class RealRedisConfig {
  private static instance: RealRedisConfig;
  private client: Redis | null = null;
  private isCluster: boolean = false;
  private connectionState: RedisConnectionState = RedisConnectionState.DISCONNECTED;
  private connectedAt: Date | undefined;
  private lastPingAt: Date | undefined;
  private commandsExecuted: number = 0;
  private errorsCount: number = 0;
  private reconnectCount: number = 0;
  private healthCheckInterval?: NodeJS.Timeout;
  private statsInterval?: NodeJS.Timeout;

  private constructor() {
    this.setupProcessHandlers();
  }

  public static getInstance(): RealRedisConfig {
    if (!RealRedisConfig.instance) {
      RealRedisConfig.instance = new RealRedisConfig();
    }
    return RealRedisConfig.instance;
  }

  /**
   * 连接到Redis（单机模式）
   */
  public async connect(options?: RedisConfigOptions): Promise<void> {
    try {
      if (this.client && this.connectionState === RedisConnectionState.CONNECTED) {
        Logger.info('Redis已经连接');
        return;
      }

      const config = this.getDefaultOptions(options);

      Logger.info('正在连接Redis...', {
        host: config.host,
        port: config.port,
        db: config.db,
      });

      this.client = new Redis(config);
      this.isCluster = false;
      this.setupEventListeners();

      // 等待连接完成
      await this.waitForConnection();

      this.connectedAt = new Date();
      this.connectionState = RedisConnectionState.CONNECTED;

      Logger.info('Redis连接成功', {
        host: config.host,
        port: config.port,
        connectedAt: this.connectedAt,
      });

      // 启动健康检查和统计
      this.startHealthCheck();
      this.startStatsCollection();

    } catch (error) {
      Logger.error('Redis连接失败', error);
      this.connectionState = RedisConnectionState.ERROR;
      this.errorsCount++;
      throw error;
    }
  }

  /**
   * 连接到Redis集群
   */
  public async connectCluster(options: RedisClusterOptions): Promise<void> {
    try {
      if (this.client && this.connectionState === RedisConnectionState.CONNECTED) {
        Logger.info('Redis集群已经连接');
        return;
      }

      Logger.info('正在连接Redis集群...', {
        nodes: options.nodes,
        nodeCount: options.nodes.length,
      });

      // 简化为单机Redis配置
      this.client = new Redis(options.redisOptions || {});

      this.isCluster = true;
      this.setupEventListeners();

      // 等待连接完成
      await this.waitForConnection();

      this.connectedAt = new Date();
      this.connectionState = RedisConnectionState.CONNECTED;

      Logger.info('Redis集群连接成功', {
        nodes: options.nodes,
        connectedAt: this.connectedAt,
      });

      // 启动健康检查和统计
      this.startHealthCheck();
      this.startStatsCollection();

    } catch (error) {
      Logger.error('Redis集群连接失败', error);
      this.connectionState = RedisConnectionState.ERROR;
      this.errorsCount++;
      throw error;
    }
  }

  /**
   * 获取默认配置选项
   */
  private getDefaultOptions(options?: RedisConfigOptions): RedisConfigOptions {
    return {
      host: process.env['REDIS_HOST'] || 'localhost',
      port: parseInt(process.env['REDIS_PORT'] || '6379'),
      password: process.env['REDIS_PASSWORD'],
      db: parseInt(process.env['REDIS_DB'] || '0'),
      family: 4,
      connectTimeout: parseInt(process.env['REDIS_CONNECT_TIMEOUT'] || '10000'),
      commandTimeout: parseInt(process.env['REDIS_COMMAND_TIMEOUT'] || '5000'),
      retryDelayOnFailover: parseInt(process.env['REDIS_RETRY_DELAY'] || '100'),
      enableReadyCheck: true,
      maxRetriesPerRequest: parseInt(process.env['REDIS_MAX_RETRIES'] || '3'),
      lazyConnect: true,
      keepAlive: parseInt(process.env['REDIS_KEEP_ALIVE'] || '30000'),
      keyPrefix: process.env['REDIS_KEY_PREFIX'] || 'idlegame:',
      ...options,
    };
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.client) return;

    this.client.on('connect', () => {
      Logger.info('Redis连接建立');
      this.connectionState = RedisConnectionState.CONNECTING;
    });

    this.client.on('ready', () => {
      Logger.info('Redis连接就绪');
      this.connectionState = RedisConnectionState.CONNECTED;
    });

    this.client.on('error', (error: any) => {
      Logger.error('Redis连接错误', error);
      this.connectionState = RedisConnectionState.ERROR;
      this.errorsCount++;
    });

    this.client.on('close', () => {
      Logger.warn('Redis连接关闭');
      this.connectionState = RedisConnectionState.DISCONNECTED;
    });

    this.client.on('reconnecting', () => {
      Logger.info('Redis正在重连');
      this.connectionState = RedisConnectionState.RECONNECTING;
      this.reconnectCount++;
    });

    this.client.on('end', () => {
      Logger.info('Redis连接结束');
      this.connectionState = RedisConnectionState.DISCONNECTED;
    });

    // 集群特有事件
    if (this.isCluster) {
      // 集群事件监听器（单机模式下不需要）
      // (this.client as any).on('node error', (error: any, node: any) => {
      //   Logger.error('Redis集群节点错误', { error, node });
      //   this.errorsCount++;
      // });

      // (this.client as any).on('+node', (node: any) => {
      //   Logger.info('Redis集群添加节点', { node });
      // });

      // (this.client as any).on('-node', (node: any) => {
      //   Logger.warn('Redis集群移除节点', { node });
      // });
    }
  }

  /**
   * 等待连接完成
   */
  private async waitForConnection(): Promise<void> {
    if (!this.client) {
      throw new Error('Redis客户端未初始化');
    }

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Redis连接超时'));
      }, 10000);

      this.client!.once('ready', () => {
        clearTimeout(timeout);
        resolve();
      });

      this.client!.once('error', (error: any) => {
        clearTimeout(timeout);
        reject(error);
      });
    });
  }

  /**
   * 断开连接
   */
  public async disconnect(): Promise<void> {
    try {
      if (!this.client) {
        Logger.info('Redis客户端未连接，无需断开');
        return;
      }

      // 停止健康检查和统计
      this.stopHealthCheck();
      this.stopStatsCollection();

      await this.client.disconnect();
      this.client = null;
      this.connectionState = RedisConnectionState.DISCONNECTED;
      this.connectedAt = undefined;

      Logger.info('Redis连接已断开');
    } catch (error) {
      Logger.error('断开Redis连接失败', error);
      throw error;
    }
  }

  /**
   * 获取Redis客户端
   */
  public getClient(): Redis | null {
    return this.client;
  }

  /**
   * 获取连接状态
   */
  public getConnectionStatus(): boolean {
    return this.connectionState === RedisConnectionState.CONNECTED;
  }

  /**
   * 获取连接状态详情
   */
  public getConnectionState(): RedisConnectionState {
    return this.connectionState;
  }

  /**
   * 获取统计信息
   */
  public getStats(): RedisStats {
    return {
      connectionState: this.connectionState,
      connectedAt: this.connectedAt,
      lastPingAt: this.lastPingAt,
      commandsExecuted: this.commandsExecuted,
      errorsCount: this.errorsCount,
      reconnectCount: this.reconnectCount,
      isCluster: this.isCluster,
    };
  }

  /**
   * 健康检查
   */
  public async healthCheck(): Promise<boolean> {
    try {
      if (!this.client || this.connectionState !== RedisConnectionState.CONNECTED) {
        return false;
      }

      const startTime = Date.now();
      await this.client.ping();
      const duration = Date.now() - startTime;

      this.lastPingAt = new Date();
      this.commandsExecuted++;

      Logger.debug('Redis健康检查成功', { duration: `${duration}ms` });
      return true;
    } catch (error) {
      Logger.error('Redis健康检查失败', error);
      this.errorsCount++;
      return false;
    }
  }

  /**
   * 启动定期健康检查
   */
  private startHealthCheck(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    const interval = parseInt(process.env['REDIS_HEALTH_CHECK_INTERVAL'] || '30000'); // 30秒
    this.healthCheckInterval = setInterval(async () => {
      const isHealthy = await this.healthCheck();
      if (!isHealthy) {
        Logger.warn('Redis健康检查失败，可能需要重连');
      }
    }, interval);
  }

  /**
   * 停止健康检查
   */
  private stopHealthCheck(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = undefined;
    }
  }

  /**
   * 启动统计信息收集
   */
  private startStatsCollection(): void {
    if (this.statsInterval) {
      clearInterval(this.statsInterval);
    }

    const interval = parseInt(process.env['REDIS_STATS_INTERVAL'] || '60000'); // 60秒
    this.statsInterval = setInterval(async () => {
      await this.collectStats();
    }, interval);
  }

  /**
   * 停止统计信息收集
   */
  private stopStatsCollection(): void {
    if (this.statsInterval) {
      clearInterval(this.statsInterval);
      this.statsInterval = undefined;
    }
  }

  /**
   * 收集统计信息
   */
  private async collectStats(): Promise<void> {
    try {
      if (!this.client || this.connectionState !== RedisConnectionState.CONNECTED) {
        return;
      }

      const info = await this.client.info('memory');
      const memoryMatch = info.match(/used_memory_human:(.+)/);
      if (memoryMatch) {
        // 更新内存使用信息
        Logger.debug('Redis内存使用', { memory: memoryMatch[1].trim() });
      }

      const clientsInfo = await this.client.info('clients');
      const clientsMatch = clientsInfo.match(/connected_clients:(\d+)/);
      if (clientsMatch) {
        Logger.debug('Redis连接客户端数', { clients: parseInt(clientsMatch[1]) });
      }

    } catch (error) {
      Logger.error('收集Redis统计信息失败', error);
      this.errorsCount++;
    }
  }

  /**
   * 获取Redis信息
   */
  public async getRedisInfo(): Promise<any> {
    try {
      if (!this.client || this.connectionState !== RedisConnectionState.CONNECTED) {
        throw new Error('Redis未连接');
      }

      const [serverInfo, memoryInfo, clientsInfo, statsInfo] = await Promise.all([
        this.client.info('server'),
        this.client.info('memory'),
        this.client.info('clients'),
        this.client.info('stats'),
      ]);

      return {
        server: this.parseInfo(serverInfo),
        memory: this.parseInfo(memoryInfo),
        clients: this.parseInfo(clientsInfo),
        stats: this.parseInfo(statsInfo),
        connectionStats: this.getStats(),
      };
    } catch (error) {
      Logger.error('获取Redis信息失败', error);
      throw error;
    }
  }

  /**
   * 解析Redis INFO命令返回的信息
   */
  private parseInfo(info: string): Record<string, string> {
    const result: Record<string, string> = {};
    const lines = info.split('\r\n');

    for (const line of lines) {
      if (line && !line.startsWith('#') && line.includes(':')) {
        const [key, value] = line.split(':');
        result[key] = value;
      }
    }

    return result;
  }

  /**
   * 设置进程处理器
   */
  private setupProcessHandlers(): void {
    // 进程退出时清理
    process.on('SIGINT', this.gracefulShutdown.bind(this));
    process.on('SIGTERM', this.gracefulShutdown.bind(this));
    process.on('SIGUSR2', this.gracefulShutdown.bind(this)); // nodemon restart
  }

  /**
   * 优雅关闭
   */
  private async gracefulShutdown(): Promise<void> {
    Logger.info('收到关闭信号，正在优雅关闭Redis连接...');
    try {
      await this.disconnect();
      process.exit(0);
    } catch (error) {
      Logger.error('优雅关闭Redis连接失败', error);
      process.exit(1);
    }
  }

  /**
   * 测试Redis连接
   */
  public async testConnection(): Promise<boolean> {
    try {
      const startTime = Date.now();
      const isHealthy = await this.healthCheck();
      const duration = Date.now() - startTime;

      Logger.info('Redis连接测试完成', {
        isHealthy,
        duration: `${duration}ms`,
        connectionState: this.connectionState,
      });

      return isHealthy;
    } catch (error) {
      Logger.error('Redis连接测试失败', error);
      return false;
    }
  }
}
