# 后端部署操作手册

## 📋 部署概述

**目标**: 实现本地开发和云服务器部署的无缝切换  
**架构**: 统一后端，多环境支持  
**部署方式**: 本地开发 + 云服务器生产  

## 🛠️ 环境准备

### 系统要求
- **Node.js**: >= 16.0.0
- **npm**: >= 8.0.0  
- **MongoDB**: >= 4.4
- **Redis**: >= 6.0 (可选)
- **操作系统**: Windows/Linux/macOS

### 依赖安装
```bash
# 进入后端目录
cd backend

# 安装依赖
npm install

# 安装开发依赖
npm install --save-dev cross-env nodemon
```

## 🔧 环境配置

### 1. 本地开发环境

**配置文件**: `backend/.env.local`
```bash
# 基础配置
NODE_ENV=development
PORT=3000
DEPLOYMENT_ENV=local

# API配置
API_BASE_URL=http://localhost:3000
CORS_ORIGIN=*

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/idlegame_dev
REDIS_HOST=localhost
REDIS_PORT=6379

# 安全配置
JWT_SECRET=local-development-jwt-secret-key
```

**切换命令**:
```bash
npm run env:local
```

### 2. 远程服务器环境

**配置文件**: `backend/.env.remote`
```bash
# 基础配置
NODE_ENV=production
PORT=3000
DEPLOYMENT_ENV=remote

# API配置 (请替换为实际服务器地址)
API_BASE_URL=https://your-aliyun-server.com
CORS_ORIGIN=*

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/idlegame_prod

# 安全配置 (请使用强密码)
JWT_SECRET=production-jwt-secret-change-this-to-strong-password
```

**切换命令**:
```bash
npm run env:remote
```

## 🚀 本地部署

### 步骤1: 环境准备
```bash
# 1. 启动MongoDB
mongod --dbpath /path/to/data

# 2. 启动Redis (可选)
redis-server

# 3. 切换到本地环境
cd backend
npm run env:local
```

### 步骤2: 启动服务
```bash
# 开发模式启动
npm run dev

# 或指定环境启动
npm run dev:local
```

### 步骤3: 验证部署
```bash
# 健康检查
curl http://localhost:3000/health

# API测试
curl http://localhost:3000/api/v1/locations/list
```

**预期输出**:
```
🌐 ========== 环境配置信息 ==========
📋 部署环境: local
🔧 Node环境: development
🚀 服务端口: 3000
🌍 API地址: http://localhost:3000
🚀 服务器启动成功
📖 API文档: http://localhost:3000/api/info
💚 健康检查: http://localhost:3000/health
🗺️ 地点配置: http://localhost:3000/api/v1/locations/list
```

## ☁️ 云服务器部署

### 步骤1: 服务器准备

**购买云服务器** (推荐配置):
- **CPU**: 2核
- **内存**: 2GB
- **存储**: 40GB SSD
- **带宽**: 3-5Mbps
- **操作系统**: Ubuntu 20.04 LTS

**安装基础环境**:
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装MongoDB
sudo apt install -y mongodb

# 安装PM2
sudo npm install -g pm2

# 安装Nginx (可选)
sudo apt install -y nginx
```

### 步骤2: 代码部署

**方法1: 手动上传**
```bash
# 本地构建
npm run deploy:build

# 上传文件
scp -r dist/ root@your-server:/opt/idlegame/
scp package.json root@your-server:/opt/idlegame/
scp .env.remote root@your-server:/opt/idlegame/.env
```

**方法2: Git部署**
```bash
# 服务器上克隆代码
git clone https://github.com/your-repo/idlegame.git /opt/idlegame
cd /opt/idlegame/backend

# 安装依赖
npm install --production

# 构建项目
npm run build

# 配置环境
cp .env.remote .env
```

### 步骤3: 配置服务

**PM2配置** (`ecosystem.config.js`):
```javascript
module.exports = {
  apps: [{
    name: 'idlegame-api',
    script: 'dist/server.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env_production: {
      NODE_ENV: 'production',
      DEPLOYMENT_ENV: 'remote',
      PORT: 3000
    }
  }]
};
```

**启动服务**:
```bash
# 启动应用
pm2 start ecosystem.config.js --env production

# 保存PM2配置
pm2 save
pm2 startup
```

### 步骤4: Nginx配置 (可选)

**Nginx配置** (`/etc/nginx/sites-available/idlegame`):
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # CORS配置
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type, Authorization";
    }
    
    location /health {
        proxy_pass http://localhost:3000/health;
    }
}
```

**启用配置**:
```bash
sudo ln -s /etc/nginx/sites-available/idlegame /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 🔄 环境切换

### 快速切换命令
```bash
# 切换到本地环境
npm run env:local

# 切换到远程环境
npm run env:remote

# 查看当前环境状态
npm run env:status
```

### 手动切换
```bash
# 使用切换脚本
node scripts/switch-env.js switch local
node scripts/switch-env.js switch remote
node scripts/switch-env.js status
```

### 环境验证
```bash
# 检查当前环境配置
npm run env:status

# 输出示例:
# 📋 当前环境状态:
#    环境: local
# 🌐 ========== 当前环境配置 ==========
# 📋 环境: local
# 🔧 Node环境: development
# 🚀 端口: 3000
# 🌍 API地址: http://localhost:3000
```

## 📊 监控和维护

### 服务状态监控
```bash
# PM2状态查看
pm2 status
pm2 logs
pm2 monit

# 系统资源监控
htop
df -h
free -m
```

### 日志管理
```bash
# 查看应用日志
pm2 logs idlegame-api

# 查看错误日志
pm2 logs idlegame-api --err

# 清理日志
pm2 flush
```

### 性能优化
```bash
# 重启应用
pm2 restart idlegame-api

# 重载应用 (零停机)
pm2 reload idlegame-api

# 扩展实例
pm2 scale idlegame-api 2
```

## 🔍 故障排除

### 常见问题

**1. 端口冲突**
```bash
# 检查端口占用
netstat -tulpn | grep :3000
lsof -i :3000

# 解决方案
# 修改.env文件中的PORT配置
```

**2. 数据库连接失败**
```bash
# 检查MongoDB状态
sudo systemctl status mongodb
mongosh --eval "db.adminCommand('ismaster')"

# 解决方案
sudo systemctl start mongodb
```

**3. 权限问题**
```bash
# 检查文件权限
ls -la /opt/idlegame/

# 修复权限
sudo chown -R $USER:$USER /opt/idlegame/
chmod +x /opt/idlegame/dist/server.js
```

**4. 内存不足**
```bash
# 检查内存使用
free -m
pm2 monit

# 解决方案
# 调整PM2配置中的max_memory_restart
```

### 调试工具
```bash
# 环境诊断
npm run env:status

# 配置验证
node -e "console.log(require('./dist/config/environment').envConfig.getEnvironmentSummary())"

# API测试
curl -v http://localhost:3000/health
curl -v http://localhost:3000/api/v1/locations/list
```

## 📋 部署检查清单

### 部署前检查
- [ ] Node.js版本 >= 16.0.0
- [ ] MongoDB服务正常运行
- [ ] 环境配置文件正确
- [ ] 依赖包完整安装
- [ ] 防火墙端口开放

### 部署后验证
- [ ] 服务启动成功
- [ ] 健康检查通过
- [ ] API接口正常响应
- [ ] 数据库连接正常
- [ ] 日志输出正常

### 生产环境检查
- [ ] HTTPS证书配置
- [ ] 域名解析正确
- [ ] 负载均衡配置
- [ ] 监控告警设置
- [ ] 备份策略实施

## 🔄 更新部署

### 代码更新流程
```bash
# 1. 本地测试
npm run env:local
npm run dev
# 测试功能正常

# 2. 构建新版本
npm run deploy:build

# 3. 备份当前版本
pm2 save
cp -r /opt/idlegame /opt/idlegame.backup

# 4. 部署新版本
scp -r dist/ root@your-server:/opt/idlegame/

# 5. 重启服务
pm2 reload idlegame-api

# 6. 验证部署
curl http://your-server/health
```

### 回滚流程
```bash
# 1. 停止当前服务
pm2 stop idlegame-api

# 2. 恢复备份
rm -rf /opt/idlegame/dist
cp -r /opt/idlegame.backup/dist /opt/idlegame/

# 3. 重启服务
pm2 start idlegame-api

# 4. 验证回滚
curl http://your-server/health
```

## 🎯 最佳实践

### 安全建议
1. **定期更新**: 保持系统和依赖包最新
2. **强密码**: 使用强密码和密钥
3. **防火墙**: 只开放必要端口
4. **备份**: 定期备份数据和配置
5. **监控**: 设置监控和告警

### 性能优化
1. **缓存策略**: 合理使用Redis缓存
2. **连接池**: 优化数据库连接池配置
3. **负载均衡**: 使用Nginx进行负载均衡
4. **CDN**: 静态资源使用CDN加速
5. **压缩**: 启用Gzip压缩

### 运维建议
1. **自动化**: 使用脚本自动化部署
2. **版本控制**: 使用Git管理代码版本
3. **文档**: 维护详细的部署文档
4. **测试**: 部署前充分测试
5. **监控**: 实时监控服务状态

## 📞 技术支持

**部署问题排查顺序**:
1. 检查环境配置 (`npm run env:status`)
2. 查看服务日志 (`pm2 logs`)
3. 测试网络连接 (`curl`)
4. 验证数据库连接
5. 检查系统资源使用

**常用命令速查**:
```bash
# 环境管理
npm run env:local    # 切换本地环境
npm run env:remote   # 切换远程环境
npm run env:status   # 查看环境状态

# 服务管理
npm run dev         # 开发模式启动
npm run start:prod  # 生产模式启动
pm2 restart all     # 重启所有服务

# 监控调试
pm2 status          # 查看服务状态
pm2 logs           # 查看日志
curl /health       # 健康检查
```
