/**
 * UI输入处理器
 * 统一处理UI相关的输入事件，包括键盘快捷键、鼠标交互等
 */

import { _decorator, Component, input, Input, EventKeyboard, EventMouse, KeyCode, Node } from 'cc';
import { UIManager } from '../../managers/UIManager';
import { UIPanelType } from '../types/UITypes';
import { EventManager } from '../../managers/EventManager';

const { ccclass } = _decorator;

/**
 * 快捷键配置接口
 */
export interface IHotkeyConfig {
    /** 快捷键码 */
    keyCode: KeyCode;
    
    /** 是否需要Ctrl */
    ctrl?: boolean;
    
    /** 是否需要Shift */
    shift?: boolean;
    
    /** 是否需要Alt */
    alt?: boolean;
    
    /** 动作类型 */
    action: string;
    
    /** 动作参数 */
    params?: any;
    
    /** 描述 */
    description?: string;
    
    /** 是否启用 */
    enabled?: boolean;
}

/**
 * 输入事件类型
 */
export enum UIInputEventType {
    HotkeyPressed = 'hotkey_pressed',
    PanelToggle = 'panel_toggle',
    MouseClick = 'mouse_click',
    MouseHover = 'mouse_hover',
    DragStart = 'drag_start',
    DragEnd = 'drag_end'
}

@ccclass('UIInputHandler')
export class UIInputHandler extends Component {
    
    // 私有属性
    private _hotkeyConfigs: Map<string, IHotkeyConfig> = new Map();
    private _isDragging: boolean = false;
    private _dragStartPos: { x: number; y: number } = { x: 0, y: 0 };
    private _dragThreshold: number = 10; // 拖拽阈值
    private _lastClickTime: number = 0;
    private _doubleClickThreshold: number = 300; // 双击时间阈值

    protected onLoad(): void {
        // 注册默认快捷键
        this.registerDefaultHotkeys();
        
        // 绑定输入事件
        this.bindInputEvents();
        
        console.log('UI输入处理器初始化完成');
    }

    protected onDestroy(): void {
        this.unbindInputEvents();
    }

    /**
     * 注册默认快捷键
     */
    private registerDefaultHotkeys(): void {
        const defaultHotkeys: IHotkeyConfig[] = [
            // 面板切换快捷键
            { keyCode: KeyCode.KEY_I, action: 'toggle_panel', params: { panelType: UIPanelType.Inventory }, description: '切换背包' },
            { keyCode: KeyCode.KEY_K, action: 'toggle_panel', params: { panelType: UIPanelType.Skills }, description: '切换技能' },
            { keyCode: KeyCode.KEY_Q, action: 'toggle_panel', params: { panelType: UIPanelType.Quests }, description: '切换任务' },
            { keyCode: KeyCode.KEY_M, action: 'toggle_panel', params: { panelType: UIPanelType.MainMenu }, description: '切换主菜单' },
            
            // 系统快捷键
            { keyCode: KeyCode.ESCAPE, action: 'escape_key', description: 'ESC键' },
            { keyCode: KeyCode.ENTER, action: 'confirm', description: '确认' },
            { keyCode: KeyCode.TAB, action: 'tab_key', description: 'Tab键' },
            
            // 功能快捷键
            { keyCode: KeyCode.KEY_F1, action: 'help', description: '帮助' },
            { keyCode: KeyCode.KEY_F5, action: 'refresh', description: '刷新' },
            { keyCode: KeyCode.KEY_F11, action: 'fullscreen', description: '全屏' },
            
            // 组合键
            { keyCode: KeyCode.KEY_S, ctrl: true, action: 'save', description: '保存' },
            { keyCode: KeyCode.KEY_Z, ctrl: true, action: 'undo', description: '撤销' },
            { keyCode: KeyCode.KEY_Y, ctrl: true, action: 'redo', description: '重做' },
            
            // 调试快捷键
            { keyCode: KeyCode.BACKQUOTE, action: 'toggle_console', description: '切换控制台' },
            { keyCode: KeyCode.KEY_F12, action: 'toggle_debug', description: '切换调试' }
        ];
        
        for (const hotkey of defaultHotkeys) {
            this.registerHotkey(hotkey);
        }
        
        console.log(`注册了 ${defaultHotkeys.length} 个默认快捷键`);
    }

    /**
     * 绑定输入事件
     */
    private bindInputEvents(): void {
        // 键盘事件
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        input.on(Input.EventType.KEY_UP, this.onKeyUp, this);
        
        // 鼠标事件
        input.on(Input.EventType.MOUSE_DOWN, this.onMouseDown, this);
        input.on(Input.EventType.MOUSE_UP, this.onMouseUp, this);
        input.on(Input.EventType.MOUSE_MOVE, this.onMouseMove, this);
        input.on(Input.EventType.MOUSE_WHEEL, this.onMouseWheel, this);
        
        // 触摸事件（移动端）
        input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
        input.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);
        input.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
    }

    /**
     * 解绑输入事件
     */
    private unbindInputEvents(): void {
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        input.off(Input.EventType.KEY_UP, this.onKeyUp, this);
        input.off(Input.EventType.MOUSE_DOWN, this.onMouseDown, this);
        input.off(Input.EventType.MOUSE_UP, this.onMouseUp, this);
        input.off(Input.EventType.MOUSE_MOVE, this.onMouseMove, this);
        input.off(Input.EventType.MOUSE_WHEEL, this.onMouseWheel, this);
        input.off(Input.EventType.TOUCH_START, this.onTouchStart, this);
        input.off(Input.EventType.TOUCH_END, this.onTouchEnd, this);
        input.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
    }

    /**
     * 键盘按下事件
     */
    private onKeyDown(event: EventKeyboard): void {
        const keyCode = event.keyCode;
        
        // 检查快捷键
        for (const [key, config] of this._hotkeyConfigs) {
            if (this.isHotkeyMatch(config, event)) {
                if (config.enabled !== false) {
                    this.executeHotkeyAction(config);
                    event.propagationStopped = true;
                    return;
                }
            }
        }
    }

    /**
     * 键盘释放事件
     */
    private onKeyUp(event: EventKeyboard): void {
        // 处理键盘释放事件
    }

    /**
     * 鼠标按下事件
     */
    private onMouseDown(event: EventMouse): void {
        const currentTime = Date.now();
        const location = event.getLocation();
        
        // 检查双击
        if (currentTime - this._lastClickTime < this._doubleClickThreshold) {
            this.onDoubleClick(event);
        }
        
        this._lastClickTime = currentTime;
        this._dragStartPos = { x: location.x, y: location.y };
        
        // 发送鼠标点击事件
        this.emitInputEvent(UIInputEventType.MouseClick, {
            button: event.getButton(),
            location: location,
            doubleClick: false
        });
    }

    /**
     * 鼠标释放事件
     */
    private onMouseUp(event: EventMouse): void {
        if (this._isDragging) {
            this._isDragging = false;
            
            // 发送拖拽结束事件
            this.emitInputEvent(UIInputEventType.DragEnd, {
                startPos: this._dragStartPos,
                endPos: event.getLocation()
            });
        }
    }

    /**
     * 鼠标移动事件
     */
    private onMouseMove(event: EventMouse): void {
        const location = event.getLocation();
        
        // 检查是否开始拖拽
        if (!this._isDragging && event.getButton() !== EventMouse.BUTTON_MISSING) {
            const deltaX = location.x - this._dragStartPos.x;
            const deltaY = location.y - this._dragStartPos.y;
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
            
            if (distance > this._dragThreshold) {
                this._isDragging = true;
                
                // 发送拖拽开始事件
                this.emitInputEvent(UIInputEventType.DragStart, {
                    startPos: this._dragStartPos,
                    currentPos: location
                });
            }
        }
        
        // 发送鼠标悬停事件
        this.emitInputEvent(UIInputEventType.MouseHover, {
            location: location,
            dragging: this._isDragging
        });
    }

    /**
     * 鼠标滚轮事件
     */
    private onMouseWheel(event: EventMouse): void {
        const scrollY = event.getScrollY();
        
        // 处理滚轮事件，可以用于缩放、滚动等
        console.log(`鼠标滚轮: ${scrollY}`);
    }

    /**
     * 触摸开始事件
     */
    private onTouchStart(event: any): void {
        // 处理触摸开始事件
        const touch = event.touch;
        const location = touch.getLocation();
        
        this._dragStartPos = { x: location.x, y: location.y };
    }

    /**
     * 触摸结束事件
     */
    private onTouchEnd(event: any): void {
        if (this._isDragging) {
            this._isDragging = false;
            
            const touch = event.touch;
            const location = touch.getLocation();
            
            // 发送拖拽结束事件
            this.emitInputEvent(UIInputEventType.DragEnd, {
                startPos: this._dragStartPos,
                endPos: location
            });
        }
    }

    /**
     * 触摸移动事件
     */
    private onTouchMove(event: any): void {
        const touch = event.touch;
        const location = touch.getLocation();
        
        // 检查是否开始拖拽
        if (!this._isDragging) {
            const deltaX = location.x - this._dragStartPos.x;
            const deltaY = location.y - this._dragStartPos.y;
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
            
            if (distance > this._dragThreshold) {
                this._isDragging = true;
                
                // 发送拖拽开始事件
                this.emitInputEvent(UIInputEventType.DragStart, {
                    startPos: this._dragStartPos,
                    currentPos: location
                });
            }
        }
    }

    /**
     * 双击事件
     */
    private onDoubleClick(event: EventMouse): void {
        console.log('双击事件');
        
        // 发送双击事件
        this.emitInputEvent(UIInputEventType.MouseClick, {
            button: event.getButton(),
            location: event.getLocation(),
            doubleClick: true
        });
    }

    /**
     * 检查快捷键是否匹配
     */
    private isHotkeyMatch(config: IHotkeyConfig, event: EventKeyboard): boolean {
        if (config.keyCode !== event.keyCode) {
            return false;
        }
        
        // 检查修饰键
        if (config.ctrl && !event.ctrlKey) return false;
        if (config.shift && !event.shiftKey) return false;
        if (config.alt && !event.altKey) return false;
        
        // 检查不需要的修饰键
        if (!config.ctrl && event.ctrlKey) return false;
        if (!config.shift && event.shiftKey) return false;
        if (!config.alt && event.altKey) return false;
        
        return true;
    }

    /**
     * 执行快捷键动作
     */
    private executeHotkeyAction(config: IHotkeyConfig): void {
        console.log(`执行快捷键动作: ${config.action}`);
        
        switch (config.action) {
            case 'toggle_panel':
                this.handleTogglePanel(config.params);
                break;
            case 'escape_key':
                this.handleEscapeKey();
                break;
            case 'confirm':
                this.handleConfirm();
                break;
            case 'tab_key':
                this.handleTabKey();
                break;
            case 'help':
                this.handleHelp();
                break;
            case 'refresh':
                this.handleRefresh();
                break;
            case 'fullscreen':
                this.handleFullscreen();
                break;
            case 'save':
                this.handleSave();
                break;
            case 'undo':
                this.handleUndo();
                break;
            case 'redo':
                this.handleRedo();
                break;
            case 'toggle_console':
                this.handleToggleConsole();
                break;
            case 'toggle_debug':
                this.handleToggleDebug();
                break;
            default:
                console.warn(`未知的快捷键动作: ${config.action}`);
                break;
        }
        
        // 发送快捷键事件
        this.emitInputEvent(UIInputEventType.HotkeyPressed, {
            action: config.action,
            params: config.params,
            keyCode: config.keyCode
        });
    }

    /**
     * 处理面板切换
     */
    private handleTogglePanel(params: any): void {
        if (params && params.panelType) {
            const uiManager = UIManager.getInstance();
            uiManager.togglePanel(params.panelType);
        }
    }

    /**
     * 处理ESC键
     */
    private handleEscapeKey(): void {
        const uiManager = UIManager.getInstance();
        if (!uiManager.handleEscapeKey()) {
            // 如果没有UI处理ESC键，可以执行默认动作
            console.log('ESC键未被处理');
        }
    }

    /**
     * 处理确认键
     */
    private handleConfirm(): void {
        console.log('确认键');
    }

    /**
     * 处理Tab键
     */
    private handleTabKey(): void {
        console.log('Tab键');
    }

    /**
     * 处理帮助
     */
    private handleHelp(): void {
        console.log('显示帮助');
    }

    /**
     * 处理刷新
     */
    private handleRefresh(): void {
        console.log('刷新');
    }

    /**
     * 处理全屏
     */
    private handleFullscreen(): void {
        console.log('切换全屏');
    }

    /**
     * 处理保存
     */
    private handleSave(): void {
        console.log('保存');
    }

    /**
     * 处理撤销
     */
    private handleUndo(): void {
        console.log('撤销');
    }

    /**
     * 处理重做
     */
    private handleRedo(): void {
        console.log('重做');
    }

    /**
     * 处理切换控制台
     */
    private handleToggleConsole(): void {
        console.log('切换控制台');
    }

    /**
     * 处理切换调试
     */
    private handleToggleDebug(): void {
        console.log('切换调试');
    }

    /**
     * 发送输入事件
     */
    private emitInputEvent(eventType: UIInputEventType, data: any): void {
        EventManager.getInstance().emit(`ui_input_${eventType}`, data);
    }

    // ==================== 公共API ====================

    /**
     * 注册快捷键
     */
    public registerHotkey(config: IHotkeyConfig): void {
        const key = this.getHotkeyKey(config);
        this._hotkeyConfigs.set(key, config);
        console.log(`注册快捷键: ${config.description} (${KeyCode[config.keyCode]})`);
    }

    /**
     * 注销快捷键
     */
    public unregisterHotkey(keyCode: KeyCode, ctrl?: boolean, shift?: boolean, alt?: boolean): void {
        const key = `${keyCode}_${!!ctrl}_${!!shift}_${!!alt}`;
        this._hotkeyConfigs.delete(key);
    }

    /**
     * 启用/禁用快捷键
     */
    public setHotkeyEnabled(keyCode: KeyCode, enabled: boolean): void {
        for (const [key, config] of this._hotkeyConfigs) {
            if (config.keyCode === keyCode) {
                config.enabled = enabled;
            }
        }
    }

    /**
     * 获取快捷键配置
     */
    public getHotkeyConfigs(): IHotkeyConfig[] {
        return Array.from(this._hotkeyConfigs.values());
    }

    /**
     * 获取快捷键键值
     */
    private getHotkeyKey(config: IHotkeyConfig): string {
        return `${config.keyCode}_${!!config.ctrl}_${!!config.shift}_${!!config.alt}`;
    }

    /**
     * 设置拖拽阈值
     */
    public setDragThreshold(threshold: number): void {
        this._dragThreshold = threshold;
    }

    /**
     * 设置双击时间阈值
     */
    public setDoubleClickThreshold(threshold: number): void {
        this._doubleClickThreshold = threshold;
    }
}
