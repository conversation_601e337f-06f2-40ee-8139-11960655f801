# IdleGame API 测试指南

> **服务地址**: http://localhost:3002 | **状态**: ✅ 可用 | **更新**: 2025-08-10

## 🚀 快速测试

### 1. 健康检查
```bash
curl http://localhost:3002/health
```

**预期响应**:
```json
{
  "status": "healthy",
  "timestamp": "2025-08-10T00:25:11.095Z",
  "uptime": "2m 30s",
  "services": {
    "database": "connected",
    "cache": "connected"
  }
}
```

### 2. API信息
```bash
curl http://localhost:3002/api/info
```

## 🔐 认证测试

### 用户注册
```bash
curl -X POST http://localhost:3002/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "confirmPassword": "password123"
  }'
```

### 用户登录
```bash
curl -X POST http://localhost:3002/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123"
  }'
```

**预期响应**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "...",
      "username": "testuser",
      "email": "<EMAIL>"
    }
  }
}
```

## 👤 用户管理测试

### 获取用户资料
```bash
curl -X GET http://localhost:3002/api/v1/users/profile \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 更新用户资料
```bash
curl -X PUT http://localhost:3002/api/v1/users/profile \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "profile": {
      "nickname": "新昵称",
      "avatar": "avatar_url"
    }
  }'
```

## 🎮 角色系统测试

### 创建角色
```bash
curl -X POST http://localhost:3002/api/v1/characters \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试角色",
    "class": "warrior"
  }'
```

### 获取角色详情
```bash
curl -X GET http://localhost:3002/api/v1/characters/CHARACTER_ID \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 更新角色信息
```bash
curl -X PUT http://localhost:3002/api/v1/characters/CHARACTER_ID \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "attributes": {
      "strength": 20,
      "agility": 15,
      "intelligence": 10
    }
  }'
```

## 🎒 物品系统测试

### 获取物品列表
```bash
curl -X GET http://localhost:3002/api/v1/items/list \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 装备物品
```bash
curl -X POST http://localhost:3002/api/v1/items/equip \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "characterId": "CHARACTER_ID",
    "itemInstanceId": "ITEM_INSTANCE_ID",
    "slot": "weapon"
  }'
```

### 卸下装备
```bash
curl -X POST http://localhost:3002/api/v1/items/unequip \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "characterId": "CHARACTER_ID",
    "slot": "weapon"
  }'
```

## 🗺️ 地点系统测试

### 获取地点列表
```bash
curl -X GET http://localhost:3002/api/v1/locations/list \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 获取地点配置
```bash
curl -X GET http://localhost:3002/api/v1/locations/config/LOCATION_ID \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 🔮 技能系统测试

### 获取技能列表
```bash
curl -X GET http://localhost:3002/api/v1/skills/list \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 学习技能
```bash
curl -X POST http://localhost:3002/api/v1/skills/learn \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "characterId": "CHARACTER_ID",
    "skillId": "SKILL_ID"
  }'
```

## ⚔️ 战斗系统测试

### 发起战斗
```bash
curl -X POST http://localhost:3002/api/v1/battle/start \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "characterId": "CHARACTER_ID",
    "targetType": "monster",
    "targetId": "MONSTER_ID"
  }'
```

### 获取战斗结果
```bash
curl -X GET http://localhost:3002/api/v1/battle/result/BATTLE_ID \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 🧪 Postman 集合

### 导入Postman集合
1. 打开Postman
2. 点击 Import
3. 选择 Link 标签
4. 输入: `http://localhost:3002/api/docs.json`
5. 点击 Continue 导入

### 环境变量设置
```json
{
  "baseUrl": "http://localhost:3002",
  "token": "YOUR_JWT_TOKEN_HERE"
}
```

## 🔍 响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 具体数据
  },
  "timestamp": "2025-08-10T00:25:11.095Z"
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误描述",
  "error": {
    "code": "ERROR_CODE",
    "details": "详细错误信息"
  },
  "timestamp": "2025-08-10T00:25:11.095Z"
}
```

## 🐛 常见错误码

| 错误码 | HTTP状态 | 说明 |
|--------|----------|------|
| 401 | Unauthorized | 未授权，需要登录 |
| 403 | Forbidden | 权限不足 |
| 404 | Not Found | 资源不存在 |
| 422 | Unprocessable Entity | 参数验证失败 |
| 500 | Internal Server Error | 服务器内部错误 |

## 📊 性能测试

### 压力测试 (使用Apache Bench)
```bash
# 健康检查压力测试
ab -n 1000 -c 10 http://localhost:3002/health

# API登录压力测试
ab -n 100 -c 5 -p login.json -T application/json http://localhost:3002/api/v1/auth/login
```

### 负载测试 (使用wrk)
```bash
# 安装wrk
# Windows: 下载预编译版本
# Linux: sudo apt-get install wrk

# 运行负载测试
wrk -t12 -c400 -d30s http://localhost:3002/health
```

## 📝 测试检查清单

### 基础功能测试
- [ ] 健康检查正常
- [ ] API信息获取正常
- [ ] Swagger文档可访问

### 认证功能测试
- [ ] 用户注册成功
- [ ] 用户登录成功
- [ ] JWT Token有效
- [ ] 权限验证正常

### 业务功能测试
- [ ] 角色创建/查询/更新
- [ ] 物品装备/卸下
- [ ] 地点配置获取
- [ ] 技能学习/升级
- [ ] 战斗系统运行

### 错误处理测试
- [ ] 参数验证错误
- [ ] 认证失败处理
- [ ] 权限不足处理
- [ ] 资源不存在处理

---

**测试指南版本**: v1.0.0  
**最后更新**: 2025-08-10 00:25  
**维护**: 后端开发团队
