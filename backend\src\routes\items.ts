import { Router } from 'express';
import { itemController, skillController } from '../controllers/ItemController';
import { validate, ValidationSchemas } from '../middleware/validation';
import { auth } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     ItemResponse:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: 物品ID
 *         id:
 *           type: string
 *           description: 物品唯一标识符
 *         name:
 *           type: string
 *           description: 物品名称
 *         description:
 *           type: string
 *           description: 物品描述
 *         type:
 *           type: string
 *           enum: [weapon, armor, accessory, consumable, material, quest, treasure, currency]
 *           description: 物品类型
 *         rarity:
 *           type: string
 *           enum: [common, uncommon, rare, epic, legendary, mythic]
 *           description: 物品稀有度
 *         value:
 *           type: number
 *           description: 物品价值
 *         stackable:
 *           type: boolean
 *           description: 是否可堆叠
 *         maxStack:
 *           type: number
 *           description: 最大堆叠数量
 *         usable:
 *           type: boolean
 *           description: 是否可使用
 *         equipable:
 *           type: boolean
 *           description: 是否可装备
 *         stats:
 *           type: object
 *           description: 物品属性
 *         requirements:
 *           type: object
 *           description: 使用要求
 *         equipSlot:
 *           type: string
 *           description: 装备槽位
 * 
 *     UserItemResponse:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: 用户物品ID
 *         userId:
 *           type: string
 *           description: 用户ID
 *         itemId:
 *           type: string
 *           description: 物品配置ID
 *         quantity:
 *           type: number
 *           description: 数量
 *         slot:
 *           type: number
 *           description: 背包位置
 *         equipped:
 *           type: boolean
 *           description: 是否已装备
 *         instanceId:
 *           type: string
 *           description: 实例ID
 *         enhanceLevel:
 *           type: number
 *           description: 强化等级
 *         durability:
 *           type: number
 *           description: 耐久度
 *         maxDurability:
 *           type: number
 *           description: 最大耐久度
 * 
 *     AddItemRequest:
 *       type: object
 *       required:
 *         - itemId
 *       properties:
 *         itemId:
 *           type: string
 *           description: 物品ID
 *         quantity:
 *           type: number
 *           minimum: 1
 *           description: 数量
 *           default: 1
 * 
 *     SkillResponse:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: 技能ID
 *         id:
 *           type: string
 *           description: 技能唯一标识符
 *         name:
 *           type: string
 *           description: 技能名称
 *         description:
 *           type: string
 *           description: 技能描述
 *         manaCost:
 *           type: number
 *           description: 法力消耗
 *         castTime:
 *           type: number
 *           description: 施法时间
 *         cooldown:
 *           type: number
 *           description: 冷却时间
 *         damageType:
 *           type: string
 *           enum: [physical, magical, true, healing]
 *           description: 伤害类型
 *         targetType:
 *           type: string
 *           enum: [self, ally, enemy, all_allies, all_enemies, all, area]
 *           description: 目标类型
 *         requirements:
 *           type: object
 *           description: 学习要求
 *         effects:
 *           type: array
 *           description: 技能效果
 * 
 *     LearnSkillRequest:
 *       type: object
 *       required:
 *         - skillId
 *       properties:
 *         skillId:
 *           type: string
 *           description: 技能ID
 */

/**
 * @swagger
 * /api/v1/items:
 *   get:
 *     summary: 获取物品配置列表
 *     tags: [Items]
 *     parameters:
 *       - $ref: '#/components/parameters/PageParam'
 *       - $ref: '#/components/parameters/LimitParam'
 *       - name: type
 *         in: query
 *         description: 物品类型过滤
 *         schema:
 *           type: string
 *           enum: [weapon, armor, accessory, consumable, material, quest, treasure, currency]
 *       - name: rarity
 *         in: query
 *         description: 稀有度过滤
 *         schema:
 *           type: string
 *           enum: [common, uncommon, rare, epic, legendary, mythic]
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PaginatedResponse'
 */
router.get('/',
  asyncHandler(itemController.getItems)
);

/**
 * @swagger
 * /api/v1/items/{itemId}:
 *   get:
 *     summary: 获取物品详情
 *     tags: [Items]
 *     parameters:
 *       - name: itemId
 *         in: path
 *         required: true
 *         description: 物品ID
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         item:
 *                           $ref: '#/components/schemas/ItemResponse'
 *       404:
 *         description: 物品不存在
 */
router.get('/:itemId',
  asyncHandler(itemController.getItem)
);

/**
 * @swagger
 * /api/v1/items/characters/{characterId}/inventory:
 *   get:
 *     summary: 获取角色背包
 *     tags: [Items]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: characterId
 *         in: path
 *         required: true
 *         description: 角色ID
 *         schema:
 *           type: string
 *           pattern: '^[0-9a-fA-F]{24}$'
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         inventory:
 *                           type: object
 *                           properties:
 *                             items:
 *                               type: array
 *                               items:
 *                                 $ref: '#/components/schemas/UserItemResponse'
 *                             maxSlots:
 *                               type: number
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         description: 角色不存在
 */
router.get('/characters/:characterId/inventory',
  auth.required,
  validate(ValidationSchemas.mongoId),
  asyncHandler(itemController.getInventory)
);

/**
 * @swagger
 * /api/v1/items/characters/{characterId}/add:
 *   post:
 *     summary: 添加物品到背包
 *     tags: [Items]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: characterId
 *         in: path
 *         required: true
 *         description: 角色ID
 *         schema:
 *           type: string
 *           pattern: '^[0-9a-fA-F]{24}$'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/AddItemRequest'
 *     responses:
 *       201:
 *         description: 添加成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         item:
 *                           $ref: '#/components/schemas/UserItemResponse'
 *       400:
 *         description: 背包已满或参数错误
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         description: 角色或物品不存在
 */
router.post('/characters/:characterId/add',
  auth.required,
  validate(ValidationSchemas.mongoId),
  validate(ValidationSchemas.addItem),
  asyncHandler(itemController.addItemToInventory)
);

/**
 * @swagger
 * /api/v1/items/characters/{characterId}/use/{instanceId}:
 *   post:
 *     summary: 使用物品
 *     tags: [Items]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: characterId
 *         in: path
 *         required: true
 *         description: 角色ID
 *         schema:
 *           type: string
 *           pattern: '^[0-9a-fA-F]{24}$'
 *       - name: instanceId
 *         in: path
 *         required: true
 *         description: 物品实例ID
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 使用成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *       400:
 *         description: 物品不可使用
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         description: 角色或物品不存在
 */
router.post('/characters/:characterId/use/:instanceId',
  auth.required,
  validate(ValidationSchemas.mongoId),
  asyncHandler(itemController.useItem)
);

/**
 * @swagger
 * /api/v1/items/characters/{characterId}/equip/{instanceId}:
 *   post:
 *     summary: 装备物品
 *     tags: [Items]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: characterId
 *         in: path
 *         required: true
 *         description: 角色ID
 *         schema:
 *           type: string
 *           pattern: '^[0-9a-fA-F]{24}$'
 *       - name: instanceId
 *         in: path
 *         required: true
 *         description: 物品实例ID
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 装备成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *       400:
 *         description: 物品不可装备或不满足要求
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         description: 角色或物品不存在
 */
router.post('/characters/:characterId/equip/:instanceId',
  auth.required,
  validate(ValidationSchemas.mongoId),
  asyncHandler(itemController.equipItem)
);

/**
 * @swagger
 * /api/v1/items/characters/{characterId}/unequip/{equipSlot}:
 *   post:
 *     summary: 卸下装备
 *     tags: [Items]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: characterId
 *         in: path
 *         required: true
 *         description: 角色ID
 *         schema:
 *           type: string
 *           pattern: '^[0-9a-fA-F]{24}$'
 *       - name: equipSlot
 *         in: path
 *         required: true
 *         description: 装备槽位
 *         schema:
 *           type: string
 *           enum: [weapon, armor, helmet, boots, gloves, accessory1, accessory2]
 *     responses:
 *       200:
 *         description: 卸下成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *       400:
 *         description: 该槽位没有装备
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         description: 角色不存在
 */
router.post('/characters/:characterId/unequip/:equipSlot',
  auth.required,
  validate(ValidationSchemas.mongoId),
  asyncHandler(itemController.unequipItem)
);

export default router;
