/**
 * 可拖拽地图容器组件
 * 提供地图的拖拽、缩放和边界限制功能
 */

import { _decorator, Component, Node, Vec3, Vec2, input, Input, EventTouch, UITransform, tween, Tween, Button, Camera, Canvas, find } from 'cc';

const { ccclass, property } = _decorator;

/**
 * 用户意图类型枚举
 */
export enum UserIntentType {
    Unknown = 0,    // 未知意图
    Click = 1,      // 点击意图
    Drag = 2,       // 拖拽意图
    Zoom = 3        // 缩放意图
}

/**
 * 意图检测配置接口
 */
export interface IIntentDetectionConfig {
    /** 是否启用智能意图检测 */
    enableIntentDetection: boolean;

    /** 意图检测延迟时间（毫秒） */
    intentDetectionDelay: number;

    /** 点击距离阈值（像素） */
    clickDistanceThreshold: number;

    /** 点击时间阈值（毫秒） */
    clickTimeThreshold: number;

    /** 拖拽开始距离阈值（像素） */
    dragStartThreshold: number;

    /** 是否启用快速拖拽检测 */
    enableFastDragDetection: boolean;

    /** 快速拖拽速度阈值（像素/毫秒） */
    fastDragVelocityThreshold: number;
}

/**
 * 拖拽配置接口
 */
export interface IDragConfig {
    /** 是否启用拖拽 */
    enableDrag: boolean;

    /** 是否启用缩放 */
    enableZoom: boolean;

    /** 拖拽阻尼系数 */
    dragDamping: number;

    /** 缩放敏感度 */
    zoomSensitivity: number;

    /** 最小缩放 */
    minScale: number;

    /** 最大缩放 */
    maxScale: number;

    /** 拖拽边界 */
    bounds: {
        left: number;
        right: number;
        top: number;
        bottom: number;
    };

    /** 启用边界限制 */
    enableBounds: boolean;

    /** 启用触摸区域检测 */
    enableTouchAreaCheck: boolean;

    /** 是否启用惯性 */
    enableInertia: boolean;

    /** 惯性阻尼 */
    inertiaDamping: number;

    /** 惯性持续时间 */
    inertiaDuration: number;

    /** 惯性最小速度阈值 */
    inertiaThreshold: number;

    /** 拖拽检测阈值（像素） */
    dragDetectionThreshold: number;

    /** 意图检测配置 */
    intentDetection: IIntentDetectionConfig;
}

@ccclass('DraggableMapContainer')
export class DraggableMapContainer extends Component {
    
    @property({ tooltip: '是否启用拖拽' })
    public enableDrag: boolean = true;
    
    @property({ tooltip: '是否启用缩放' })
    public enableZoom: boolean = true;
    
    @property({ tooltip: '拖拽阻尼系数', range: [0.1, 1.0] })
    public dragDamping: number = 0.8;
    
    @property({ tooltip: '缩放敏感度', range: [0.01, 1.0] })
    public zoomSensitivity: number = 0.1;
    
    @property({ tooltip: '最小缩放', range: [0.1, 1.0] })
    public minScale: number = 0.5;
    
    @property({ tooltip: '最大缩放', range: [1.0, 5.0] })
    public maxScale: number = 2.0;
    
    @property({ tooltip: '是否启用惯性' })
    public enableInertia: boolean = true;

    @property({ tooltip: '惯性阻尼', range: [0.1, 1.0] })
    public inertiaDamping: number = 0.95;

    @property({ tooltip: '惯性持续时间（秒）', range: [0.1, 3.0] })
    public inertiaDuration: number = 1.0;

    @property({ tooltip: '惯性最小速度阈值', range: [10, 200] })
    public inertiaThreshold: number = 50;

    @property({ tooltip: '拖拽检测阈值（像素）', range: [5, 50] })
    public dragDetectionThreshold: number = 10;

    @property({ tooltip: '是否启用智能意图检测' })
    public enableIntentDetection: boolean = true;

    @property({ tooltip: '意图检测延迟时间（毫秒）', range: [50, 300] })
    public intentDetectionDelay: number = 120;

    @property({ tooltip: '点击距离阈值（像素）', range: [3, 20] })
    public clickDistanceThreshold: number = 8;

    @property({ tooltip: '点击时间阈值（毫秒）', range: [100, 500] })
    public clickTimeThreshold: number = 250;

    // 私有属性
    private _config: IDragConfig = {
        enableDrag: true,
        enableZoom: true,
        dragDamping: 0.8,
        zoomSensitivity: 0.1,
        minScale: 0.5,
        maxScale: 2.0,
        bounds: {
            left: -500,
            right: 500,
            top: 300,
            bottom: -300
        },
        enableBounds: true,
        enableTouchAreaCheck: false, // 默认禁用触摸区域检测，避免拖拽后无法触摸的问题
        enableInertia: true,
        inertiaDamping: 0.95,
        inertiaDuration: 1.0,
        inertiaThreshold: 50,
        dragDetectionThreshold: 10,
        intentDetection: {
            enableIntentDetection: true,
            intentDetectionDelay: 80,
            clickDistanceThreshold: 5,
            clickTimeThreshold: 200,
            dragStartThreshold: 8,
            enableFastDragDetection: true,
            fastDragVelocityThreshold: 0.3
        }
    };
    
    // 拖拽状态
    private _isDragging: boolean = false;
    private _isPotentialDrag: boolean = false; // 潜在拖拽状态（触摸开始但未确认拖拽）
    private _lastTouchPosition: Vec2 = new Vec2();
    private _dragStartPosition: Vec2 = new Vec2();
    private _velocity: Vec2 = new Vec2();
    private _lastMoveTime: number = 0;
    private _dragDetectionTimer: number = 0; // 拖拽检测定时器

    // 缩放状态
    private _isZooming: boolean = false;
    private _lastTouchDistance: number = 0;

    // 惯性动画
    private _inertiaTween: Tween<Node> | null = null;

    // 状态检查定时器
    private _stateCheckTimer: number = 0;

    // 按钮输入屏蔽
    private static _isBlockingInput: boolean = false;

    // ==================== 智能意图检测相关属性 ====================

    /** 当前用户意图 */
    private _currentIntent: UserIntentType = UserIntentType.Unknown;

    /** 是否正在进行意图检测 */
    private _isDetectingIntent: boolean = false;

    /** 意图检测定时器 */
    private _intentDetectionTimer: number | null = null;

    /** 触摸开始时间 */
    private _touchStartTime: number = 0;

    /** 触摸开始位置 */
    private _touchStartPosition: Vec2 = new Vec2();

    /** 累计移动距离 */
    private _totalMovementDistance: number = 0;

    /** 最后一次移动位置 */
    private _lastMovementPosition: Vec2 = new Vec2();

    /** 是否已经确定意图 */
    private _intentConfirmed: boolean = false;

    protected onLoad(): void {
        console.log('🎮 DraggableMapContainer: 可拖拽地图容器加载');
        this.updateConfig();
        this.startStateCheck();
    }

    protected onEnable(): void {
        console.log('🎮 DraggableMapContainer: 启用，注册输入事件');
        // 重置所有状态
        this.forceResetStates();
        // 解除输入屏蔽
        DraggableMapContainer.setInputBlocking(false);
        // 设置输入事件
        this.setupInputEvents();
    }

    protected onDisable(): void {
        console.log('🎮 DraggableMapContainer: 禁用，清理输入事件');
        this.cleanupInputEvents();
        this.forceStopInertia();
        this.forceResetStates();
    }

    protected onDestroy(): void {
        console.log('🎮 DraggableMapContainer: 销毁');
        this.cleanupInputEvents(); // 双重保险
        this.stopInertia();
        this.stopStateCheck();
    }

    /**
     * 更新配置
     */
    private updateConfig(): void {
        this._config.enableDrag = this.enableDrag;
        this._config.enableZoom = this.enableZoom;
        this._config.dragDamping = this.dragDamping;
        this._config.zoomSensitivity = this.zoomSensitivity;
        this._config.minScale = this.minScale;
        this._config.maxScale = this.maxScale;
        this._config.enableInertia = this.enableInertia;
        this._config.inertiaDamping = this.inertiaDamping;
        this._config.inertiaDuration = this.inertiaDuration;
        this._config.inertiaThreshold = this.inertiaThreshold;
        this._config.dragDetectionThreshold = this.dragDetectionThreshold;

        // 更新意图检测配置
        this._config.intentDetection.enableIntentDetection = this.enableIntentDetection;
        this._config.intentDetection.intentDetectionDelay = this.intentDetectionDelay;
        this._config.intentDetection.clickDistanceThreshold = this.clickDistanceThreshold;
        this._config.intentDetection.clickTimeThreshold = this.clickTimeThreshold;
    }

    /**
     * 设置输入事件
     */
    private setupInputEvents(): void {
        // 先清理可能存在的事件监听器，避免重复注册
        this.cleanupInputEvents();

        // 使用节点事件监听，注册在捕获阶段，优先级高于Button
        this.node.on(Node.EventType.TOUCH_START, this.onTouchStartCapture, this, true);
        this.node.on(Node.EventType.TOUCH_MOVE, this.onTouchMoveCapture, this, true);
        this.node.on(Node.EventType.TOUCH_END, this.onTouchEndCapture, this, true);
        this.node.on(Node.EventType.TOUCH_CANCEL, this.onTouchEndCapture, this, true);

        // 同时保留全局监听作为备用
        input.on(Input.EventType.TOUCH_START, this.onGlobalTouchStart, this);
        input.on(Input.EventType.TOUCH_MOVE, this.onGlobalTouchMove, this);
        input.on(Input.EventType.TOUCH_END, this.onGlobalTouchEnd, this);
        input.on(Input.EventType.TOUCH_CANCEL, this.onGlobalTouchEnd, this);

        console.log('🎮 DraggableMapContainer: 输入事件设置完成（捕获阶段 + 全局监听）');
    }

    /**
     * 清理输入事件
     */
    private cleanupInputEvents(): void {
        try {
            // 清理捕获阶段监听
            if (this.node && this.node.isValid) {
                this.node.off(Node.EventType.TOUCH_START, this.onTouchStartCapture, this);
                this.node.off(Node.EventType.TOUCH_MOVE, this.onTouchMoveCapture, this);
                this.node.off(Node.EventType.TOUCH_END, this.onTouchEndCapture, this);
                this.node.off(Node.EventType.TOUCH_CANCEL, this.onTouchEndCapture, this);
            }

            // 清理全局监听
            input.off(Input.EventType.TOUCH_START, this.onGlobalTouchStart, this);
            input.off(Input.EventType.TOUCH_MOVE, this.onGlobalTouchMove, this);
            input.off(Input.EventType.TOUCH_END, this.onGlobalTouchEnd, this);
            input.off(Input.EventType.TOUCH_CANCEL, this.onGlobalTouchEnd, this);

            // 清理可能存在的节点监听（兼容性处理）
            if (this.node && this.node.isValid) {
                this.node.off(Input.EventType.TOUCH_START, this.onTouchStart, this);
                this.node.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
                this.node.off(Input.EventType.TOUCH_END, this.onTouchEnd, this);
                this.node.off(Input.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
            }

            console.log('🎮 DraggableMapContainer: 输入事件清理完成');
        } catch (error) {
            console.warn('🎮 DraggableMapContainer: 清理输入事件时出错', error);
        }
    }

    // ==================== 捕获阶段事件处理 ====================

    /**
     * 捕获阶段触摸开始（优先级高于Button）
     */
    private onTouchStartCapture(event: EventTouch): void {
        console.log('🎯 DraggableMapContainer: 捕获阶段触摸开始');

        // 设置事件穿透，让事件继续传递给Button
        event.preventSwallow = true;

        // 调用原有的全局触摸开始逻辑
        this.onGlobalTouchStart(event);
    }

    /**
     * 捕获阶段触摸移动
     */
    private onTouchMoveCapture(event: EventTouch): void {
        // 如果正在检测意图或已确认拖拽，设置事件穿透
        if (this._isDetectingIntent || this._currentIntent === UserIntentType.Drag) {
            event.preventSwallow = true;
            console.log('🎯 DraggableMapContainer: 捕获阶段触摸移动，设置穿透');
        }

        // 调用原有的全局触摸移动逻辑
        this.onGlobalTouchMove(event);
    }

    /**
     * 捕获阶段触摸结束
     */
    private onTouchEndCapture(event: EventTouch): void {
        // 如果确认为拖拽意图，设置事件穿透
        if (this._currentIntent === UserIntentType.Drag) {
            event.preventSwallow = true;
            console.log('🎯 DraggableMapContainer: 捕获阶段触摸结束，拖拽意图，设置穿透');
        }

        // 调用原有的全局触摸结束逻辑
        this.onGlobalTouchEnd(event);
    }

    // ==================== 智能意图检测核心方法 ====================

    /**
     * 开始意图检测
     */
    private startIntentDetection(event: EventTouch): void {
        if (!this._config.intentDetection.enableIntentDetection) {
            // 如果禁用意图检测，直接按原逻辑处理
            this.onTouchStart(event);
            return;
        }

        // 重置意图检测状态
        this.resetIntentDetection();

        // 记录触摸开始信息
        this._touchStartTime = Date.now();
        this._touchStartPosition.set(event.getUILocation());
        this._lastMovementPosition.set(this._touchStartPosition);
        this._totalMovementDistance = 0;
        this._isDetectingIntent = true;
        this._currentIntent = UserIntentType.Unknown;
        this._intentConfirmed = false;

        console.log('🧠 DraggableMapContainer: 开始意图检测', {
            startTime: this._touchStartTime,
            startPos: this._touchStartPosition.toString()
        });

        // 启动延迟检测定时器
        this.scheduleOnce(() => {
            this.finalizeIntentByTimeout();
        }, this._config.intentDetection.intentDetectionDelay / 1000);
        this._intentDetectionTimer = 1; // 标记定时器已启动
    }

    /**
     * 更新意图检测
     */
    private updateIntentDetection(event: EventTouch): void {
        if (!this._isDetectingIntent || this._intentConfirmed) {
            return;
        }

        const currentPos = event.getUILocation();
        const deltaDistance = Vec2.distance(this._lastMovementPosition, currentPos);
        this._totalMovementDistance += deltaDistance;
        this._lastMovementPosition.set(currentPos);

        // 计算从起始点的直线距离（更准确的拖拽判断）
        const directDistance = Vec2.distance(this._touchStartPosition, currentPos);

        // 检查是否超过拖拽阈值（使用直线距离）
        if (directDistance > this._config.intentDetection.dragStartThreshold) {
            console.log('🧠 DraggableMapContainer: 直线距离超过阈值，确认拖拽意图', {
                directDistance,
                threshold: this._config.intentDetection.dragStartThreshold
            });
            this.confirmIntent(UserIntentType.Drag, event);
            return;
        }

        // 检查快速拖拽（基于速度）
        if (this._config.intentDetection.enableFastDragDetection) {
            const currentTime = Date.now();
            const deltaTime = currentTime - this._touchStartTime;
            if (deltaTime > 30) { // 至少30ms后才计算速度，避免误判
                const velocity = directDistance / deltaTime;
                if (velocity > this._config.intentDetection.fastDragVelocityThreshold) {
                    console.log('🧠 DraggableMapContainer: 快速移动检测到，确认拖拽意图', {
                        velocity,
                        threshold: this._config.intentDetection.fastDragVelocityThreshold
                    });
                    this.confirmIntent(UserIntentType.Drag, event);
                    return;
                }
            }
        }

        // 检查连续移动模式（如果用户持续移动，很可能是拖拽）
        const currentTime = Date.now();
        if (deltaDistance > 2 && (currentTime - this._touchStartTime) > 50) {
            // 如果移动距离虽然不大，但持续在移动，也可能是拖拽意图
            const timeSinceStart = currentTime - this._touchStartTime;
            const avgVelocity = this._totalMovementDistance / timeSinceStart;
            if (avgVelocity > 0.1 && this._totalMovementDistance > 6) {
                console.log('🧠 DraggableMapContainer: 连续移动检测到，确认拖拽意图', {
                    avgVelocity,
                    totalDistance: this._totalMovementDistance
                });
                this.confirmIntent(UserIntentType.Drag, event);
                return;
            }
        }

        console.log('🧠 DraggableMapContainer: 更新意图检测', {
            directDistance,
            totalDistance: this._totalMovementDistance,
            threshold: this._config.intentDetection.dragStartThreshold,
            deltaDistance
        });
    }

    /**
     * 通过超时确定意图
     */
    private finalizeIntentByTimeout(): void {
        if (!this._isDetectingIntent || this._intentConfirmed) {
            return;
        }

        const currentTime = Date.now();
        const touchDuration = currentTime - this._touchStartTime;
        const directDistance = Vec2.distance(this._touchStartPosition, this._lastMovementPosition);

        // 更智能的意图判断逻辑
        if (directDistance <= this._config.intentDetection.clickDistanceThreshold &&
            touchDuration <= this._config.intentDetection.clickTimeThreshold &&
            this._totalMovementDistance <= this._config.intentDetection.clickDistanceThreshold * 1.5) {
            // 非常严格的点击条件：直线距离小、时间短、总移动距离也小
            this._currentIntent = UserIntentType.Click;
        } else {
            // 其他情况都倾向于拖拽意图，让用户更容易拖拽
            this._currentIntent = UserIntentType.Drag;
        }

        this._intentConfirmed = true;
        this._isDetectingIntent = false;

        console.log('🧠 DraggableMapContainer: 超时确定意图', {
            intent: UserIntentType[this._currentIntent],
            directDistance,
            totalDistance: this._totalMovementDistance,
            duration: touchDuration,
            clickThreshold: this._config.intentDetection.clickDistanceThreshold
        });

        // 根据确定的意图执行相应操作
        this.executeIntentAction();
    }

    /**
     * 确认用户意图
     */
    private confirmIntent(intent: UserIntentType, event?: EventTouch): void {
        if (this._intentConfirmed) {
            return;
        }

        this._currentIntent = intent;
        this._intentConfirmed = true;
        this._isDetectingIntent = false;

        // 取消定时器
        if (this._intentDetectionTimer !== null) {
            this.unschedule(this.finalizeIntentByTimeout);
            this._intentDetectionTimer = null;
        }

        console.log('🧠 DraggableMapContainer: 确认用户意图', {
            intent: UserIntentType[intent],
            distance: this._totalMovementDistance,
            duration: Date.now() - this._touchStartTime
        });

        // 执行相应操作
        this.executeIntentAction(event);
    }

    /**
     * 执行意图对应的操作
     */
    private executeIntentAction(event?: EventTouch): void {
        switch (this._currentIntent) {
            case UserIntentType.Click:
                this.handleClickIntent();
                break;
            case UserIntentType.Drag:
                this.handleDragIntent(event);
                break;
            case UserIntentType.Zoom:
                // 缩放意图暂时不需要特殊处理
                break;
            default:
                console.warn('🧠 DraggableMapContainer: 未知的用户意图', this._currentIntent);
                break;
        }
    }

    /**
     * 处理点击意图
     */
    private handleClickIntent(): void {
        console.log('👆 DraggableMapContainer: 处理点击意图');

        // 确保不屏蔽按钮输入
        DraggableMapContainer.setInputBlocking(false);

        // 尝试触发按钮点击
        this.triggerButtonClickAtPosition(this._touchStartPosition);
    }

    /**
     * 在指定位置触发按钮点击
     */
    private triggerButtonClickAtPosition(position: Vec2): void {
        try {
            // 查找所有可能的按钮
            const buttons = this.findButtonsAtPosition(position);

            if (buttons.length > 0) {
                console.log('🎯 DraggableMapContainer: 找到按钮，触发点击', buttons.length);

                // 触发第一个找到的按钮（通常是最上层的）
                const button = buttons[0];
                if (button && button.interactable) {
                    // 模拟按钮点击事件
                    button.node.emit(Button.EventType.CLICK);
                    console.log('✅ DraggableMapContainer: 按钮点击已触发');
                }
            } else {
                console.log('🔍 DraggableMapContainer: 在点击位置未找到按钮');
            }
        } catch (error) {
            console.warn('⚠️ DraggableMapContainer: 触发按钮点击时出错', error);
        }
    }

    /**
     * 查找指定位置的按钮
     */
    private findButtonsAtPosition(screenPosition: Vec2): Button[] {
        const buttons: Button[] = [];

        try {
            // 获取主摄像机
            const camera = find('Canvas/Main Camera')?.getComponent(Camera);
            if (!camera) {
                console.warn('🔍 DraggableMapContainer: 未找到主摄像机');
                return buttons;
            }

            // 获取Canvas
            const canvas = find('Canvas');
            if (!canvas) {
                console.warn('🔍 DraggableMapContainer: 未找到Canvas');
                return buttons;
            }

            // 递归查找所有按钮组件
            this.findButtonsInNode(canvas, screenPosition, camera, buttons);

        } catch (error) {
            console.warn('🔍 DraggableMapContainer: 查找按钮时出错', error);
        }

        return buttons;
    }

    /**
     * 在节点中递归查找按钮
     */
    private findButtonsInNode(node: Node, screenPosition: Vec2, camera: Camera, buttons: Button[]): void {
        if (!node || !node.active) return;

        // 检查当前节点是否有Button组件
        const button = node.getComponent(Button);
        if (button && button.interactable) {
            // 检查点击位置是否在按钮范围内
            if (this.isPositionInButton(node, screenPosition, camera)) {
                buttons.push(button);
            }
        }

        // 递归检查子节点
        for (const child of node.children) {
            this.findButtonsInNode(child, screenPosition, camera, buttons);
        }
    }

    /**
     * 检查位置是否在按钮范围内
     */
    private isPositionInButton(buttonNode: Node, screenPosition: Vec2, camera: Camera): boolean {
        try {
            const uiTransform = buttonNode.getComponent(UITransform);
            if (!uiTransform) return false;

            // 将屏幕坐标转换为节点本地坐标
            const worldPos = camera.screenToWorld(new Vec3(screenPosition.x, screenPosition.y, 0));
            const localPos = new Vec3();
            buttonNode.inverseTransformPoint(localPos, worldPos);

            // 检查是否在按钮范围内
            const size = uiTransform.contentSize;
            const anchorPoint = uiTransform.anchorPoint;

            const left = -size.width * anchorPoint.x;
            const right = size.width * (1 - anchorPoint.x);
            const bottom = -size.height * anchorPoint.y;
            const top = size.height * (1 - anchorPoint.y);

            return localPos.x >= left && localPos.x <= right &&
                   localPos.y >= bottom && localPos.y <= top;

        } catch (error) {
            console.warn('🔍 DraggableMapContainer: 检查按钮位置时出错', error);
            return false;
        }
    }

    /**
     * 处理拖拽意图
     */
    private handleDragIntent(event?: EventTouch): void {
        console.log('👋 DraggableMapContainer: 处理拖拽意图');

        if (event) {
            // 模拟触摸开始事件来启动拖拽
            this.onTouchStart(event);
        }
    }

    /**
     * 重置意图检测状态
     */
    private resetIntentDetection(): void {
        this._isDetectingIntent = false;
        this._currentIntent = UserIntentType.Unknown;
        this._intentConfirmed = false;
        this._totalMovementDistance = 0;
        this._touchStartTime = 0;

        // 取消定时器
        if (this._intentDetectionTimer !== null) {
            this.unschedule(this.finalizeIntentByTimeout);
            this._intentDetectionTimer = null;
        }
    }



    /**
     * 触摸开始
     */
    private onTouchStart(event: EventTouch): void {
        if (!this._config.enableDrag) return;

        const touches = event.getAllTouches();

        // 立即停止惯性动画和重置状态
        this.forceStopInertia();
        this.forceResetStates();

        console.log('🎮 DraggableMapContainer: 触摸开始，强制停止惯性动画');

        if (touches.length === 1) {
            // 单点触摸 - 拖拽
            this.startDrag(event);
        } else if (touches.length === 2 && this._config.enableZoom) {
            // 双点触摸 - 缩放
            this.startZoom(touches);
        }
    }

    /**
     * 触摸移动
     */
    private onTouchMove(event: EventTouch): void {
        const touches = event.getAllTouches();
        
        if (touches.length === 1 && this._isDragging) {
            // 单点拖拽
            this.updateDrag(event);
        } else if (touches.length === 2 && this._isZooming) {
            // 双点缩放
            this.updateZoom(touches);
        }
    }

    /**
     * 触摸结束
     */
    private onTouchEnd(event: EventTouch): void {
        const touches = event.getAllTouches();

        try {
            if (touches.length === 0) {
                // 所有触摸结束
                if (this._isDragging) {
                    this.endDrag();
                }
                if (this._isZooming) {
                    this.endZoom();
                }
                // 确保状态完全重置
                this.scheduleOnce(() => {
                    this.resetStates();
                }, 0.1);
            } else if (touches.length === 1) {
                // 从双点变为单点
                if (this._isZooming) {
                    this.endZoom();
                    this.startDrag(event);
                }
            }
        } catch (error) {
            console.warn('🎮 DraggableMapContainer: 触摸结束处理异常', error);
            this.forceResetStates();
        }
    }

    /**
     * 开始拖拽
     */
    private startDrag(event: EventTouch): void {
        // 确保完全清理之前的状态
        this.forceStopInertia();
        this.forceResetStates();

        this._isDragging = true;
        this._lastTouchPosition = event.getUILocation();
        this._dragStartPosition.set(this._lastTouchPosition);
        this._velocity.set(0, 0);
        this._lastMoveTime = Date.now();

        // 屏蔽按钮输入
        DraggableMapContainer.setInputBlocking(true);

        console.log('🎮 DraggableMapContainer: 开始拖拽，屏蔽按钮输入');
    }

    /**
     * 更新拖拽
     */
    private updateDrag(event: EventTouch): void {
        const currentPosition = event.getUILocation();
        const deltaX = (currentPosition.x - this._lastTouchPosition.x) * this._config.dragDamping;
        const deltaY = (currentPosition.y - this._lastTouchPosition.y) * this._config.dragDamping;

        // 更新位置（2D游戏只需要X,Y）
        const currentNodePosition = this.node.getPosition();
        const newPosition = new Vec3(
            currentNodePosition.x + deltaX,
            currentNodePosition.y + deltaY,
            currentNodePosition.z
        );
        
        // 应用边界限制
        this.applyBounds(newPosition);
        this.node.setPosition(newPosition);
        
        // 计算速度（用于惯性）
        const currentTime = Date.now();
        const deltaTime = (currentTime - this._lastMoveTime) / 1000;
        if (deltaTime > 0) {
            this._velocity.set(deltaX / deltaTime, deltaY / deltaTime);
        }
        
        this._lastTouchPosition = currentPosition;
        this._lastMoveTime = currentTime;
    }

    /**
     * 结束拖拽
     */
    private endDrag(): void {
        this._isDragging = false;

        // 启动惯性动画
        if (this._config.enableInertia && this._velocity.length() > this._config.inertiaThreshold) {
            this.startInertia();
        } else {
            // 如果没有惯性动画，立即解除输入屏蔽
            DraggableMapContainer.setInputBlocking(false);
        }

        console.log('🎮 DraggableMapContainer: 结束拖拽');
    }

    /**
     * 开始缩放
     */
    private startZoom(touches: any[]): void {
        this._isZooming = true;
        this._lastTouchDistance = this.getTouchDistance(touches);
        
        console.log('🎮 DraggableMapContainer: 开始缩放');
    }

    /**
     * 更新缩放
     */
    private updateZoom(touches: any[]): void {
        const currentDistance = this.getTouchDistance(touches);
        const deltaDistance = currentDistance - this._lastTouchDistance;
        
        if (Math.abs(deltaDistance) > 5) { // 防止微小变化
            const scaleChange = deltaDistance * this._config.zoomSensitivity * 0.01;
            const currentScale = this.node.getScale().x;
            const newScale = Math.max(this._config.minScale, 
                            Math.min(this._config.maxScale, currentScale + scaleChange));
            
            this.node.setScale(newScale, newScale, 1);
            this._lastTouchDistance = currentDistance;
        }
    }

    /**
     * 结束缩放
     */
    private endZoom(): void {
        this._isZooming = false;
        console.log('🎮 DraggableMapContainer: 结束缩放');
    }

    // ==================== 全局触摸事件处理 ====================

    /**
     * 全局触摸开始
     */
    private onGlobalTouchStart(event: EventTouch): void {
        const touchPos = event.getLocation();

        // 可选的触摸区域检测（默认禁用以避免拖拽后无法触摸的问题）
        if (this._config.enableTouchAreaCheck && !this.isPointInDragArea(touchPos)) {
            console.log('🌐 DraggableMapContainer: 触摸点不在拖拽区域内，忽略');
            return;
        }

        console.log('🌐 DraggableMapContainer: 全局触摸开始，打断惯性动画');

        // 立即强制停止任何现有状态
        this.forceStopInertia();
        this.forceResetStates();

        // 使用智能意图检测
        this.startIntentDetection(event);
    }

    /**
     * 全局触摸移动
     */
    private onGlobalTouchMove(event: EventTouch): void {
        // 如果正在进行意图检测，更新检测状态
        if (this._isDetectingIntent) {
            this.updateIntentDetection(event);
            return;
        }

        // 只有在有活动拖拽或缩放时才处理
        if (this._isDragging || this._isZooming) {
            this.onTouchMove(event);
        }
    }

    /**
     * 全局触摸结束
     */
    private onGlobalTouchEnd(event: EventTouch): void {
        // 如果正在进行意图检测，立即确定意图
        if (this._isDetectingIntent && !this._intentConfirmed) {
            const touchDuration = Date.now() - this._touchStartTime;

            // 根据当前状态判断意图
            if (this._totalMovementDistance <= this._config.intentDetection.clickDistanceThreshold &&
                touchDuration <= this._config.intentDetection.clickTimeThreshold) {
                this.confirmIntent(UserIntentType.Click);
            } else {
                this.confirmIntent(UserIntentType.Drag, event);
            }
            return;
        }

        // 只有在有活动拖拽或缩放时才处理
        if (this._isDragging || this._isZooming) {
            console.log('🌐 DraggableMapContainer: 全局触摸结束');
            this.onTouchEnd(event);
        }
    }

    /**
     * 检查触摸点是否在有效的拖拽区域内（基于屏幕区域）
     */
    private isPointInDragArea(touchPos: Vec2): boolean {
        try {
            // 获取父容器（通常是Canvas或主UI容器）的范围
            const parentNode = this.node.parent;
            if (!parentNode) {
                console.log('🌐 DraggableMapContainer: 没有父节点，允许所有触摸');
                return true;
            }

            const parentTransform = parentNode.getComponent(UITransform);
            if (!parentTransform) {
                console.log('🌐 DraggableMapContainer: 父节点没有UITransform，允许所有触摸');
                return true;
            }

            // 将屏幕坐标转换为父节点的本地坐标
            const worldPos = new Vec3(touchPos.x, touchPos.y, 0);
            const localPos = new Vec3();
            parentTransform.convertToNodeSpaceAR(worldPos, localPos);

            // 检查是否在父容器范围内
            const size = parentTransform.contentSize;
            const anchorPoint = parentTransform.anchorPoint;

            const minX = -size.width * anchorPoint.x;
            const maxX = size.width * (1 - anchorPoint.x);
            const minY = -size.height * anchorPoint.y;
            const maxY = size.height * (1 - anchorPoint.y);

            const inArea = localPos.x >= minX && localPos.x <= maxX &&
                          localPos.y >= minY && localPos.y <= maxY;

            console.log('🌐 DraggableMapContainer: 触摸检测', {
                touchPos: touchPos,
                localPos: localPos,
                parentSize: size,
                inArea: inArea
            });

            return inArea;
        } catch (error) {
            console.warn('🌐 DraggableMapContainer: 拖拽区域检测失败', error);
            return true; // 出错时默认允许
        }
    }



    /**
     * 获取两点间距离
     */
    private getTouchDistance(touches: any[]): number {
        if (touches.length < 2) return 0;
        
        const touch1 = touches[0].getUILocation();
        const touch2 = touches[1].getUILocation();
        
        return Math.sqrt(
            Math.pow(touch2.x - touch1.x, 2) + 
            Math.pow(touch2.y - touch1.y, 2)
        );
    }

    /**
     * 应用边界限制（考虑缩放因子）
     */
    private applyBounds(position: Vec3): void {
        if (!this._config.enableBounds) {
            return; // 如果禁用边界限制，直接返回
        }

        // 获取当前缩放
        const currentScale = this.node.getScale();
        const scaleX = currentScale.x;
        const scaleY = currentScale.y;

        // 计算动态边界
        const dynamicBounds = this.calculateDynamicBounds(scaleX, scaleY);

        // 应用边界限制
        const originalX = position.x;
        const originalY = position.y;

        position.x = Math.max(dynamicBounds.left,
                    Math.min(dynamicBounds.right, position.x));
        position.y = Math.max(dynamicBounds.bottom,
                    Math.min(dynamicBounds.top, position.y));

        // 只在位置被限制时输出日志
        if (originalX !== position.x || originalY !== position.y) {
            console.log('🔒 DraggableMapContainer: 位置被边界限制', {
                original: { x: originalX, y: originalY },
                limited: { x: position.x, y: position.y },
                scale: { x: scaleX, y: scaleY },
                bounds: dynamicBounds
            });
        }
    }

    /**
     * 计算动态边界（根据缩放和内容大小）
     */
    private calculateDynamicBounds(scaleX: number, scaleY: number): any {
        // 获取容器大小
        const containerTransform = this.node.getComponent(UITransform);
        const mapContainer = this.node.getChildByName('MapContainer');

        if (!containerTransform || !mapContainer) {
            // 如果没有UITransform或地图容器，使用配置的固定边界
            return {
                left: this._config.bounds.left,
                right: this._config.bounds.right,
                top: this._config.bounds.top,
                bottom: this._config.bounds.bottom
            };
        }

        // 获取地图内容大小
        const mapTransform = mapContainer.getComponent(UITransform);
        if (!mapTransform) {
            // 如果地图容器没有UITransform，使用固定边界
            return {
                left: this._config.bounds.left,
                right: this._config.bounds.right,
                top: this._config.bounds.top,
                bottom: this._config.bounds.bottom
            };
        }

        const containerSize = containerTransform.contentSize;
        const mapSize = mapTransform.contentSize;
        const scaledMapWidth = mapSize.width * scaleX;
        const scaledMapHeight = mapSize.height * scaleY;

        // 计算可移动范围
        // 当地图小于容器时，限制在中心区域
        // 当地图大于容器时，允许移动以显示所有内容
        const maxOffsetX = Math.max(0, (scaledMapWidth - containerSize.width) / 2);
        const maxOffsetY = Math.max(0, (scaledMapHeight - containerSize.height) / 2);

        return {
            left: -maxOffsetX,
            right: maxOffsetX,
            top: maxOffsetY,
            bottom: -maxOffsetY
        };
    }

    /**
     * 开始惯性动画
     */
    private startInertia(): void {
        // 强制停止任何现有的惯性动画
        this.forceStopInertia();

        const startPosition = this.node.getPosition();
        const targetPosition = new Vec3(
            startPosition.x + this._velocity.x * 0.5,
            startPosition.y + this._velocity.y * 0.5,
            startPosition.z
        );

        // 应用边界限制
        this.applyBounds(targetPosition);

        console.log('🎮 DraggableMapContainer: 开始惯性动画', {
            duration: this._config.inertiaDuration,
            threshold: this._config.inertiaThreshold,
            velocity: this._velocity.length()
        });

        this._inertiaTween = tween(this.node)
            .to(this._config.inertiaDuration, { position: targetPosition }, {
                easing: 'quadOut'
            })
            .call(() => {
                // 惯性动画完成后清理引用和解除输入屏蔽
                this._inertiaTween = null;
                DraggableMapContainer.setInputBlocking(false);
                console.log('🎮 DraggableMapContainer: 惯性动画完成，解除输入屏蔽');
            })
            .start();
    }

    /**
     * 停止惯性动画
     */
    private stopInertia(): void {
        if (this._inertiaTween) {
            this._inertiaTween.stop();
            this._inertiaTween = null;
        }
    }

    /**
     * 强制停止惯性动画（用于用户打断）
     */
    private forceStopInertia(): void {
        if (this._inertiaTween) {
            this._inertiaTween.stop();
            this._inertiaTween = null;
            // 立即解除输入屏蔽
            DraggableMapContainer.setInputBlocking(false);
            console.log('🎮 DraggableMapContainer: 强制停止惯性动画，解除输入屏蔽');
        }
    }

    /**
     * 重置所有状态
     */
    private resetStates(): void {
        // 只在没有活动触摸时重置状态
        if (!this._isDragging && !this._isZooming) {
            this._velocity.set(0, 0);
            this._lastMoveTime = 0;
        }

        // 重置意图检测状态
        this.resetIntentDetection();
    }

    /**
     * 强制重置所有状态（用于异常情况）
     */
    private forceResetStates(): void {
        console.warn('🎮 DraggableMapContainer: 强制重置状态');
        this._isDragging = false;
        this._isZooming = false;
        this._velocity.set(0, 0);
        this._lastMoveTime = 0;

        // 强制重置意图检测状态
        this.resetIntentDetection();
        this.stopInertia();
    }

    // ==================== 公共API ====================

    /**
     * 设置意图检测配置
     */
    public setIntentDetectionConfig(config: Partial<IIntentDetectionConfig>): void {
        Object.assign(this._config.intentDetection, config);
        this.updateConfig();

        console.log('🧠 DraggableMapContainer: 意图检测配置已更新', this._config.intentDetection);
    }

    /**
     * 获取意图检测配置
     */
    public getIntentDetectionConfig(): IIntentDetectionConfig {
        return { ...this._config.intentDetection };
    }

    /**
     * 启用/禁用智能意图检测
     */
    public setIntentDetectionEnabled(enabled: boolean): void {
        this._config.intentDetection.enableIntentDetection = enabled;
        this.enableIntentDetection = enabled;

        console.log('🧠 DraggableMapContainer: 智能意图检测', enabled ? '已启用' : '已禁用');
    }

    /**
     * 检查当前用户意图
     */
    public getCurrentIntent(): UserIntentType {
        return this._currentIntent;
    }

    /**
     * 检查是否正在进行意图检测
     */
    public isDetectingIntent(): boolean {
        return this._isDetectingIntent;
    }

    /**
     * 手动确认用户意图（用于调试或特殊情况）
     */
    public forceConfirmIntent(intent: UserIntentType): void {
        if (this._isDetectingIntent) {
            this.confirmIntent(intent);
        }
    }

    /**
     * 设置拖拽配置
     */
    public setConfig(config: Partial<IDragConfig>): void {
        this._config = { ...this._config, ...config };
        this.updateConfig();
    }

    /**
     * 设置边界
     */
    public setBounds(left: number, right: number, top: number, bottom: number): void {
        this._config.bounds = { left, right, top, bottom };
        console.log('🔒 DraggableMapContainer: 边界已更新', this._config.bounds);
    }

    /**
     * 启用/禁用边界限制
     */
    public setBoundsEnabled(enabled: boolean): void {
        this._config.enableBounds = enabled;
        console.log('🔒 DraggableMapContainer: 边界限制', enabled ? '启用' : '禁用');
    }

    /**
     * 启用/禁用触摸区域检测
     */
    public setTouchAreaCheckEnabled(enabled: boolean): void {
        this._config.enableTouchAreaCheck = enabled;
        console.log('👆 DraggableMapContainer: 触摸区域检测', enabled ? '启用' : '禁用');
    }

    /**
     * 设置惯性参数
     */
    public setInertiaSettings(settings: {
        enabled?: boolean;
        damping?: number;
        duration?: number;
        threshold?: number;
    }): void {
        if (settings.enabled !== undefined) {
            this.enableInertia = settings.enabled;
            this._config.enableInertia = settings.enabled;
        }
        if (settings.damping !== undefined) {
            this.inertiaDamping = Math.max(0.1, Math.min(1.0, settings.damping));
            this._config.inertiaDamping = this.inertiaDamping;
        }
        if (settings.duration !== undefined) {
            this.inertiaDuration = Math.max(0.1, Math.min(3.0, settings.duration));
            this._config.inertiaDuration = this.inertiaDuration;
        }
        if (settings.threshold !== undefined) {
            this.inertiaThreshold = Math.max(10, Math.min(200, settings.threshold));
            this._config.inertiaThreshold = this.inertiaThreshold;
        }

        console.log('🎮 DraggableMapContainer: 惯性设置已更新', {
            enabled: this._config.enableInertia,
            damping: this._config.inertiaDamping,
            duration: this._config.inertiaDuration,
            threshold: this._config.inertiaThreshold
        });
    }

    /**
     * 获取惯性设置
     */
    public getInertiaSettings(): any {
        return {
            enabled: this._config.enableInertia,
            damping: this._config.inertiaDamping,
            duration: this._config.inertiaDuration,
            threshold: this._config.inertiaThreshold
        };
    }

    /**
     * 获取当前边界设置
     */
    public getBounds(): any {
        const currentScale = this.node.getScale();
        return {
            enabled: this._config.enableBounds,
            originalBounds: this._config.bounds,
            dynamicBounds: this.calculateDynamicBounds(currentScale.x, currentScale.y),
            currentScale: { x: currentScale.x, y: currentScale.y }
        };
    }

    /**
     * 获取组件状态信息（调试用）
     */
    public getDebugInfo(): any {
        return {
            componentEnabled: this.enabled,
            nodeValid: this.node && this.node.isValid,
            nodeActive: this.node ? this.node.active : false,
            isDragging: this._isDragging,
            isZooming: this._isZooming,
            hasInertia: this._inertiaTween !== null,
            inputBlocked: DraggableMapContainer.isInputBlocked(),
            position: this.node ? this.node.getPosition() : null,
            scale: this.node ? this.node.getScale() : null,
            config: this._config
        };
    }

    /**
     * 移动到指定位置
     */
    public moveTo(position: Vec3, duration: number = 0.5): void {
        this.stopInertia();
        this.forceResetStates();

        const targetPosition = new Vec3(position);
        this.applyBounds(targetPosition);

        if (duration > 0) {
            tween(this.node)
                .to(duration, { position: targetPosition }, {
                    easing: 'quadInOut'
                })
                .start();
        } else {
            this.node.setPosition(targetPosition);
        }
    }

    /**
     * 手动重置拖拽状态（公共API）
     */
    public resetDragState(): void {
        console.log('🎮 DraggableMapContainer: 手动重置拖拽状态');
        this.forceStopInertia();
        this.forceResetStates();
        // 确保解除输入屏蔽
        DraggableMapContainer.setInputBlocking(false);
    }

    /**
     * 手动打断惯性动画（公共API）
     */
    public interruptInertia(): void {
        console.log('🎮 DraggableMapContainer: 手动打断惯性动画');
        this.forceStopInertia();
    }

    /**
     * 检查是否正在进行惯性动画
     */
    public isInertiaActive(): boolean {
        return this._inertiaTween !== null;
    }

    // ==================== 静态输入屏蔽管理 ====================

    /**
     * 设置输入屏蔽状态
     */
    public static setInputBlocking(blocking: boolean): void {
        DraggableMapContainer._isBlockingInput = blocking;
        console.log('🚫 DraggableMapContainer: 输入屏蔽', blocking ? '开启' : '关闭');
    }

    /**
     * 检查是否正在屏蔽输入
     */
    public static isInputBlocked(): boolean {
        return DraggableMapContainer._isBlockingInput;
    }

    /**
     * 强制解除输入屏蔽
     */
    public static clearInputBlocking(): void {
        DraggableMapContainer._isBlockingInput = false;
        console.log('🚫 DraggableMapContainer: 强制解除输入屏蔽');
    }

    /**
     * 开始状态检查定时器
     */
    private startStateCheck(): void {
        this._stateCheckTimer = setInterval(() => {
            this.checkAndCleanStates();
        }, 2000); // 每2秒检查一次
    }

    /**
     * 停止状态检查定时器
     */
    private stopStateCheck(): void {
        if (this._stateCheckTimer) {
            clearInterval(this._stateCheckTimer);
            this._stateCheckTimer = 0;
        }
    }

    /**
     * 检查并清理异常状态
     */
    private checkAndCleanStates(): void {
        const now = Date.now();

        // 如果拖拽状态持续时间过长（超过5秒），强制重置
        if (this._isDragging && this._lastMoveTime > 0 && (now - this._lastMoveTime) > 5000) {
            console.warn('🎮 DraggableMapContainer: 检测到拖拽状态异常，自动重置');
            this.forceResetStates();
        }

        // 如果缩放状态持续时间过长，强制重置
        if (this._isZooming && this._lastMoveTime > 0 && (now - this._lastMoveTime) > 5000) {
            console.warn('🎮 DraggableMapContainer: 检测到缩放状态异常，自动重置');
            this.forceResetStates();
        }
    }

    /**
     * 缩放到指定比例
     */
    public scaleTo(scale: number, duration: number = 0.3): void {
        scale = Math.max(this._config.minScale, Math.min(this._config.maxScale, scale));
        
        if (duration > 0) {
            tween(this.node)
                .to(duration, { scale: new Vec3(scale, scale, 1) }, {
                    easing: 'quadInOut'
                })
                .start();
        } else {
            this.node.setScale(scale, scale, 1);
        }
    }
}
