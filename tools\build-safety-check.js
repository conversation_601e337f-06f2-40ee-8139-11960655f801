/**
 * Copyright (c) 2025 "放置". All rights reserved.
 * 构建安全检查工具 - 确保策划配置不会被意外打包
 */

const fs = require('fs');
const path = require('path');

class BuildSafetyChecker {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.buildPaths = [
      'build/wechatgame',
      'build/bytedance-mini-game',
      'build/web-desktop',
      'build/web-mobile'
    ];
    
    // 不应该被打包的目录
    this.excludePaths = [
      'tools/',
      'backend/',
      'node_modules/',
      'temp/',
      'library/',
      '.git/',
      'docs/',
      'scripts/',
      'plans/'
    ];
  }

  /**
   * 运行安全检查
   */
  async runSafetyCheck() {
    console.log('🔒 开始构建安全检查...\n');
    
    let hasIssues = false;
    
    // 检查每个构建目录
    for (const buildPath of this.buildPaths) {
      const fullBuildPath = path.join(this.projectRoot, buildPath);
      
      if (fs.existsSync(fullBuildPath)) {
        console.log(`📦 检查构建目录: ${buildPath}`);
        const issues = this.checkBuildDirectory(fullBuildPath);
        
        if (issues.length > 0) {
          hasIssues = true;
          console.log(`❌ 发现问题:`);
          issues.forEach(issue => console.log(`  - ${issue}`));
        } else {
          console.log(`✅ 构建目录安全`);
        }
        console.log('');
      }
    }
    
    // 检查配置文件
    this.checkConfigFiles();
    
    // 总结
    if (hasIssues) {
      console.log('🚨 构建安全检查失败！请修复上述问题。');
      process.exit(1);
    } else {
      console.log('🎉 构建安全检查通过！策划配置不会被意外打包。');
    }
  }

  /**
   * 检查构建目录
   */
  checkBuildDirectory(buildPath) {
    const issues = [];
    
    try {
      const items = fs.readdirSync(buildPath);
      
      for (const item of items) {
        const itemPath = path.join(buildPath, item);
        const stat = fs.statSync(itemPath);

        // 只检查目录，忽略文件
        if (stat.isDirectory()) {
          // 检查是否包含不应该存在的目录
          if (this.excludePaths.some(excludePath =>
            item === excludePath.replace('/', '') ||
            item.startsWith(excludePath.replace('/', ''))
          )) {
            issues.push(`不应包含目录: ${item}`);
          }

          // 检查是否包含策划配置目录
          if (item.includes('planning-configs') ||
              item.includes('excel-tables')) {
            issues.push(`不应包含策划目录: ${item}`);
          }
        } else {
          // 检查是否包含策划配置文件
          if (item.endsWith('.xlsx') || item.includes('planning')) {
            issues.push(`不应包含策划文件: ${item}`);
          }
        }
      }
    } catch (error) {
      // 构建目录不存在是正常的
    }
    
    return issues;
  }

  /**
   * 检查配置文件
   */
  checkConfigFiles() {
    console.log('📋 检查配置文件...');
    
    // 检查tsconfig.json
    const tsconfigPath = path.join(this.projectRoot, 'tsconfig.json');
    if (fs.existsSync(tsconfigPath)) {
      const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'));
      const excludes = tsconfig.exclude || [];
      
      if (excludes.includes('tools')) {
        console.log('✅ tsconfig.json 正确排除了 tools 目录');
      } else {
        console.log('⚠️ 建议在 tsconfig.json 中添加 "tools" 到 exclude 列表');
      }
    }
    
    // 检查.gitignore
    const gitignorePath = path.join(this.projectRoot, '.gitignore');
    if (fs.existsSync(gitignorePath)) {
      const gitignore = fs.readFileSync(gitignorePath, 'utf8');
      
      if (gitignore.includes('/tools/') || gitignore.includes('tools/')) {
        console.log('✅ .gitignore 包含 tools 目录规则');
      } else {
        console.log('💡 可以考虑在 .gitignore 中添加 tools 目录规则');
      }
    }
    
    console.log('');
  }

  /**
   * 创建安全的目录结构
   */
  createSafeStructure() {
    console.log('🏗️ 创建安全的策划配置目录结构...\n');
    
    const directories = [
      'tools/planning-configs/excel-tables',
      'tools/planning-configs/conversion-tools',
      'tools/planning-configs/templates',
      'tools/planning-configs/docs'
    ];
    
    directories.forEach(dir => {
      const fullPath = path.join(this.projectRoot, dir);
      if (!fs.existsSync(fullPath)) {
        fs.mkdirSync(fullPath, { recursive: true });
        console.log(`✅ 创建目录: ${dir}`);
      } else {
        console.log(`📁 目录已存在: ${dir}`);
      }
    });
    
    // 创建README文件
    const readmePath = path.join(this.projectRoot, 'tools/planning-configs/README.md');
    if (!fs.existsSync(readmePath)) {
      const readmeContent = `# 策划配置工作区

这个目录专门用于策划同事们编辑游戏配置，不会被打包到游戏中。

## 目录说明
- \`excel-tables/\` - Excel配置表工作区
- \`conversion-tools/\` - 配置转换工具
- \`templates/\` - Excel模板文件
- \`docs/\` - 说明文档

## 安全保障
- 此目录在构建时会被自动排除
- 不会影响游戏打包和部署
- 可以安全地进行配置编辑和版本控制
`;
      
      fs.writeFileSync(readmePath, readmeContent);
      console.log('✅ 创建说明文档: tools/planning-configs/README.md');
    }
    
    console.log('\n🎉 安全目录结构创建完成！');
  }
}

// 命令行参数处理
const args = process.argv.slice(2);

if (args.includes('--create-structure')) {
  const checker = new BuildSafetyChecker();
  checker.createSafeStructure();
} else {
  const checker = new BuildSafetyChecker();
  checker.runSafetyCheck();
}
