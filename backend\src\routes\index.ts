import express = require('express');
import { authRoutes } from './auth';
import userRoutes from './users';
import { gameRoutes } from './game';
import characterRoutes from './characters';
import itemRoutes from './items';
import skillRoutes from './skills';
import { battleRoutes } from './battle';
import { socialRoutes } from './social';
import { locationRoutes } from './locations';
import healthRoutes from './health';
import { Logger } from '../utils/logger';

/**
 * 路由配置接口
 */
export interface RouteConfig {
  prefix: string;
  router: express.Router;
  description: string;
  version: string;
  enabled: boolean;
  middleware?: express.RequestHandler[];
}

/**
 * API版本枚举
 */
export enum ApiVersion {
  V1 = 'v1',
  V2 = 'v2',
}

/**
 * 路由管理器类
 */
export class RouteManager {
  private static instance: RouteManager;
  private routes: Map<string, RouteConfig> = new Map();
  private mainRouter: express.Router;

  private constructor() {
    this.mainRouter = express.Router();
    this.initializeRoutes();
  }

  public static getInstance(): RouteManager {
    if (!RouteManager.instance) {
      RouteManager.instance = new RouteManager();
    }
    return RouteManager.instance;
  }

  /**
   * 初始化路由
   */
  private initializeRoutes(): void {
    // 注册所有路由模块
    this.registerRoute({
      prefix: '/auth',
      router: authRoutes,
      description: '用户认证和授权',
      version: ApiVersion.V1,
      enabled: true,
    });

    this.registerRoute({
      prefix: '/users',
      router: userRoutes,
      description: '用户管理',
      version: ApiVersion.V1,
      enabled: true,
    });

    this.registerRoute({
      prefix: '/game',
      router: gameRoutes,
      description: '游戏核心功能',
      version: ApiVersion.V1,
      enabled: true,
    });

    this.registerRoute({
      prefix: '/characters',
      router: characterRoutes,
      description: '角色系统',
      version: ApiVersion.V1,
      enabled: true,
    });

    this.registerRoute({
      prefix: '/items',
      router: itemRoutes,
      description: '物品系统',
      version: ApiVersion.V1,
      enabled: true,
    });

    this.registerRoute({
      prefix: '/skills',
      router: skillRoutes,
      description: '技能系统',
      version: ApiVersion.V1,
      enabled: true,
    });

    this.registerRoute({
      prefix: '/battle',
      router: battleRoutes,
      description: '战斗系统',
      version: ApiVersion.V1,
      enabled: true,
    });

    this.registerRoute({
      prefix: '/social',
      router: socialRoutes,
      description: '社交系统',
      version: ApiVersion.V1,
      enabled: true,
    });

    Logger.info('🗺️ 准备注册locations路由', { locationRoutes: !!locationRoutes });
    this.registerRoute({
      prefix: '/locations',
      router: locationRoutes,
      description: '地点配置系统',
      version: ApiVersion.V1,
      enabled: true,
    });
    Logger.info('🗺️ locations路由注册完成');

    Logger.info('路由系统初始化完成', {
      totalRoutes: this.routes.size,
      enabledRoutes: Array.from(this.routes.values()).filter(r => r.enabled).length,
    });
  }

  /**
   * 注册路由
   */
  public registerRoute(config: RouteConfig): void {
    const routeKey = `${config.version}${config.prefix}`;

    Logger.info('🔧 注册路由开始', {
      routeKey,
      prefix: config.prefix,
      enabled: config.enabled,
      hasRouter: !!config.router,
      description: config.description
    });

    if (this.routes.has(routeKey)) {
      Logger.warn('路由已存在，将被覆盖', { routeKey, config });
    }

    this.routes.set(routeKey, config);

    if (config.enabled) {
      // 使用配置的前缀（不添加/api/version，因为这在app.ts中已经添加了）
      const fullPrefix = config.prefix;

      // 应用中间件
      if (config.middleware && config.middleware.length > 0) {
        this.mainRouter.use(fullPrefix, ...config.middleware, config.router);
      } else {
        this.mainRouter.use(fullPrefix, config.router);
      }

      Logger.info('✅ 路由已注册', {
        prefix: `/api/${config.version}${fullPrefix}`, // 仅用于日志显示
        description: config.description,
        version: config.version,
        middleware: config.middleware?.length || 0,
      });
    } else {
      Logger.warn('⚠️ 路由已禁用', { routeKey, description: config.description });
    }
  }

  /**
   * 获取主路由器
   */
  public getMainRouter(): express.Router {
    return this.mainRouter;
  }

  /**
   * 获取所有路由配置
   */
  public getRoutes(): RouteConfig[] {
    return Array.from(this.routes.values());
  }

  /**
   * 获取启用的路由
   */
  public getEnabledRoutes(): RouteConfig[] {
    return Array.from(this.routes.values()).filter(route => route.enabled);
  }

  /**
   * 获取路由统计信息
   */
  public getRouteStats(): any {
    const routes = Array.from(this.routes.values());

    return {
      total: routes.length,
      enabled: routes.filter(r => r.enabled).length,
      disabled: routes.filter(r => !r.enabled).length,
      byVersion: routes.reduce((acc, route) => {
        acc[route.version] = (acc[route.version] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      routes: routes.map(route => ({
        prefix: route.prefix,
        description: route.description,
        version: route.version,
        enabled: route.enabled,
        fullPath: `/api/${route.version}${route.prefix}`,
      })),
    };
  }

  /**
   * 健康检查
   */
  public healthCheck(): boolean {
    try {
      const enabledRoutes = this.getEnabledRoutes();
      return enabledRoutes.length > 0;
    } catch (error) {
      Logger.error('路由健康检查失败', error);
      return false;
    }
  }
}

// 创建并配置路由管理器
const routeManager = RouteManager.getInstance();
const router = routeManager.getMainRouter();

// 添加健康检查路由（不需要版本前缀）
const healthRouter = express.Router();
healthRouter.use('/health', healthRoutes);

// API版本信息
router.get('/', (_req, res) => {
  const routeStats = routeManager.getRouteStats();
  res.json({
    name: 'IdleGame API',
    version: 'v1',
    description: '江湖风放置游戏API接口',
    timestamp: new Date().toISOString(),
    routes: routeStats,
    endpoints: routeStats.routes.reduce((acc: Record<string, string>, route: any) => {
      const key = route.prefix.replace('/', '');
      acc[key] = route.fullPath;
      return acc;
    }, {} as Record<string, string>),
    docs: '/api/docs',
    health: '/health',
  });
});

// 添加API信息路径
router.get('/api', (_req, res) => {
  res.json({
    message: 'IdleGame Backend API Information',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    routes: routeManager.getRouteStats(),
  });
});

// 路由日志中间件
router.use('*', (req, _res, next) => {
  Logger.apiRequest(
    req.method,
    req.originalUrl,
    0, // 状态码将在响应时更新
    0, // 持续时间将在响应时更新
    (req as any).user?.id,
    {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    }
  );
  next();
});

export { router as apiRoutes, healthRouter };
