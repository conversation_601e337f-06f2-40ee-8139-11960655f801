{"// Copyright (c) 2025 \"放置\". All rights reserved.": "", "version": "1.0.0", "config_type": "location_display_configs", "last_updated": "2025-01-10T00:00:00Z", "security_level": "public", "description": "地点显示配置 - 仅包含UI和视觉相关信息，不包含游戏逻辑", "locations": {"fishpond": {"locationId": "fishpond", "displayName": "鱼塘", "description": "宁静的鱼塘，适合钓鱼放松", "type": "fishing", "displayLevel": 1, "iconPath": "textures/locations/fishpond_icon", "backgroundPath": "textures/locations/fishpond_bg", "mapConfig": {"backgroundImagePath": "areas/fishpond/background", "mapSize": {"width": 1600, "height": 1200}, "nodeSpacing": {"x": 200, "y": 150}, "startPosition": {"x": -600, "y": 0}, "branchCount": 3, "nodesPerBranch": 5}, "environment": {"weather": "sunny", "timeOfDay": "morning", "backgroundMusic": "audio/bgm/peaceful_pond", "ambientSounds": ["water_ripple", "bird_chirping"], "lightingColor": {"r": 255, "g": 248, "b": 220, "a": 255}}, "uiLayout": {"behaviorButtonsPosition": {"x": 0, "y": -300}, "infoDisplayPosition": {"x": -400, "y": 250}, "customElements": [{"type": "weather_display", "position": {"x": 350, "y": 250}, "config": {"showTemperature": true, "showWind": false}}]}, "behaviorDisplay": [{"id": "fishing_basic", "displayName": "基础钓鱼", "description": "使用基础鱼竿进行钓鱼", "iconPath": "textures/behaviors/fishing_basic", "displayDuration": "30秒", "displayCooldown": "5秒", "displayEnergyCost": "10"}, {"id": "fishing_advanced", "displayName": "高级钓鱼", "description": "使用高级鱼竿进行钓鱼，获得更好的收益", "iconPath": "textures/behaviors/fishing_advanced", "displayDuration": "60秒", "displayCooldown": "10秒", "displayEnergyCost": "20"}]}, "farm": {"locationId": "farm", "displayName": "农场", "description": "肥沃的农场，适合种植各种作物", "type": "farming", "displayLevel": 2, "iconPath": "textures/locations/farm_icon", "backgroundPath": "textures/locations/farm_bg", "mapConfig": {"backgroundImagePath": "areas/farm/background", "mapSize": {"width": 1800, "height": 1400}, "nodeSpacing": {"x": 180, "y": 160}, "startPosition": {"x": -700, "y": 0}, "branchCount": 4, "nodesPerBranch": 6}, "environment": {"weather": "sunny", "timeOfDay": "afternoon", "backgroundMusic": "audio/bgm/farm_life", "ambientSounds": ["wind_through_crops", "farm_animals"], "lightingColor": {"r": 255, "g": 235, "b": 200, "a": 255}}, "uiLayout": {"behaviorButtonsPosition": {"x": 0, "y": -320}, "infoDisplayPosition": {"x": -420, "y": 280}, "customElements": [{"type": "crop_status_display", "position": {"x": 380, "y": 280}, "config": {"showGrowthTime": true, "showYield": true}}]}, "behaviorDisplay": [{"id": "planting_basic", "displayName": "基础种植", "description": "种植基础作物", "iconPath": "textures/behaviors/planting_basic", "displayDuration": "45秒", "displayCooldown": "无", "displayEnergyCost": "15"}, {"id": "harvesting", "displayName": "收获作物", "description": "收获成熟的作物", "iconPath": "textures/behaviors/harvesting", "displayDuration": "20秒", "displayCooldown": "无", "displayEnergyCost": "5"}]}, "forest": {"locationId": "forest", "displayName": "森林", "description": "茂密的森林，适合伐木和探险", "type": "logging", "displayLevel": 3, "iconPath": "textures/locations/forest_icon", "backgroundPath": "textures/locations/forest_bg", "mapConfig": {"backgroundImagePath": "areas/forest/background", "mapSize": {"width": 2000, "height": 1500}, "nodeSpacing": {"x": 220, "y": 180}, "startPosition": {"x": -800, "y": 0}, "branchCount": 5, "nodesPerBranch": 7}, "environment": {"weather": "cloudy", "timeOfDay": "evening", "backgroundMusic": "audio/bgm/mysterious_forest", "ambientSounds": ["leaves_rustling", "owl_hooting", "distant_howl"], "lightingColor": {"r": 200, "g": 220, "b": 180, "a": 255}}, "uiLayout": {"behaviorButtonsPosition": {"x": 0, "y": -350}, "infoDisplayPosition": {"x": -450, "y": 300}, "customElements": [{"type": "danger_level_display", "position": {"x": 400, "y": 300}, "config": {"showMonsterLevel": true, "showSafetyTips": true}}]}, "behaviorDisplay": [{"id": "logging_basic", "displayName": "基础伐木", "description": "砍伐普通树木获得木材", "iconPath": "textures/behaviors/logging_basic", "displayDuration": "40秒", "displayCooldown": "8秒", "displayEnergyCost": "18"}, {"id": "exploration", "displayName": "森林探险", "description": "在森林中探险，寻找宝藏和稀有资源", "iconPath": "textures/behaviors/exploration", "displayDuration": "90秒", "displayCooldown": "30秒", "displayEnergyCost": "25"}]}, "mine": {"locationId": "mine", "displayName": "矿洞", "description": "深邃的矿洞，蕴藏着珍贵的矿物", "type": "mining", "displayLevel": 4, "iconPath": "textures/locations/mine_icon", "backgroundPath": "textures/locations/mine_bg", "mapConfig": {"backgroundImagePath": "areas/mine/background", "mapSize": {"width": 2200, "height": 1600}, "nodeSpacing": {"x": 240, "y": 200}, "startPosition": {"x": -900, "y": 0}, "branchCount": 6, "nodesPerBranch": 8}, "environment": {"weather": "underground", "timeOfDay": "dark", "backgroundMusic": "audio/bgm/deep_cave", "ambientSounds": ["dripping_water", "cave_echo", "mining_sounds"], "lightingColor": {"r": 180, "g": 180, "b": 200, "a": 255}}, "uiLayout": {"behaviorButtonsPosition": {"x": 0, "y": -380}, "infoDisplayPosition": {"x": -480, "y": 320}, "customElements": [{"type": "depth_display", "position": {"x": 420, "y": 320}, "config": {"showDepth": true, "showOxygen": true}}]}, "behaviorDisplay": [{"id": "mining_basic", "displayName": "基础挖矿", "description": "挖掘普通矿石", "iconPath": "textures/behaviors/mining_basic", "displayDuration": "50秒", "displayCooldown": "12秒", "displayEnergyCost": "22"}]}, "ocean": {"locationId": "ocean", "displayName": "海洋", "description": "广阔的海洋，充满未知的宝藏", "type": "diving", "displayLevel": 5, "iconPath": "textures/locations/ocean_icon", "backgroundPath": "textures/locations/ocean_bg"}, "mountain": {"locationId": "mountain", "displayName": "山脉", "description": "高耸的山脉，挑战极限的地方", "type": "climbing", "displayLevel": 6, "iconPath": "textures/locations/mountain_icon", "backgroundPath": "textures/locations/mountain_bg"}, "desert": {"locationId": "desert", "displayName": "沙漠", "description": "炎热的沙漠，隐藏着古老的秘密", "type": "exploration", "displayLevel": 7, "iconPath": "textures/locations/desert_icon", "backgroundPath": "textures/locations/desert_bg"}}, "ui_constants": {"default_button_size": {"width": 120, "height": 40}, "default_icon_size": {"width": 64, "height": 64}, "animation_duration": 0.3, "fade_duration": 0.2}, "display_rules": {"locked_location_alpha": 0.5, "locked_location_grayscale": true, "show_level_requirement": true, "show_unlock_hint": true, "max_description_length": 100}}