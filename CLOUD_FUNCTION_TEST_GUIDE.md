# 云函数联调测试指南

## 测试环境状态

### ✅ 云函数测试服务器已启动
- **服务器地址**: `http://localhost:3000`
- **状态**: 运行中 (Terminal ID: 15)
- **已加载配置**: 3个地点配置 (Forest, Desert, Mountain)

### ✅ 可用的API接口
```
GET  http://localhost:3000/health                              # 健康检查
GET  http://localhost:3000/api/v1/locations/config/:locationId # 获取单个地点配置
POST http://localhost:3000/api/v1/locations/config/batch       # 批量获取地点配置
GET  http://localhost:3000/api/v1/locations/list               # 获取地点列表
```

### ✅ 前端配置已更新
- **LocationConfigManager** 已配置为连接测试服务器
- **按钮点击事件** 已绑定到测试组件
- **测试组件** 已添加到场景中

## 联调测试步骤

### 步骤1: 验证服务器运行状态
在浏览器中访问: `http://localhost:3000/health`

**预期响应:**
```json
{
  "success": true,
  "message": "地点配置测试服务器运行正常",
  "timestamp": "2025-08-09T08:14:12.817Z",
  "configs": 3
}
```

### 步骤2: 测试API接口
#### 2.1 获取森林地点配置
```bash
curl http://localhost:3000/api/v1/locations/config/Forest
```

**预期响应:**
```json
{
  "success": true,
  "message": "获取地点配置成功",
  "data": {
    "locationId": "Forest",
    "version": "v1.0.0",
    "config": {
      "locationId": "Forest",
      "name": "神秘森林",
      "description": "充满生机的森林地带，蕴藏着丰富的资源和神秘的生物",
      "mapConfig": {
        "backgroundImagePath": "bundles/locations/textures/forest_background",
        "nodeSpacing": { "x": 200, "y": 150 },
        "branchCount": 3,
        "nodesPerBranch": 5
      }
    }
  }
}
```

#### 2.2 获取地点列表
```bash
curl http://localhost:3000/api/v1/locations/list
```

### 步骤3: 游戏内联调测试
1. **启动游戏**
2. **观察控制台输出**，应该看到:
   ```
   🧪 LocationSwitchingTest: 地点切换测试组件加载
   📋 Maplist包含 10 个子节点
   ```

3. **点击地图按钮**进行测试:
   - 点击第1个按钮 (map) → 触发森林地点切换
   - 点击第2个按钮 (map-001) → 触发沙漠地点切换
   - 点击第3个按钮 (map-002) → 触发山脉地点切换

4. **预期控制台输出**:
   ```
   🗺️ 测试: 地图按钮被点击 - map
   🌐 请求云端地点配置: http://localhost:3000/api/v1/locations/config/Forest?version=latest
   🌐 从云端加载地点配置成功: Forest
   ✅ 地点配置获取成功: 神秘森林
   🎨 应用地点配置: Forest
   ✅ 地点切换测试完成: Forest
   ```

### 步骤4: 键盘快捷键测试
- **数字键1** - 测试森林地点
- **数字键2** - 测试沙漠地点
- **数字键3** - 测试山脉地点
- **T键** - 运行所有测试
- **H键** - 显示帮助

## 服务器日志监控

### 查看服务器日志
服务器运行在Terminal ID 15中，您可以观察到以下日志:

**启动日志:**
```
🌐 ========== 地点配置测试服务器 ==========
🚀 服务器启动成功: http://localhost:3000
📋 可用的API接口:
✅ 地点配置已加载: Forest (神秘森林)
✅ 地点配置已加载: Desert (炽热沙漠)
✅ 地点配置已加载: Mountain (雪峰山脉)
📋 总共加载了 3 个地点配置
```

**请求日志:**
```
📋 请求地点配置: Forest (版本: latest)
✅ 返回地点配置: Forest
```

## 故障排除

### 问题1: 服务器无响应
**症状**: 游戏中看到网络请求失败
**解决方案**:
1. 检查服务器是否运行: `curl http://localhost:3000/health`
2. 如果服务器停止，重新启动: `node backend/test-server.js`

### 问题2: 按钮点击无反应
**症状**: 点击地图按钮没有控制台输出
**解决方案**:
1. 检查测试组件是否正确加载
2. 按H键查看帮助信息
3. 尝试使用键盘快捷键测试

### 问题3: 配置数据格式错误
**症状**: 看到"云端响应格式错误"
**解决方案**:
1. 检查配置文件格式是否正确
2. 重启服务器重新加载配置

### 问题4: CORS跨域错误
**症状**: 浏览器控制台显示CORS错误
**解决方案**: 服务器已配置CORS，应该不会出现此问题

## 测试数据验证

### 验证地点配置完整性
每个地点配置应包含以下关键字段:
- `locationId`: 地点唯一标识
- `name`: 地点名称
- `mapConfig`: 地图配置
  - `backgroundImagePath`: 背景图路径
  - `nodeSpacing`: 节点间距
  - `branchCount`: 分支数量
  - `nodesPerBranch`: 每分支节点数
- `environment`: 环境设置
- `uiElements`: UI元素配置
- `nodeData`: 节点数据

### 验证网络请求流程
1. **请求发起**: 游戏发起HTTP请求
2. **服务器处理**: 测试服务器接收并处理请求
3. **数据返回**: 服务器返回JSON格式的配置数据
4. **数据解析**: 游戏解析配置数据
5. **配置应用**: 将配置应用到LinearBranchMapContainer

## 下一步扩展

### 1. 添加更多地点
1. 在`assets/bundles/locations/`中添加新的配置文件
2. 重启测试服务器自动加载新配置
3. 在按钮映射中添加新的地点ID

### 2. 实现真实云函数
1. 部署到微信云开发环境
2. 更新LocationConfigManager中的服务器地址
3. 配置生产环境的数据库

### 3. 添加缓存机制
1. 实现本地存储缓存
2. 添加缓存过期策略
3. 支持离线模式

## 总结

现在您可以进行完整的云函数联调测试:
1. ✅ **服务器运行**: 测试服务器已启动并加载了3个地点配置
2. ✅ **API可用**: 所有地点配置API接口正常工作
3. ✅ **前端集成**: 游戏已配置为连接测试服务器
4. ✅ **按钮事件**: 地图按钮点击事件已正确绑定

请开始测试，观察控制台输出验证整个流程是否正常工作！🎉
