#!/usr/bin/env node
// Copyright (c) 2025 "放置". All rights reserved.
/**
 * 环境切换脚本
 * 用于在本地和远程环境之间快速切换
 */

const fs = require('fs');
const path = require('path');

// 颜色定义
const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logInfo(message) {
    log(`ℹ️  ${message}`, 'blue');
}

function logSuccess(message) {
    log(`✅ ${message}`, 'green');
}

function logWarning(message) {
    log(`⚠️  ${message}`, 'yellow');
}

function logError(message) {
    log(`❌ ${message}`, 'red');
}

/**
 * 切换环境配置
 */
function switchEnvironment(targetEnv) {
    const envFiles = {
        'local': '.env.local',
        'remote': '.env.remote'
    };

    const sourceFile = envFiles[targetEnv];
    if (!sourceFile) {
        logError(`不支持的环境: ${targetEnv}`);
        logInfo('支持的环境: local, remote');
        process.exit(1);
    }

    const sourcePath = path.join(__dirname, '..', sourceFile);
    const targetPath = path.join(__dirname, '..', '.env');

    // 检查源文件是否存在
    if (!fs.existsSync(sourcePath)) {
        logError(`环境配置文件不存在: ${sourceFile}`);
        process.exit(1);
    }

    try {
        // 备份当前.env文件
        if (fs.existsSync(targetPath)) {
            const backupPath = path.join(__dirname, '..', '.env.backup');
            fs.copyFileSync(targetPath, backupPath);
            logInfo('已备份当前.env文件到.env.backup');
        }

        // 复制新的环境配置
        fs.copyFileSync(sourcePath, targetPath);
        logSuccess(`已切换到 ${targetEnv} 环境`);

        // 显示当前配置摘要
        showEnvironmentSummary(targetEnv);

    } catch (error) {
        logError(`切换环境失败: ${error.message}`);
        process.exit(1);
    }
}

/**
 * 显示环境配置摘要
 */
function showEnvironmentSummary(env) {
    const envPath = path.join(__dirname, '..', '.env');
    
    if (!fs.existsSync(envPath)) {
        logWarning('环境配置文件不存在');
        return;
    }

    try {
        const envContent = fs.readFileSync(envPath, 'utf8');
        const config = {};
        
        envContent.split('\n').forEach(line => {
            line = line.trim();
            if (line && !line.startsWith('#')) {
                const [key, value] = line.split('=');
                if (key && value) {
                    config[key.trim()] = value.trim();
                }
            }
        });

        log('\n🌐 ========== 当前环境配置 ==========', 'blue');
        log(`📋 环境: ${env}`, 'green');
        log(`🔧 Node环境: ${config.NODE_ENV || 'development'}`, 'blue');
        log(`🚀 端口: ${config.PORT || '3000'}`, 'blue');
        log(`🌍 API地址: ${config.API_BASE_URL || 'http://localhost:3000'}`, 'blue');
        log(`💾 数据库: ${config.MONGODB_URI || 'mongodb://localhost:27017/idlegame_dev'}`, 'blue');
        log(`⚡ Redis: ${config.REDIS_HOST || 'localhost'}:${config.REDIS_PORT || '6379'}`, 'blue');
        log('🌐 ===================================\n', 'blue');

        // 显示启动命令
        log('🚀 启动命令:', 'green');
        if (env === 'local') {
            log('   npm run dev:local', 'yellow');
        } else if (env === 'remote') {
            log('   npm run dev:remote', 'yellow');
        }
        log('   或者直接: npm run dev\n', 'yellow');

    } catch (error) {
        logWarning(`读取环境配置失败: ${error.message}`);
    }
}

/**
 * 显示当前环境状态
 */
function showCurrentEnvironment() {
    const envPath = path.join(__dirname, '..', '.env');
    
    if (!fs.existsSync(envPath)) {
        logWarning('未找到.env文件，请先切换环境');
        return;
    }

    try {
        const envContent = fs.readFileSync(envPath, 'utf8');
        const deploymentEnvMatch = envContent.match(/DEPLOYMENT_ENV=(.+)/);
        const currentEnv = deploymentEnvMatch ? deploymentEnvMatch[1].trim() : 'unknown';
        
        log('\n📋 当前环境状态:', 'blue');
        log(`   环境: ${currentEnv}`, 'green');
        
        showEnvironmentSummary(currentEnv);
        
    } catch (error) {
        logError(`读取环境状态失败: ${error.message}`);
    }
}

/**
 * 显示帮助信息
 */
function showHelp() {
    log('\n🔧 环境切换工具', 'blue');
    log('用法: node scripts/switch-env.js <command> [environment]', 'blue');
    log('\n命令:', 'green');
    log('  switch <env>  - 切换到指定环境 (local|remote)', 'yellow');
    log('  status        - 显示当前环境状态', 'yellow');
    log('  help          - 显示帮助信息', 'yellow');
    log('\n示例:', 'green');
    log('  node scripts/switch-env.js switch local   # 切换到本地环境', 'yellow');
    log('  node scripts/switch-env.js switch remote  # 切换到远程环境', 'yellow');
    log('  node scripts/switch-env.js status         # 查看当前环境', 'yellow');
    log('\n环境说明:', 'green');
    log('  local   - 本地开发环境 (localhost:3000)', 'yellow');
    log('  remote  - 远程服务器环境 (云服务器)', 'yellow');
    log('');
}

// 主函数
function main() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        showHelp();
        return;
    }

    const command = args[0];
    
    switch (command) {
        case 'switch':
            if (args.length < 2) {
                logError('请指定要切换的环境');
                logInfo('用法: node scripts/switch-env.js switch <local|remote>');
                process.exit(1);
            }
            switchEnvironment(args[1]);
            break;
            
        case 'status':
            showCurrentEnvironment();
            break;
            
        case 'help':
        case '--help':
        case '-h':
            showHelp();
            break;
            
        default:
            logError(`未知命令: ${command}`);
            showHelp();
            process.exit(1);
    }
}

// 运行主函数
main();
