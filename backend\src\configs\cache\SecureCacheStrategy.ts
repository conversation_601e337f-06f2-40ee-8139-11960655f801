/**
 * Copyright (c) 2025 "放置". All rights reserved.
 * 安全缓存策略 - 多人在线游戏优化
 */

import { Redis } from 'ioredis';
import { Logger } from '../../utils/Logger';
import { ConfigType, ConfigSecurityLevel } from '../../../shared/configs/types/config-types';

/**
 * 缓存层级定义
 */
export enum CacheLayer {
  L1_MEMORY = 'memory',      // 内存缓存（最快）
  L2_REDIS = 'redis',        // Redis缓存（快速）
  L3_DATABASE = 'database'   // 数据库缓存（持久）
}

/**
 * 缓存策略配置
 */
export interface CacheConfig {
  type: ConfigType;
  securityLevel: ConfigSecurityLevel;
  layers: CacheLayer[];
  ttl: {
    memory: number;    // 内存缓存TTL（秒）
    redis: number;     // Redis缓存TTL（秒）
    database: number;  // 数据库缓存TTL（秒）
  };
  maxSize: {
    memory: number;    // 内存缓存最大条目数
    redis: number;     // Redis缓存最大大小（MB）
  };
  compression: boolean;
  encryption: boolean;
}

/**
 * 安全缓存策略管理器
 */
export class SecureCacheStrategy {
  private static instance: SecureCacheStrategy;
  private redis: Redis;
  private memoryCache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map();
  private cacheConfigs: Map<string, CacheConfig> = new Map();

  private constructor() {
    this.initializeRedis();
    this.initializeCacheConfigs();
    this.startCleanupTimer();
  }

  public static getInstance(): SecureCacheStrategy {
    if (!SecureCacheStrategy.instance) {
      SecureCacheStrategy.instance = new SecureCacheStrategy();
    }
    return SecureCacheStrategy.instance;
  }

  /**
   * 初始化Redis连接
   */
  private initializeRedis(): void {
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '1'), // 使用不同的DB
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      keyPrefix: 'game:cache:',
      lazyConnect: true
    });
  }

  /**
   * 初始化缓存配置
   */
  private initializeCacheConfigs(): void {
    // 静态配置 - 长期缓存
    this.cacheConfigs.set('static', {
      type: ConfigType.STATIC,
      securityLevel: ConfigSecurityLevel.PUBLIC,
      layers: [CacheLayer.L1_MEMORY, CacheLayer.L2_REDIS],
      ttl: { memory: 3600, redis: 7200, database: 86400 },
      maxSize: { memory: 1000, redis: 100 },
      compression: true,
      encryption: false
    });

    // 动态配置 - 中期缓存
    this.cacheConfigs.set('dynamic', {
      type: ConfigType.DYNAMIC,
      securityLevel: ConfigSecurityLevel.PROTECTED,
      layers: [CacheLayer.L1_MEMORY, CacheLayer.L2_REDIS, CacheLayer.L3_DATABASE],
      ttl: { memory: 300, redis: 600, database: 1800 },
      maxSize: { memory: 500, redis: 50 },
      compression: true,
      encryption: true
    });

    // 用户配置 - 短期缓存
    this.cacheConfigs.set('user', {
      type: ConfigType.USER,
      securityLevel: ConfigSecurityLevel.PRIVATE,
      layers: [CacheLayer.L1_MEMORY, CacheLayer.L2_REDIS],
      ttl: { memory: 600, redis: 1800, database: 3600 },
      maxSize: { memory: 2000, redis: 200 },
      compression: false,
      encryption: true
    });

    // 实时配置 - 极短期缓存
    this.cacheConfigs.set('realtime', {
      type: ConfigType.REALTIME,
      securityLevel: ConfigSecurityLevel.PROTECTED,
      layers: [CacheLayer.L1_MEMORY],
      ttl: { memory: 60, redis: 120, database: 300 },
      maxSize: { memory: 10000, redis: 500 },
      compression: false,
      encryption: false
    });
  }

  /**
   * 获取缓存数据（多层策略）
   */
  public async get<T = any>(
    key: string, 
    configType: string = 'static',
    userId?: string
  ): Promise<T | null> {
    const config = this.cacheConfigs.get(configType);
    if (!config) {
      Logger.warn('未知的缓存配置类型', { configType });
      return null;
    }

    const secureKey = this.generateSecureKey(key, configType, userId);

    try {
      // L1: 内存缓存
      if (config.layers.includes(CacheLayer.L1_MEMORY)) {
        const memoryResult = this.getFromMemory<T>(secureKey);
        if (memoryResult !== null) {
          Logger.debug('从内存缓存获取', { key: secureKey });
          return memoryResult;
        }
      }

      // L2: Redis缓存
      if (config.layers.includes(CacheLayer.L2_REDIS)) {
        const redisResult = await this.getFromRedis<T>(secureKey, config);
        if (redisResult !== null) {
          // 回填内存缓存
          if (config.layers.includes(CacheLayer.L1_MEMORY)) {
            this.setToMemory(secureKey, redisResult, config.ttl.memory);
          }
          Logger.debug('从Redis缓存获取', { key: secureKey });
          return redisResult;
        }
      }

      // L3: 数据库缓存
      if (config.layers.includes(CacheLayer.L3_DATABASE)) {
        const dbResult = await this.getFromDatabase<T>(secureKey, config);
        if (dbResult !== null) {
          // 回填上层缓存
          if (config.layers.includes(CacheLayer.L2_REDIS)) {
            await this.setToRedis(secureKey, dbResult, config);
          }
          if (config.layers.includes(CacheLayer.L1_MEMORY)) {
            this.setToMemory(secureKey, dbResult, config.ttl.memory);
          }
          Logger.debug('从数据库缓存获取', { key: secureKey });
          return dbResult;
        }
      }

      return null;

    } catch (error) {
      Logger.error('获取缓存失败', { key: secureKey, configType, error });
      return null;
    }
  }

  /**
   * 设置缓存数据（多层策略）
   */
  public async set(
    key: string,
    value: any,
    configType: string = 'static',
    userId?: string,
    customTTL?: number
  ): Promise<boolean> {
    const config = this.cacheConfigs.get(configType);
    if (!config) {
      Logger.warn('未知的缓存配置类型', { configType });
      return false;
    }

    const secureKey = this.generateSecureKey(key, configType, userId);

    try {
      // 数据安全处理
      const processedValue = this.processValueForCache(value, config);

      // 设置到所有配置的缓存层
      const promises: Promise<any>[] = [];

      if (config.layers.includes(CacheLayer.L1_MEMORY)) {
        const ttl = customTTL || config.ttl.memory;
        this.setToMemory(secureKey, processedValue, ttl);
      }

      if (config.layers.includes(CacheLayer.L2_REDIS)) {
        const ttl = customTTL || config.ttl.redis;
        promises.push(this.setToRedis(secureKey, processedValue, config, ttl));
      }

      if (config.layers.includes(CacheLayer.L3_DATABASE)) {
        const ttl = customTTL || config.ttl.database;
        promises.push(this.setToDatabase(secureKey, processedValue, config, ttl));
      }

      await Promise.all(promises);

      Logger.debug('缓存设置成功', { 
        key: secureKey, 
        configType, 
        layers: config.layers.length 
      });

      return true;

    } catch (error) {
      Logger.error('设置缓存失败', { key: secureKey, configType, error });
      return false;
    }
  }

  /**
   * 批量设置缓存
   */
  public async mset(
    keyValues: Record<string, any>,
    configType: string = 'static',
    userId?: string
  ): Promise<boolean> {
    try {
      const promises = Object.entries(keyValues).map(([key, value]) =>
        this.set(key, value, configType, userId)
      );

      const results = await Promise.all(promises);
      const successCount = results.filter(Boolean).length;

      Logger.info('批量缓存设置完成', {
        total: Object.keys(keyValues).length,
        success: successCount,
        configType
      });

      return successCount === Object.keys(keyValues).length;

    } catch (error) {
      Logger.error('批量设置缓存失败', { configType, error });
      return false;
    }
  }

  /**
   * 删除缓存
   */
  public async del(
    key: string,
    configType: string = 'static',
    userId?: string
  ): Promise<boolean> {
    const config = this.cacheConfigs.get(configType);
    if (!config) return false;

    const secureKey = this.generateSecureKey(key, configType, userId);

    try {
      const promises: Promise<any>[] = [];

      // 从所有层删除
      if (config.layers.includes(CacheLayer.L1_MEMORY)) {
        this.memoryCache.delete(secureKey);
      }

      if (config.layers.includes(CacheLayer.L2_REDIS)) {
        promises.push(this.redis.del(secureKey));
      }

      if (config.layers.includes(CacheLayer.L3_DATABASE)) {
        promises.push(this.deleteFromDatabase(secureKey));
      }

      await Promise.all(promises);

      Logger.debug('缓存删除成功', { key: secureKey, configType });
      return true;

    } catch (error) {
      Logger.error('删除缓存失败', { key: secureKey, configType, error });
      return false;
    }
  }

  /**
   * 清理过期缓存
   */
  public async cleanup(): Promise<void> {
    try {
      const now = Date.now();
      let cleanedCount = 0;

      // 清理内存缓存
      for (const [key, entry] of this.memoryCache.entries()) {
        if (now - entry.timestamp > entry.ttl * 1000) {
          this.memoryCache.delete(key);
          cleanedCount++;
        }
      }

      // 检查内存缓存大小限制
      const totalMemoryLimit = Array.from(this.cacheConfigs.values())
        .reduce((sum, config) => sum + config.maxSize.memory, 0);

      if (this.memoryCache.size > totalMemoryLimit) {
        const excess = this.memoryCache.size - totalMemoryLimit;
        const entries = Array.from(this.memoryCache.entries())
          .sort((a, b) => a[1].timestamp - b[1].timestamp);
        
        for (let i = 0; i < excess; i++) {
          this.memoryCache.delete(entries[i][0]);
          cleanedCount++;
        }
      }

      Logger.debug('缓存清理完成', { 
        cleanedCount, 
        remainingSize: this.memoryCache.size 
      });

    } catch (error) {
      Logger.error('缓存清理失败', { error });
    }
  }

  // ==================== 私有方法 ====================

  private generateSecureKey(key: string, configType: string, userId?: string): string {
    const parts = [configType, key];
    if (userId) parts.push(userId);
    return parts.join(':');
  }

  private getFromMemory<T>(key: string): T | null {
    const entry = this.memoryCache.get(key);
    if (!entry) return null;

    const now = Date.now();
    if (now - entry.timestamp > entry.ttl * 1000) {
      this.memoryCache.delete(key);
      return null;
    }

    return entry.data;
  }

  private setToMemory(key: string, value: any, ttl: number): void {
    this.memoryCache.set(key, {
      data: value,
      timestamp: Date.now(),
      ttl
    });
  }

  private async getFromRedis<T>(key: string, config: CacheConfig): Promise<T | null> {
    try {
      const value = await this.redis.get(key);
      if (!value) return null;

      return this.processValueFromCache(JSON.parse(value), config);
    } catch (error) {
      Logger.error('Redis获取失败', { key, error });
      return null;
    }
  }

  private async setToRedis(
    key: string, 
    value: any, 
    config: CacheConfig, 
    ttl?: number
  ): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value);
      const finalTTL = ttl || config.ttl.redis;
      await this.redis.setex(key, finalTTL, serializedValue);
    } catch (error) {
      Logger.error('Redis设置失败', { key, error });
      throw error;
    }
  }

  private async getFromDatabase<T>(key: string, config: CacheConfig): Promise<T | null> {
    // 这里应该实现数据库缓存逻辑
    // 暂时返回null
    return null;
  }

  private async setToDatabase(
    key: string, 
    value: any, 
    config: CacheConfig, 
    ttl: number
  ): Promise<void> {
    // 这里应该实现数据库缓存逻辑
  }

  private async deleteFromDatabase(key: string): Promise<void> {
    // 这里应该实现数据库删除逻辑
  }

  private processValueForCache(value: any, config: CacheConfig): any {
    let processed = value;

    // 压缩处理
    if (config.compression && typeof processed === 'object') {
      // 这里可以实现压缩逻辑
    }

    // 加密处理
    if (config.encryption && config.securityLevel !== ConfigSecurityLevel.PUBLIC) {
      // 这里可以实现加密逻辑
    }

    return processed;
  }

  private processValueFromCache(value: any, config: CacheConfig): any {
    let processed = value;

    // 解密处理
    if (config.encryption && config.securityLevel !== ConfigSecurityLevel.PUBLIC) {
      // 这里可以实现解密逻辑
    }

    // 解压缩处理
    if (config.compression && typeof processed === 'object') {
      // 这里可以实现解压缩逻辑
    }

    return processed;
  }

  private startCleanupTimer(): void {
    // 每5分钟清理一次过期缓存
    setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }
}
