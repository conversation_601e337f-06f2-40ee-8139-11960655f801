{"// Copyright (c) 2025 \"放置\". All rights reserved.": "", "version": "1.0.0", "config_type": "currency_config", "last_updated": "2025-01-10T00:00:00Z", "checksum": "def456ghi789", "currencies": {"gold": {"id": "gold", "name": "金币", "icon": "currency_gold", "max_amount": 999999999, "decimal_places": 0, "default_amount": 100, "can_purchase": false, "can_trade": true, "storage_type": "server"}, "diamond": {"id": "diamond", "name": "钻石", "icon": "currency_diamond", "max_amount": 99999, "decimal_places": 0, "default_amount": 0, "can_purchase": true, "can_trade": false, "storage_type": "server"}, "exp": {"id": "exp", "name": "经验值", "icon": "currency_exp", "max_amount": 999999999, "decimal_places": 0, "default_amount": 0, "can_purchase": false, "can_trade": false, "storage_type": "server"}, "energy": {"id": "energy", "name": "体力", "icon": "currency_energy", "max_amount": 100, "decimal_places": 0, "default_amount": 100, "can_purchase": true, "can_trade": false, "storage_type": "server", "regeneration": {"enabled": true, "rate": 1, "interval": 300000, "max_regen": 100}}}, "idle_income": {"base_gold_per_second": 1, "base_exp_per_second": 0.5, "level_multiplier": {"gold": 1.1, "exp": 1.05}, "max_offline_hours": 24, "offline_efficiency": 0.8, "formulas": {"gold_per_second": "base_gold_per_second * pow(level_multiplier.gold, character_level - 1)", "exp_per_second": "base_exp_per_second * pow(level_multiplier.exp, character_level - 1)", "offline_income": "income_per_second * offline_seconds * offline_efficiency"}}, "conversion_rates": {"diamond_to_gold": {"rate": 1000, "daily_limit": 10, "cost_formula": "diamonds * rate"}, "diamond_to_energy": {"rate": 10, "daily_limit": 5, "cost_formula": "diamonds * rate"}}, "anti_cheat": {"max_income_per_hour": {"gold": 100000, "exp": 50000}, "suspicious_threshold": {"gold_gain_rate": 10.0, "exp_gain_rate": 5.0}, "validation_rules": ["income_rate <= max_theoretical_rate * 1.1", "offline_time <= max_offline_hours * 3600", "currency_amount <= max_amount"]}}