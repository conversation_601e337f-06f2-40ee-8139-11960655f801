/**
 * Copyright (c) 2025 "放置". All rights reserved.
 * 协议基础类型定义 - 所有协议的基础
 */

// ==================== 协议版本管理 ====================

/**
 * 协议版本信息
 */
export const PROTOCOL_VERSION = {
  CORE: "1.0.0",
  SCENE: "1.0.0",
  PLAYER: "1.0.0",
  ITEM: "1.0.0",
  BATTLE: "1.0.0",
  SOCIAL: "1.0.0",
  ECONOMY: "1.0.0"
} as const;

/**
 * 协议层级枚举
 */
export enum ProtocolLayer {
  UI = "ui",                          // UI层协议
  BUSINESS = "business",              // 业务层协议
  DATA = "data",                      // 数据层协议
  NETWORK = "network"                 // 网络层协议
}

/**
 * 协议消息类型枚举
 */
export enum ProtocolMessageType {
  REQUEST = "request",                // 请求消息
  RESPONSE = "response",              // 响应消息
  EVENT = "event",                    // 事件消息
  NOTIFICATION = "notification"       // 通知消息
}

// ==================== 基础协议接口 ====================

/**
 * 协议消息基础接口
 */
export interface BaseProtocolMessage<T = any> {
  id: string;                         // 消息唯一ID
  type: string;                       // 消息类型
  messageType: ProtocolMessageType;   // 协议消息类型
  layer: ProtocolLayer;               // 协议层级
  version: string;                    // 协议版本
  payload: T;                         // 消息载荷
  metadata?: ProtocolMetadata;        // 元数据
  timestamp: number;                  // 时间戳
}

/**
 * 协议响应基础接口
 */
export interface BaseProtocolResponse<T = any> {
  id: string;                         // 对应的请求ID
  success: boolean;                   // 是否成功
  data?: T;                          // 响应数据
  error?: ProtocolError;             // 错误信息
  metadata?: ProtocolMetadata;        // 元数据
  timestamp: number;                  // 响应时间戳
}

/**
 * 协议元数据
 */
export interface ProtocolMetadata {
  userId?: string;                    // 用户ID
  sessionId?: string;                 // 会话ID
  clientId?: string;                  // 客户端ID
  serverId?: string;                  // 服务器ID
  priority?: ProtocolPriority;        // 优先级
  timeout?: number;                   // 超时时间（毫秒）
  retryCount?: number;                // 重试次数
  correlationId?: string;             // 关联ID
  traceId?: string;                   // 追踪ID
  tags?: Record<string, string>;      // 标签
}

/**
 * 协议优先级枚举
 */
export enum ProtocolPriority {
  LOW = "low",                        // 低优先级
  NORMAL = "normal",                  // 普通优先级
  HIGH = "high",                      // 高优先级
  CRITICAL = "critical"               // 关键优先级
}

// ==================== 错误处理 ====================

/**
 * 协议错误接口
 */
export interface ProtocolError {
  code: string;                       // 错误码
  message: string;                    // 错误消息
  details?: any;                      // 错误详情
  retryable?: boolean;                // 是否可重试
  retryAfter?: number;                // 重试延迟（毫秒）
  stack?: string;                     // 错误堆栈
  timestamp: number;                  // 错误发生时间
}

/**
 * 通用错误码枚举
 */
export enum CommonErrorCode {
  // 通用错误 (1xxx)
  UNKNOWN_ERROR = "PROTO_1001",
  INVALID_REQUEST = "PROTO_1002",
  INVALID_RESPONSE = "PROTO_1003",
  TIMEOUT_ERROR = "PROTO_1004",
  NETWORK_ERROR = "PROTO_1005",
  
  // 验证错误 (2xxx)
  VALIDATION_ERROR = "PROTO_2001",
  MISSING_REQUIRED_FIELD = "PROTO_2002",
  INVALID_FIELD_TYPE = "PROTO_2003",
  INVALID_FIELD_VALUE = "PROTO_2004",
  
  // 权限错误 (3xxx)
  UNAUTHORIZED = "PROTO_3001",
  FORBIDDEN = "PROTO_3002",
  ACCESS_DENIED = "PROTO_3003",
  
  // 资源错误 (4xxx)
  RESOURCE_NOT_FOUND = "PROTO_4001",
  RESOURCE_ALREADY_EXISTS = "PROTO_4002",
  RESOURCE_LOCKED = "PROTO_4003",
  RESOURCE_EXPIRED = "PROTO_4004",
  
  // 服务器错误 (5xxx)
  SERVER_ERROR = "PROTO_5001",
  SERVICE_UNAVAILABLE = "PROTO_5002",
  DATABASE_ERROR = "PROTO_5003",
  CACHE_ERROR = "PROTO_5004"
}

// ==================== 缓存策略 ====================

/**
 * 缓存策略枚举
 */
export enum CachePolicy {
  NO_CACHE = "no_cache",              // 不使用缓存
  USE_CACHE = "use_cache",            // 优先使用缓存
  FORCE_REFRESH = "force_refresh",    // 强制刷新
  CACHE_FIRST = "cache_first",        // 缓存优先
  SERVER_FIRST = "server_first",      // 服务器优先
  CACHE_ONLY = "cache_only"           // 仅使用缓存
}

/**
 * 缓存配置接口
 */
export interface CacheConfig {
  policy: CachePolicy;                // 缓存策略
  ttl?: number;                       // 生存时间（毫秒）
  maxSize?: number;                   // 最大缓存大小
  key?: string;                       // 缓存键
  tags?: string[];                    // 缓存标签
}

// ==================== 分页和排序 ====================

/**
 * 分页请求接口
 */
export interface PaginationRequest {
  page: number;                       // 页码（从1开始）
  pageSize: number;                   // 每页大小
  total?: number;                     // 总数（可选）
}

/**
 * 分页响应接口
 */
export interface PaginationResponse<T> {
  items: T[];                         // 数据项
  pagination: {
    page: number;                     // 当前页码
    pageSize: number;                 // 每页大小
    total: number;                    // 总数
    totalPages: number;               // 总页数
    hasNext: boolean;                 // 是否有下一页
    hasPrev: boolean;                 // 是否有上一页
  };
}

/**
 * 排序配置接口
 */
export interface SortConfig {
  field: string;                      // 排序字段
  order: "asc" | "desc";             // 排序方向
}

// ==================== 过滤和查询 ====================

/**
 * 过滤条件接口
 */
export interface FilterCondition {
  field: string;                      // 字段名
  operator: FilterOperator;           // 操作符
  value: any;                         // 值
}

/**
 * 过滤操作符枚举
 */
export enum FilterOperator {
  EQUALS = "eq",                      // 等于
  NOT_EQUALS = "ne",                  // 不等于
  GREATER_THAN = "gt",                // 大于
  GREATER_THAN_OR_EQUAL = "gte",      // 大于等于
  LESS_THAN = "lt",                   // 小于
  LESS_THAN_OR_EQUAL = "lte",         // 小于等于
  IN = "in",                          // 包含
  NOT_IN = "nin",                     // 不包含
  LIKE = "like",                      // 模糊匹配
  REGEX = "regex"                     // 正则匹配
}

/**
 * 查询配置接口
 */
export interface QueryConfig {
  filters?: FilterCondition[];        // 过滤条件
  sort?: SortConfig[];                // 排序配置
  pagination?: PaginationRequest;     // 分页配置
  fields?: string[];                  // 返回字段
  cache?: CacheConfig;                // 缓存配置
}

// ==================== 工具类型 ====================

/**
 * 可选字段类型
 */
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * 必需字段类型
 */
export type Required<T, K extends keyof T> = T & Required<Pick<T, K>>;

/**
 * 深度只读类型
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

/**
 * 深度可选类型
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// ==================== 常量定义 ====================

/**
 * 协议常量
 */
export const PROTOCOL_CONSTANTS = {
  // 默认值
  DEFAULT_TIMEOUT: 30000,             // 30秒
  DEFAULT_RETRY_COUNT: 3,             // 重试3次
  DEFAULT_PAGE_SIZE: 20,              // 每页20条
  MAX_PAGE_SIZE: 100,                 // 最大每页100条
  
  // 缓存
  DEFAULT_CACHE_TTL: 300000,          // 5分钟
  MAX_CACHE_SIZE: 1000,               // 最大缓存1000项
  
  // 消息
  MAX_MESSAGE_SIZE: 1024 * 1024,      // 1MB
  MESSAGE_ID_LENGTH: 32,              // 消息ID长度
  
  // 协议标识
  PROTOCOL_PREFIX: "IDLE_GAME",       // 协议前缀
  PROTOCOL_SEPARATOR: ".",            // 协议分隔符
} as const;

/**
 * 协议工具函数
 */
export class ProtocolUtils {
  /**
   * 生成消息ID
   */
  static generateMessageId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 9);
    return `${PROTOCOL_CONSTANTS.PROTOCOL_PREFIX}_${timestamp}_${random}`;
  }

  /**
   * 生成追踪ID
   */
  static generateTraceId(): string {
    return `trace_${Date.now()}_${Math.random().toString(36).substr(2, 16)}`;
  }

  /**
   * 验证消息格式
   */
  static validateMessage(message: any): message is BaseProtocolMessage {
    return (
      message &&
      typeof message.id === "string" &&
      typeof message.type === "string" &&
      typeof message.messageType === "string" &&
      typeof message.layer === "string" &&
      typeof message.version === "string" &&
      message.payload !== undefined &&
      typeof message.timestamp === "number"
    );
  }

  /**
   * 创建错误对象
   */
  static createError(
    code: string,
    message: string,
    details?: any,
    retryable: boolean = false
  ): ProtocolError {
    return {
      code,
      message,
      details,
      retryable,
      timestamp: Date.now()
    };
  }

  /**
   * 格式化协议类型
   */
  static formatProtocolType(module: string, action: string, layer: ProtocolLayer): string {
    return `${PROTOCOL_CONSTANTS.PROTOCOL_PREFIX}${PROTOCOL_CONSTANTS.PROTOCOL_SEPARATOR}${module}${PROTOCOL_CONSTANTS.PROTOCOL_SEPARATOR}${action}${PROTOCOL_CONSTANTS.PROTOCOL_SEPARATOR}${layer}`;
  }
}
