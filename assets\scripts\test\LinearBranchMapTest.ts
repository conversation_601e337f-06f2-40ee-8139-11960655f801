/**
 * 线性分支地图系统测试脚本
 * 用于验证系统是否正常工作
 */

import { _decorator, Component, Node, Button, Label, find } from 'cc';
import { EventManager } from '../managers/EventManager';
import { LinearBranchMapPanel } from '../ui/panels/LinearBranchMapPanel';
import { InteractiveMapController } from '../ui/controllers/InteractiveMapController';
import { MainUIController } from '../ui/MainUIController';

const { ccclass, property } = _decorator;

@ccclass('LinearBranchMapTest')
export class LinearBranchMapTest extends Component {
    
    @property({ type: Button, tooltip: '测试按钮' })
    public testButton: Button | null = null;
    
    @property({ type: Label, tooltip: '状态标签' })
    public statusLabel: Label | null = null;
    
    @property({ type: Node, tooltip: '测试面板' })
    public testPanel: Node | null = null;
    
    // 测试状态
    private _testResults: string[] = [];
    private _currentTest: number = 0;

    protected onLoad(): void {
        console.log('🧪 LinearBranchMapTest: 测试脚本加载');
        this.setupTestUI();
        this.bindEvents();
    }

    protected start(): void {
        this.updateStatus('测试系统已准备就绪');
        this.runBasicTests();
    }

    protected onDestroy(): void {
        this.unbindEvents();
    }

    /**
     * 设置测试UI
     */
    private setupTestUI(): void {
        if (this.testButton) {
            this.testButton.node.on(Button.EventType.CLICK, this.runAllTests, this);
        }
        
        if (!this.testPanel) {
            this.testPanel = this.node.getChildByName('TestPanel');
        }
        
        if (!this.statusLabel) {
            this.statusLabel = this.testPanel?.getChildByName('StatusLabel')?.getComponent(Label) || null;
        }
    }

    /**
     * 绑定事件
     */
    private bindEvents(): void {
        const eventManager = EventManager.getInstance();
        
        eventManager.on('branch_node_clicked', this.onTestEvent, this);
        eventManager.on('map_behavior_completed', this.onTestEvent, this);
        eventManager.on('branch_node_unlocked', this.onTestEvent, this);
        
        console.log('🧪 LinearBranchMapTest: 事件绑定完成');
    }

    /**
     * 解绑事件
     */
    private unbindEvents(): void {
        const eventManager = EventManager.getInstance();
        
        eventManager.off('branch_node_clicked', this.onTestEvent, this);
        eventManager.off('map_behavior_completed', this.onTestEvent, this);
        eventManager.off('branch_node_unlocked', this.onTestEvent, this);
    }

    /**
     * 测试事件处理
     */
    private onTestEvent(eventData: any): void {
        console.log('🧪 LinearBranchMapTest: 收到测试事件', eventData);
        this._testResults.push(`事件接收成功: ${JSON.stringify(eventData)}`);
    }

    /**
     * 更新状态显示
     */
    private updateStatus(message: string): void {
        if (this.statusLabel) {
            this.statusLabel.string = message;
        }
        console.log('🧪 LinearBranchMapTest:', message);
    }

    /**
     * 运行基础测试
     */
    private runBasicTests(): void {
        this.updateStatus('开始运行基础测试...');
        
        // 测试1: 检查组件是否存在
        this.testComponentsExist();
        
        // 测试2: 检查事件系统
        this.testEventSystem();
        
        // 测试3: 检查地图系统
        this.testMapSystem();
        
        this.updateStatus('基础测试完成');
    }

    /**
     * 测试组件是否存在
     */
    private testComponentsExist(): void {
        console.log('🧪 测试1: 检查组件是否存在');
        
        // 查找MainUIController
        const mainUI = find('Canvas/MainUI')?.getComponent(MainUIController);
        if (mainUI) {
            console.log('✅ MainUIController 存在');
            this._testResults.push('MainUIController: 通过');
        } else {
            console.log('❌ MainUIController 不存在');
            this._testResults.push('MainUIController: 失败');
        }
        
        // 查找LinearBranchMapPanel
        const mapPanel = find('Canvas')?.getComponentInChildren(LinearBranchMapPanel);
        if (mapPanel) {
            console.log('✅ LinearBranchMapPanel 存在');
            this._testResults.push('LinearBranchMapPanel: 通过');
        } else {
            console.log('❌ LinearBranchMapPanel 不存在');
            this._testResults.push('LinearBranchMapPanel: 失败');
        }
        
        // 查找InteractiveMapController
        const mapController = find('Canvas')?.getComponentInChildren(InteractiveMapController);
        if (mapController) {
            console.log('✅ InteractiveMapController 存在');
            this._testResults.push('InteractiveMapController: 通过');
        } else {
            console.log('❌ InteractiveMapController 不存在');
            this._testResults.push('InteractiveMapController: 失败');
        }
    }

    /**
     * 测试事件系统
     */
    private testEventSystem(): void {
        console.log('🧪 测试2: 检查事件系统');
        
        const eventManager = EventManager.getInstance();
        
        // 测试事件发送和接收
        let testEventReceived = false;
        
        const testHandler = () => {
            testEventReceived = true;
            console.log('✅ 测试事件接收成功');
        };
        
        eventManager.on('test_event', testHandler);
        eventManager.emit('test_event', { test: true });
        eventManager.off('test_event', testHandler);
        
        if (testEventReceived) {
            this._testResults.push('事件系统: 通过');
        } else {
            this._testResults.push('事件系统: 失败');
        }
    }

    /**
     * 测试地图系统
     */
    private testMapSystem(): void {
        console.log('🧪 测试3: 检查地图系统');
        
        const mainUI = find('Canvas/MainUI')?.getComponent(MainUIController);
        
        if (mainUI) {
            const mapController = mainUI.getMapController();
            const mapPanel = mainUI.getBranchMapPanel();
            
            if (mapController && mapPanel) {
                console.log('✅ 地图系统组件完整');
                this._testResults.push('地图系统: 通过');
                
                // 测试地图显示/隐藏
                try {
                    mapPanel.showPanel();
                    console.log('✅ 地图显示功能正常');
                    
                    mapPanel.hidePanel();
                    console.log('✅ 地图隐藏功能正常');
                    
                    this._testResults.push('地图显示/隐藏: 通过');
                } catch (error) {
                    console.log('❌ 地图显示/隐藏功能异常:', error);
                    this._testResults.push('地图显示/隐藏: 失败');
                }
            } else {
                console.log('❌ 地图系统组件不完整');
                this._testResults.push('地图系统: 失败');
            }
        } else {
            console.log('❌ 无法找到MainUIController');
            this._testResults.push('地图系统: 失败');
        }
    }

    /**
     * 运行所有测试
     */
    public runAllTests(): void {
        this.updateStatus('开始运行完整测试套件...');
        this._testResults = [];
        this._currentTest = 0;
        
        // 运行基础测试
        this.runBasicTests();
        
        // 运行高级测试
        this.runAdvancedTests();
        
        // 显示测试结果
        this.showTestResults();
    }

    /**
     * 运行高级测试
     */
    private runAdvancedTests(): void {
        console.log('🧪 开始高级测试...');
        
        // 测试4: 节点创建和交互
        this.testNodeInteraction();
        
        // 测试5: 地图拖拽功能
        this.testMapDragging();
        
        // 测试6: 行为系统集成
        this.testBehaviorIntegration();
    }

    /**
     * 测试节点交互
     */
    private testNodeInteraction(): void {
        console.log('🧪 测试4: 节点创建和交互');
        
        const mainUI = find('Canvas/MainUI')?.getComponent(MainUIController);
        const mapPanel = mainUI?.getBranchMapPanel();
        
        if (mapPanel) {
            try {
                // 测试聚焦功能
                mapPanel.focusOnNode('start');
                console.log('✅ 节点聚焦功能正常');
                this._testResults.push('节点聚焦: 通过');
                
                // 测试节点数据获取
                const nodeData = mapPanel.getNodeData('start');
                if (nodeData) {
                    console.log('✅ 节点数据获取正常');
                    this._testResults.push('节点数据获取: 通过');
                } else {
                    console.log('❌ 节点数据获取失败');
                    this._testResults.push('节点数据获取: 失败');
                }
            } catch (error) {
                console.log('❌ 节点交互测试异常:', error);
                this._testResults.push('节点交互: 失败');
            }
        } else {
            console.log('❌ 无法找到地图面板');
            this._testResults.push('节点交互: 失败');
        }
    }

    /**
     * 测试地图拖拽功能
     */
    private testMapDragging(): void {
        console.log('🧪 测试5: 地图拖拽功能');
        
        // 这里可以添加拖拽功能的测试
        // 由于需要模拟触摸事件，暂时标记为通过
        console.log('✅ 地图拖拽功能测试跳过（需要用户交互）');
        this._testResults.push('地图拖拽: 跳过');
    }

    /**
     * 测试行为系统集成
     */
    private testBehaviorIntegration(): void {
        console.log('🧪 测试6: 行为系统集成');
        
        const eventManager = EventManager.getInstance();
        
        try {
            // 模拟触发行为事件
            eventManager.emit('trigger_behavior', {
                behaviorType: 'Attack',
                nodeData: { id: 'test', name: '测试节点' }
            });
            
            console.log('✅ 行为事件触发正常');
            this._testResults.push('行为系统集成: 通过');
        } catch (error) {
            console.log('❌ 行为系统集成异常:', error);
            this._testResults.push('行为系统集成: 失败');
        }
    }

    /**
     * 显示测试结果
     */
    private showTestResults(): void {
        console.log('🧪 ========== 测试结果 ==========');
        
        let passCount = 0;
        let failCount = 0;
        let skipCount = 0;
        
        this._testResults.forEach((result, index) => {
            console.log(`${index + 1}. ${result}`);
            
            if (result.includes('通过')) {
                passCount++;
            } else if (result.includes('失败')) {
                failCount++;
            } else if (result.includes('跳过')) {
                skipCount++;
            }
        });
        
        console.log('🧪 =============================');
        console.log(`✅ 通过: ${passCount}`);
        console.log(`❌ 失败: ${failCount}`);
        console.log(`⏭️ 跳过: ${skipCount}`);
        console.log(`📊 总计: ${this._testResults.length}`);
        
        const successRate = passCount / (passCount + failCount) * 100;
        console.log(`📈 成功率: ${successRate.toFixed(1)}%`);
        
        this.updateStatus(`测试完成 - 成功率: ${successRate.toFixed(1)}%`);
    }

    /**
     * 获取测试结果
     */
    public getTestResults(): string[] {
        return [...this._testResults];
    }

    /**
     * 清除测试结果
     */
    public clearTestResults(): void {
        this._testResults = [];
        this._currentTest = 0;
        this.updateStatus('测试结果已清除');
    }
}
