import mongoose = require('mongoose');
import { Logger } from '../utils/logger';

/**
 * 基础文档接口
 */
export interface IBaseDocument extends mongoose.Document {
  _id: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  isDeleted: boolean;
  version: number;
}

/**
 * 查询选项接口
 */
export interface QueryOptions {
  page?: number;
  limit?: number;
  sort?: string | object;
  select?: string | object;
  populate?: string | object;
  lean?: boolean;
  includeDeleted?: boolean;
}

/**
 * 分页结果接口
 */
export interface PaginatedResult<T> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * 基础模型类
 */
export abstract class BaseModel<T extends IBaseDocument> {
  protected model: mongoose.Model<T>;
  protected modelName: string;

  constructor(model: mongoose.Model<T>) {
    this.model = model;
    this.modelName = model.modelName;
  }

  /**
   * 创建文档
   */
  public async create(data: Partial<T>): Promise<T> {
    try {
      const startTime = Date.now();
      const document = new this.model(data);
      const result = await document.save();
      
      const duration = Date.now() - startTime;
      Logger.dbOperation('CREATE', this.modelName, duration, { id: result._id });
      
      return result;
    } catch (error) {
      Logger.error(`创建${this.modelName}失败`, error);
      throw error;
    }
  }

  /**
   * 批量创建文档
   */
  public async createMany(dataArray: Partial<T>[]): Promise<T[]> {
    try {
      const startTime = Date.now();
      const results = await this.model.insertMany(dataArray);
      
      const duration = Date.now() - startTime;
      Logger.dbOperation('CREATE_MANY', this.modelName, duration, { count: results.length });
      
      return results as unknown as T[];
    } catch (error) {
      Logger.error(`批量创建${this.modelName}失败`, error);
      throw error;
    }
  }

  /**
   * 根据ID查找文档
   */
  public async findById(id: string | mongoose.Types.ObjectId, options?: QueryOptions): Promise<T | null> {
    try {
      const startTime = Date.now();
      let query = this.model.findById(id);

      // 应用查询选项
      query = this.applyQueryOptions(query, options);

      // 软删除过滤
      if (!options?.includeDeleted) {
        query = query.where({ isDeleted: { $ne: true } });
      }

      const result = await query.exec();
      
      const duration = Date.now() - startTime;
      Logger.dbOperation('FIND_BY_ID', this.modelName, duration, { id, found: !!result });
      
      return result;
    } catch (error) {
      Logger.error(`根据ID查找${this.modelName}失败`, error);
      throw error;
    }
  }

  /**
   * 查找单个文档
   */
  public async findOne(filter: object, options?: QueryOptions): Promise<T | null> {
    try {
      const startTime = Date.now();
      let query = this.model.findOne(filter);

      // 应用查询选项
      query = this.applyQueryOptions(query, options);

      // 软删除过滤
      if (!options?.includeDeleted) {
        query = query.where({ isDeleted: { $ne: true } });
      }

      const result = await query.exec();
      
      const duration = Date.now() - startTime;
      Logger.dbOperation('FIND_ONE', this.modelName, duration, { filter, found: !!result });
      
      return result;
    } catch (error) {
      Logger.error(`查找${this.modelName}失败`, error);
      throw error;
    }
  }

  /**
   * 查找多个文档
   */
  public async find(filter: object = {}, options?: QueryOptions): Promise<T[]> {
    try {
      const startTime = Date.now();
      let query = this.model.find(filter);

      // 应用查询选项
      query = this.applyQueryOptions(query, options);

      // 软删除过滤
      if (!options?.includeDeleted) {
        query = query.where({ isDeleted: { $ne: true } });
      }

      const results = await query.exec();
      
      const duration = Date.now() - startTime;
      Logger.dbOperation('FIND', this.modelName, duration, { filter, count: results.length });
      
      return results;
    } catch (error) {
      Logger.error(`查找${this.modelName}列表失败`, error);
      throw error;
    }
  }

  /**
   * 分页查询
   */
  public async paginate(filter: object = {}, options: QueryOptions = {}): Promise<PaginatedResult<T>> {
    try {
      const startTime = Date.now();
      const page = Math.max(1, options.page || 1);
      const limit = Math.min(100, Math.max(1, options.limit || 20));
      const skip = (page - 1) * limit;

      // 软删除过滤
      if (!options.includeDeleted) {
        filter = { ...filter, isDeleted: { $ne: true } };
      }

      // 构建查询
      let query = this.model.find(filter);
      query = this.applyQueryOptions(query, { ...options, page: undefined, limit: undefined });
      query = query.skip(skip).limit(limit);

      // 执行查询和计数
      const [items, total] = await Promise.all([
        query.exec(),
        this.model.countDocuments(filter),
      ]);

      const pages = Math.ceil(total / limit);
      const result: PaginatedResult<T> = {
        items,
        pagination: {
          page,
          limit,
          total,
          pages,
          hasNext: page < pages,
          hasPrev: page > 1,
        },
      };

      const duration = Date.now() - startTime;
      Logger.dbOperation('PAGINATE', this.modelName, duration, { 
        filter, page, limit, total, count: items.length 
      });

      return result;
    } catch (error) {
      Logger.error(`分页查询${this.modelName}失败`, error);
      throw error;
    }
  }

  /**
   * 更新文档
   */
  public async updateById(id: string | mongoose.Types.ObjectId, update: Partial<T>, options?: QueryOptions): Promise<T | null> {
    try {
      const startTime = Date.now();

      // 添加更新时间和版本号
      const updateData = {
        ...update,
        updatedAt: new Date(),
        $inc: { version: 1 },
      };

      let query = this.model.findByIdAndUpdate(id, updateData, {
        new: true,
        runValidators: true
      });

      // 应用查询选项
      query = this.applyQueryOptions(query, options);

      // 软删除过滤
      if (!options?.includeDeleted) {
        query = query.where({ isDeleted: { $ne: true } });
      }

      const result = await query.exec();

      const duration = Date.now() - startTime;
      Logger.dbOperation('UPDATE_BY_ID', this.modelName, duration, { id, updated: !!result });

      return result;
    } catch (error) {
      Logger.error(`更新${this.modelName}失败`, error);
      throw error;
    }
  }

  /**
   * 软删除文档
   */
  public async softDeleteById(id: string | mongoose.Types.ObjectId): Promise<T | null> {
    try {
      const startTime = Date.now();

      const result = await this.model.findByIdAndUpdate(
        id,
        {
          isDeleted: true,
          deletedAt: new Date(),
          updatedAt: new Date(),
          $inc: { version: 1 },
        },
        { new: true }
      );

      const duration = Date.now() - startTime;
      Logger.dbOperation('SOFT_DELETE_BY_ID', this.modelName, duration, { id, deleted: !!result });

      return result;
    } catch (error) {
      Logger.error(`软删除${this.modelName}失败`, error);
      throw error;
    }
  }

  /**
   * 计数文档
   */
  public async count(filter: object = {}): Promise<number> {
    try {
      const startTime = Date.now();

      // 软删除过滤
      const finalFilter = { ...filter, isDeleted: { $ne: true } };

      const count = await this.model.countDocuments(finalFilter);

      const duration = Date.now() - startTime;
      Logger.dbOperation('COUNT', this.modelName, duration, { filter, count });

      return count;
    } catch (error) {
      Logger.error(`计数${this.modelName}失败`, error);
      throw error;
    }
  }

  /**
   * 应用查询选项
   */
  private applyQueryOptions(query: any, options?: QueryOptions): any {
    if (!options) return query;

    if (options.select) {
      query = query.select(options.select);
    }

    if (options.sort) {
      query = query.sort(options.sort);
    }

    if (options.populate) {
      query = query.populate(options.populate);
    }

    if (options.lean) {
      query = query.lean();
    }

    return query;
  }

  /**
   * 检查文档是否存在
   */
  public async exists(filter: object): Promise<boolean> {
    try {
      const startTime = Date.now();

      // 软删除过滤
      const finalFilter = { ...filter, isDeleted: { $ne: true } };

      const exists = await this.model.exists(finalFilter);

      const duration = Date.now() - startTime;
      Logger.dbOperation('EXISTS', this.modelName, duration, { filter, exists: !!exists });

      return !!exists;
    } catch (error) {
      Logger.error(`检查${this.modelName}是否存在失败`, error);
      throw error;
    }
  }

  /**
   * 获取模型实例
   */
  public getModel(): mongoose.Model<T> {
    return this.model;
  }
}
