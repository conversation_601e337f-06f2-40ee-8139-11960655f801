/**
 * 技能展示栏组件 - 挂机手游专用
 * 用于展示当前装备的技能状态，技能自动释放，玩家主要用于查看和配置
 */

import { _decorator, Component, Node, Prefab, instantiate, Label, ProgressBar, Sprite } from 'cc';
import { UIButton } from './UIButton';
import { IPlayerSkill } from '../panels/SkillPanel';
import { EventManager } from '../../managers/EventManager';

const { ccclass, property } = _decorator;

/**
 * 技能展示槽位接口
 */
export interface ISkillDisplaySlot {
    /** 槽位索引 */
    index: number;
    
    /** 槽位节点 */
    node: Node;
    
    /** 技能图标 */
    iconSprite: Sprite | null;
    
    /** 技能名称标签 */
    nameLabel: Label | null;
    
    /** 技能等级标签 */
    levelLabel: Label | null;
    
    /** 冷却进度条 */
    cooldownBar: ProgressBar | null;
    
    /** 查看详情按钮 */
    detailButton: UIButton | null;
    
    /** 绑定的技能 */
    skill: IPlayerSkill | null;
    
    /** 冷却结束时间 */
    cooldownEndTime: number;
    
    /** 是否启用自动释放 */
    autoUse: boolean;
    
    /** 上次自动释放时间 */
    lastAutoUseTime: number;
    
    /** 自动释放次数统计 */
    autoUseCount: number;
}

/**
 * 技能展示事件类型
 */
export enum SkillDisplayEventType {
    SkillAutoUsed = 'skill_auto_used',          // 技能自动释放
    SkillDetailViewed = 'skill_detail_viewed',  // 查看技能详情
    SkillConfigChanged = 'skill_config_changed', // 技能配置改变
    SkillEquipped = 'skill_equipped',           // 技能装备
    SkillUnequipped = 'skill_unequipped'        // 技能卸下
}

@ccclass('SkillDisplayBar')
export class SkillDisplayBar extends Component {
    
    @property({ type: Prefab, tooltip: '技能展示槽预制体' })
    public skillSlotPrefab: Prefab | null = null;
    
    @property({ tooltip: '技能栏槽位数量' })
    public slotCount: number = 6;
    
    @property({ tooltip: '是否显示冷却动画' })
    public showCooldownAnimation: boolean = true;
    
    @property({ tooltip: '是否显示自动释放统计' })
    public showAutoUseStats: boolean = true;
    
    @property({ tooltip: '技能栏布局方向' })
    public horizontal: boolean = true;
    
    @property({ type: Label, tooltip: '总释放次数标签' })
    public totalUsageLabel: Label | null = null;
    
    @property({ type: Label, tooltip: '当前DPS标签' })
    public currentDPSLabel: Label | null = null;
    
    // 私有属性
    private _slots: ISkillDisplaySlot[] = [];
    private _autoUseEnabled: boolean = true;
    private _totalAutoUseCount: number = 0;
    private _lastDPSCalculationTime: number = 0;
    private _damageInLastSecond: number = 0;
    private _updateTimer: number = 0;

    protected onLoad(): void {
        // 创建技能展示槽
        this.createSkillSlots();
        
        // 启动更新循环
        this.startUpdateLoop();
        
        console.log(`⚔️ 技能展示栏初始化完成，${this.slotCount} 个槽位`);
    }

    protected onDestroy(): void {
        this.stopUpdateLoop();
    }

    /**
     * 创建技能展示槽
     */
    private createSkillSlots(): void {
        if (!this.skillSlotPrefab) {
            console.warn('缺少技能展示槽预制体');
            return;
        }
        
        // 清除现有槽位
        this.node.removeAllChildren();
        this._slots = [];
        
        // 创建新槽位
        for (let i = 0; i < this.slotCount; i++) {
            const slotNode = instantiate(this.skillSlotPrefab);
            slotNode.name = `SkillDisplaySlot_${i}`;
            slotNode.setParent(this.node);
            
            // 查找子组件
            const iconSprite = slotNode.getChildByName('Icon')?.getComponent(Sprite) || null;
            const nameLabel = slotNode.getChildByName('Name')?.getComponent(Label) || null;
            const levelLabel = slotNode.getChildByName('Level')?.getComponent(Label) || null;
            const cooldownBar = slotNode.getChildByName('CooldownBar')?.getComponent(ProgressBar) || null;
            const detailButton = slotNode.getChildByName('DetailButton')?.getComponent(UIButton) || null;
            
            // 创建槽位数据
            const slot: ISkillDisplaySlot = {
                index: i,
                node: slotNode,
                iconSprite,
                nameLabel,
                levelLabel,
                cooldownBar,
                detailButton,
                skill: null,
                cooldownEndTime: 0,
                autoUse: true,
                lastAutoUseTime: 0,
                autoUseCount: 0
            };
            
            // 绑定槽位事件
            this.bindSlotEvents(slot);
            
            this._slots.push(slot);
        }
        
        console.log(`创建了 ${this.slotCount} 个技能展示槽`);
    }

    /**
     * 绑定槽位事件
     */
    private bindSlotEvents(slot: ISkillDisplaySlot): void {
        if (slot.detailButton) {
            slot.detailButton.setClickCallback(() => {
                this.onSlotDetailClick(slot);
            });
        }
        
        // 绑定长按事件查看技能详情
        slot.node.on(Node.EventType.TOUCH_START, () => {
            // 可以添加长按检测逻辑
        });
    }

    /**
     * 启动更新循环
     */
    private startUpdateLoop(): void {
        this._updateTimer = setInterval(() => {
            this.updateSkillStates();
            this.updateDPSCalculation();
        }, 100); // 每100ms更新一次
    }

    /**
     * 停止更新循环
     */
    private stopUpdateLoop(): void {
        if (this._updateTimer) {
            clearInterval(this._updateTimer);
            this._updateTimer = 0;
        }
    }

    /**
     * 更新技能状态
     */
    private updateSkillStates(): void {
        const currentTime = Date.now();
        
        for (const slot of this._slots) {
            if (!slot.skill || !slot.autoUse) {
                continue;
            }
            
            // 更新冷却显示
            this.updateCooldownDisplay(slot, currentTime);
            
            // 检查是否可以自动释放技能
            if (this.canAutoUseSkill(slot, currentTime)) {
                this.autoUseSkill(slot, currentTime);
            }
        }
    }

    /**
     * 更新冷却显示
     */
    private updateCooldownDisplay(slot: ISkillDisplaySlot, currentTime: number): void {
        if (!slot.cooldownBar || !this.showCooldownAnimation) {
            return;
        }
        
        const remainingTime = slot.cooldownEndTime - currentTime;
        
        if (remainingTime > 0) {
            const totalCooldown = slot.skill!.skillData.cooldown * 1000;
            const progress = 1 - (remainingTime / totalCooldown);
            slot.cooldownBar.progress = Math.max(0, Math.min(1, progress));
        } else {
            slot.cooldownBar.progress = 1;
        }
    }

    /**
     * 检查是否可以自动释放技能
     */
    private canAutoUseSkill(slot: ISkillDisplaySlot, currentTime: number): boolean {
        if (!slot.skill || !slot.autoUse) {
            return false;
        }
        
        // 检查冷却时间
        if (currentTime < slot.cooldownEndTime) {
            return false;
        }
        
        // 检查技能是否解锁
        if (!slot.skill.unlocked) {
            return false;
        }
        
        return true;
    }

    /**
     * 自动释放技能
     */
    private autoUseSkill(slot: ISkillDisplaySlot, currentTime: number): void {
        if (!slot.skill) {
            return;
        }
        
        console.log(`⚔️ 自动释放技能: ${slot.skill.skillData.name}`);
        
        // 设置冷却时间
        slot.cooldownEndTime = currentTime + (slot.skill.skillData.cooldown * 1000);
        slot.lastAutoUseTime = currentTime;
        slot.autoUseCount++;
        this._totalAutoUseCount++;
        
        // 更新技能使用统计
        slot.skill.useCount++;
        slot.skill.lastUseTime = currentTime;
        
        // 计算伤害（模拟）
        const damage = this.calculateSkillDamage(slot.skill);
        this._damageInLastSecond += damage;
        
        // 发送技能自动释放事件
        this.emitSkillEvent(SkillDisplayEventType.SkillAutoUsed, {
            slotIndex: slot.index,
            skill: slot.skill,
            damage: damage,
            autoUseCount: slot.autoUseCount
        });
        
        // 更新显示
        this.updateSlotDisplay(slot);
    }

    /**
     * 计算技能伤害
     */
    private calculateSkillDamage(skill: IPlayerSkill): number {
        // 这里应该根据实际的伤害计算公式
        const baseDamage = skill.skillData.damage || 100;
        const levelMultiplier = 1 + (skill.level - 1) * 0.2;
        return Math.floor(baseDamage * levelMultiplier);
    }

    /**
     * 更新DPS计算
     */
    private updateDPSCalculation(): void {
        const currentTime = Date.now();
        
        if (currentTime - this._lastDPSCalculationTime >= 1000) {
            // 每秒更新一次DPS
            const dps = this._damageInLastSecond;
            this._damageInLastSecond = 0;
            this._lastDPSCalculationTime = currentTime;
            
            // 更新DPS显示
            if (this.currentDPSLabel) {
                this.currentDPSLabel.string = `DPS: ${dps}`;
            }
            
            // 更新总释放次数显示
            if (this.totalUsageLabel && this.showAutoUseStats) {
                this.totalUsageLabel.string = `总释放: ${this._totalAutoUseCount}`;
            }
        }
    }

    /**
     * 槽位详情点击事件
     */
    private onSlotDetailClick(slot: ISkillDisplaySlot): void {
        if (slot.skill) {
            console.log(`📋 查看技能详情: ${slot.skill.skillData.name}`);
            
            // 发送查看详情事件
            this.emitSkillEvent(SkillDisplayEventType.SkillDetailViewed, {
                slotIndex: slot.index,
                skill: slot.skill
            });
        }
    }

    /**
     * 更新槽位显示
     */
    private updateSlotDisplay(slot: ISkillDisplaySlot): void {
        if (slot.skill) {
            // 更新技能名称
            if (slot.nameLabel) {
                slot.nameLabel.string = slot.skill.skillData.name;
            }
            
            // 更新技能等级
            if (slot.levelLabel) {
                slot.levelLabel.string = `Lv.${slot.skill.level}`;
            }
            
            // 更新技能图标
            if (slot.iconSprite) {
                // 这里需要加载技能图标
                // slot.iconSprite.spriteFrame = skillIcon;
            }
            
            slot.node.active = true;
        } else {
            slot.node.active = false;
        }
    }

    /**
     * 发送技能事件
     */
    private emitSkillEvent(eventType: SkillDisplayEventType, data: any): void {
        EventManager.getInstance().emit(`skill_display_${eventType}`, data);
    }

    // ==================== 公共API ====================

    /**
     * 装备技能到槽位
     */
    public equipSkill(skill: IPlayerSkill, slotIndex?: number): boolean {
        let targetSlot: ISkillDisplaySlot | null = null;
        
        if (slotIndex !== undefined && slotIndex >= 0 && slotIndex < this._slots.length) {
            targetSlot = this._slots[slotIndex];
        } else {
            // 查找空闲槽位
            targetSlot = this._slots.find(slot => !slot.skill) || null;
        }
        
        if (!targetSlot) {
            console.warn('没有可用的技能槽位');
            return false;
        }
        
        targetSlot.skill = skill;
        targetSlot.autoUse = true;
        targetSlot.autoUseCount = 0;
        
        this.updateSlotDisplay(targetSlot);
        
        // 发送技能装备事件
        this.emitSkillEvent(SkillDisplayEventType.SkillEquipped, {
            slotIndex: targetSlot.index,
            skill
        });
        
        console.log(`⚔️ 装备技能到槽位 ${targetSlot.index}: ${skill.skillData.name}`);
        return true;
    }

    /**
     * 卸下技能
     */
    public unequipSkill(slotIndex: number): boolean {
        if (slotIndex < 0 || slotIndex >= this._slots.length) {
            return false;
        }
        
        const slot = this._slots[slotIndex];
        const removedSkill = slot.skill;
        
        if (!removedSkill) {
            return false;
        }
        
        slot.skill = null;
        slot.cooldownEndTime = 0;
        slot.autoUseCount = 0;
        
        this.updateSlotDisplay(slot);
        
        // 发送技能卸下事件
        this.emitSkillEvent(SkillDisplayEventType.SkillUnequipped, {
            slotIndex,
            skill: removedSkill
        });
        
        console.log(`⚔️ 从槽位 ${slotIndex} 卸下技能: ${removedSkill.skillData.name}`);
        return true;
    }

    /**
     * 设置自动释放状态
     */
    public setAutoUseEnabled(enabled: boolean): void {
        this._autoUseEnabled = enabled;
        console.log(`⚔️ 技能自动释放: ${enabled ? '启用' : '禁用'}`);
    }

    /**
     * 设置槽位自动释放状态
     */
    public setSlotAutoUse(slotIndex: number, autoUse: boolean): void {
        if (slotIndex >= 0 && slotIndex < this._slots.length) {
            this._slots[slotIndex].autoUse = autoUse;
            console.log(`⚔️ 槽位 ${slotIndex} 自动释放: ${autoUse ? '启用' : '禁用'}`);
        }
    }

    /**
     * 获取技能展示统计
     */
    public getSkillStats(): any {
        return {
            totalSlots: this.slotCount,
            equippedSkills: this._slots.filter(slot => slot.skill).length,
            totalAutoUseCount: this._totalAutoUseCount,
            autoUseEnabled: this._autoUseEnabled,
            slots: this._slots.map(slot => ({
                index: slot.index,
                skillId: slot.skill?.skillData.id || null,
                autoUse: slot.autoUse,
                autoUseCount: slot.autoUseCount,
                cooldownRemaining: Math.max(0, slot.cooldownEndTime - Date.now())
            }))
        };
    }

    /**
     * 清空所有槽位
     */
    public clearAllSlots(): void {
        for (const slot of this._slots) {
            slot.skill = null;
            slot.cooldownEndTime = 0;
            slot.autoUseCount = 0;
            this.updateSlotDisplay(slot);
        }
        
        this._totalAutoUseCount = 0;
        console.log('⚔️ 清空所有技能槽位');
    }
}
