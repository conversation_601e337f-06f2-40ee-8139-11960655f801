/**
 * Copyright (c) 2025 "放置". All rights reserved.
 * 游戏核心类型定义
 */

// ==================== 基础游戏类型 ====================

/**
 * 玩家基础信息
 */
export interface PlayerInfo {
  playerId: string;
  username: string;
  level: number;
  exp: number;
  gold: number;
  energy: number;
  maxEnergy: number;
  lastLoginTime: number;
  createTime: number;
}

/**
 * 游戏位置信息
 */
export interface GameLocation {
  locationId: string;
  name: string;
  type: string;
  unlocked: boolean;
  playerCount?: number;
}

/**
 * 游戏物品
 */
export interface GameItem {
  itemId: string;
  name: string;
  type: string;
  rarity: string;
  quantity: number;
  description?: string;
  iconPath?: string;
}

/**
 * 技能信息
 */
export interface SkillInfo {
  skillId: string;
  name: string;
  level: number;
  exp: number;
  maxLevel: number;
  type: string;
  description?: string;
}

/**
 * 装备信息
 */
export interface EquipmentInfo {
  equipmentId: string;
  name: string;
  type: string;
  level: number;
  rarity: string;
  attributes: Record<string, number>;
  equipped: boolean;
}

// ==================== 游戏行为类型 ====================

/**
 * 游戏行为
 */
export interface GameBehavior {
  behaviorId: string;
  locationId: string;
  name: string;
  type: string;
  available: boolean;
  cooldownRemaining?: number;
}

/**
 * 行为结果
 */
export interface BehaviorResult {
  success: boolean;
  rewards?: {
    exp?: number;
    gold?: number;
    items?: GameItem[];
  };
  message?: string;
  cooldownTime?: number;
}

// ==================== 多人游戏类型 ====================

/**
 * 房间信息
 */
export interface RoomInfo {
  roomId: string;
  name: string;
  playerCount: number;
  maxPlayers: number;
  gameMode: string;
  status: 'waiting' | 'playing' | 'finished';
}

/**
 * 玩家状态
 */
export interface PlayerState {
  playerId: string;
  position: { x: number; y: number };
  currentAction?: string;
  status: 'idle' | 'busy' | 'offline';
  lastUpdate: number;
}

// ==================== 系统类型 ====================

/**
 * 游戏配置
 */
export interface GameConfig {
  version: string;
  serverUrl: string;
  debugMode: boolean;
  features: Record<string, boolean>;
}

/**
 * 错误信息
 */
export interface GameError {
  code: string;
  message: string;
  details?: any;
}

/**
 * API响应基础结构
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: GameError;
  timestamp: number;
}
