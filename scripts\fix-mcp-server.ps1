# Cocos Creator MCP服务器修复脚本
Write-Host "开始修复Cocos Creator MCP服务器..." -ForegroundColor Green

# 获取项目根目录
$projectRoot = Split-Path -Parent $PSScriptRoot
Write-Host "项目目录: $projectRoot" -ForegroundColor Cyan

# 检查MCP扩展目录
$mcpExtensionPath = Join-Path $projectRoot "extensions\cocos-mcp-server"
if (-not (Test-Path $mcpExtensionPath)) {
    Write-Host "❌ MCP扩展目录不存在: $mcpExtensionPath" -ForegroundColor Red
    exit 1
}

Write-Host "✅ MCP扩展目录存在" -ForegroundColor Green

# 检查package.json
$packageJsonPath = Join-Path $mcpExtensionPath "package.json"
if (-not (Test-Path $packageJsonPath)) {
    Write-Host "❌ package.json不存在" -ForegroundColor Red
    exit 1
}

Write-Host "✅ package.json存在" -ForegroundColor Green

# 检查dist目录
$distPath = Join-Path $mcpExtensionPath "dist"
if (-not (Test-Path $distPath)) {
    Write-Host "❌ dist目录不存在，需要重新编译" -ForegroundColor Red
    
    # 尝试编译
    Set-Location $mcpExtensionPath
    if (Test-Path "tsconfig.json") {
        Write-Host "正在编译TypeScript..." -ForegroundColor Yellow
        try {
            & npx tsc
            Write-Host "✅ TypeScript编译完成" -ForegroundColor Green
        } catch {
            Write-Host "❌ TypeScript编译失败: $_" -ForegroundColor Red
            exit 1
        }
    }
} else {
    Write-Host "✅ dist目录存在" -ForegroundColor Green
}

# 检查主要文件
$mainFiles = @("main.js", "mcp-server.js")
foreach ($file in $mainFiles) {
    $filePath = Join-Path $distPath $file
    if (-not (Test-Path $filePath)) {
        Write-Host "❌ 缺少文件: $file" -ForegroundColor Red
        exit 1
    } else {
        Write-Host "✅ 文件存在: $file" -ForegroundColor Green
    }
}

# 检查设置文件
$settingsPath = Join-Path $projectRoot "settings\mcp-server.json"
if (-not (Test-Path $settingsPath)) {
    Write-Host "❌ MCP设置文件不存在，正在创建..." -ForegroundColor Yellow
    
    $settingsDir = Split-Path $settingsPath -Parent
    if (-not (Test-Path $settingsDir)) {
        New-Item -ItemType Directory -Path $settingsDir -Force | Out-Null
    }
    
    $defaultSettings = @{
        enabled = $true
        port = 3000
        autoStart = $true
        logLevel = "info"
    } | ConvertTo-Json -Depth 3
    
    Set-Content -Path $settingsPath -Value $defaultSettings -Encoding UTF8
    Write-Host "✅ 创建了默认MCP设置文件" -ForegroundColor Green
} else {
    Write-Host "✅ MCP设置文件存在" -ForegroundColor Green
}

# 测试MCP服务器连接
Write-Host "正在测试MCP服务器连接..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:3000/health" -Method GET -TimeoutSec 5
    Write-Host "✅ MCP服务器正在运行，状态码: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "响应内容: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "⚠️ MCP服务器未运行或无法连接" -ForegroundColor Yellow
    Write-Host "这可能是正常的，如果Cocos Creator未启动" -ForegroundColor Gray
}

Write-Host ""
Write-Host "MCP服务器检查完成！" -ForegroundColor Green
Write-Host "下一步操作:" -ForegroundColor Cyan
Write-Host "   1. 启动Cocos Creator" -ForegroundColor White
Write-Host "   2. 打开项目" -ForegroundColor White
Write-Host "   3. 检查扩展管理器中的MCP服务器状态" -ForegroundColor White
Write-Host "   4. 如果需要，手动启用MCP扩展" -ForegroundColor White
