{"configurations": [{"id": "b6acf1b9-49fc-464b-98f6-24460d9af7b2", "name": "默认配置", "description": "自动创建的默认工具配置", "tools": [{"category": "scene", "name": "scene_management", "enabled": true, "description": "SCENE MANAGEMENT: Get current scene info, list all scenes, open/close scenes, create new scenes, and save scenes. Use this for basic scene operations."}, {"category": "scene", "name": "scene_hierarchy", "enabled": true, "description": "SCENE HIERARCHY: Get the complete hierarchy of current scene with optional component information. Use this to inspect scene structure."}, {"category": "scene", "name": "scene_execution_control", "enabled": true, "description": "EXECUTION CONTROL: Execute component methods, scene scripts, or restore prefab instances. Use this for running custom logic and managing prefab synchronization."}, {"category": "scene", "name": "scene_state_management", "enabled": true, "description": "STATE MANAGEMENT: Create snapshots, manage undo/redo operations, and control scene reload. Use this for version control and state tracking."}, {"category": "scene", "name": "scene_query_system", "enabled": true, "description": "QUERY SYSTEM: Get scene status, available classes/components, and find nodes by asset usage. Use this for scene inspection and analysis."}, {"category": "node", "name": "node_query", "enabled": true, "description": "SEARCH AND GET NODE INFO: Use this to find nodes by name, get node details, list all nodes in scene, or detect if a node is 2D/3D. ALWAYS use this FIRST to find node UUIDs before modifying nodes."}, {"category": "node", "name": "node_lifecycle", "enabled": true, "description": "CREATE OR DELETE NODES: Use this to create new nodes (empty, with components, or from prefabs/assets) or delete existing nodes. For create: ALWAYS provide parentUuid (use node_query to find parent first)."}, {"category": "node", "name": "node_transform", "enabled": true, "description": "MODIFY NODE PROPERTIES: Use this to change node name, visibility, position, rotation, scale, or other properties. Automatically handles 2D/3D differences. ALWAYS provide uuid (get from node_query first)."}, {"category": "node", "name": "node_hierarchy", "enabled": true, "description": "MOVE OR COPY NODES: Use this to change node parent (move in hierarchy) or duplicate nodes. For move: changes which node is the parent. For duplicate: creates a copy of the node."}, {"category": "node", "name": "node_clipboard", "enabled": true, "description": "CLIPBOARD OPERATIONS: Copy, paste, cut nodes and manage node clipboard operations. Use this for duplicating and moving nodes within the scene hierarchy."}, {"category": "node", "name": "node_property_management", "enabled": true, "description": "PROPERTY MANAGEMENT: Reset node properties, transform values, or component settings to defaults. Use this to restore original values and clean up modifications."}, {"category": "node", "name": "node_array_management", "enabled": true, "description": "ARRAY MANAGEMENT: Move or remove elements in node array properties like component lists. Use this for reordering components or removing array items."}, {"category": "component", "name": "component_manage", "enabled": true, "description": "COMPONENT MANAGEMENT: Add or remove regular engine components (cc.Sprite, cc.Button, etc.). For script components use component_script. ALWAYS get nodeUuid from node tools first!"}, {"category": "component", "name": "component_script", "enabled": true, "description": "SCRIPT COMPONENT OPERATIONS: Attach or remove custom script components. Scripts have UUID-format CIDs different from engine components. Use component_query to find script CIDs."}, {"category": "component", "name": "component_query", "enabled": true, "description": "COMPONENT QUERY: Get component information, list all components on node, or get available component types. Use this FIRST to find component CIDs before removing!"}, {"category": "component", "name": "set_component_property", "enabled": true, "description": "Set one or multiple component property values for UI components or custom script components. Supports setting single property or multiple properties in one call. For node basic properties and transform properties, use set_node_properties."}, {"category": "component", "name": "configure_click_event", "enabled": true, "description": "Configure or remove click events for Button components. Supports adding new events, removing specific events, or clearing all events."}, {"category": "prefab", "name": "prefab_browse", "enabled": true, "description": "Browse prefabs: get list, view info, validate prefab files"}, {"category": "prefab", "name": "prefab_lifecycle", "enabled": true, "description": "Prefab lifecycle: create new prefab from node, delete prefab"}, {"category": "prefab", "name": "prefab_instance", "enabled": true, "description": "Scene prefab instances: instantiate prefab to scene, unlink from prefab, apply changes to prefab, revert to original"}, {"category": "prefab", "name": "prefab_edit", "enabled": true, "description": "PREFAB EDIT WORKFLOW: To modify prefab content, you MUST: 1) enter edit mode, 2) make changes, 3) save, 4) exit. Editor switches to prefab editing scene."}, {"category": "project", "name": "project_manage", "enabled": true, "description": "PROJECT MANAGEMENT: Run, build, get project information and settings. Use this for all core project operations and configuration management."}, {"category": "project", "name": "project_build_system", "enabled": true, "description": "BUILD SYSTEM: Control build panel, check builder status, and manage preview servers. Use this for build-related operations and preview management."}, {"category": "debug", "name": "debug_console", "enabled": true, "description": "CONSOLE MANAGEMENT: Get console logs or clear console. Use this for monitoring editor output and debugging messages."}, {"category": "debug", "name": "debug_logs", "enabled": true, "description": "PROJECT LOG ANALYSIS: Read, search, and analyze project log files. Use this for troubleshooting errors and monitoring system activity."}, {"category": "debug", "name": "debug_system", "enabled": true, "description": "SYSTEM INFORMATION: Get editor version, project details, memory usage, and performance stats. Use this for environment debugging and system monitoring."}, {"category": "preferences", "name": "preferences_manage", "enabled": true, "description": "PREFERENCES MANAGEMENT: Open preferences panel, get/set configuration values, and reset preferences. Use this for all preference configuration operations."}, {"category": "preferences", "name": "preferences_query", "enabled": true, "description": "PREFERENCES QUERY: Get all available preferences, list categories, or search for specific preference settings. Use this for preference discovery and inspection."}, {"category": "preferences", "name": "preferences_backup", "enabled": true, "description": "PREFERENCES BACKUP: Export current preferences to JSON format or prepare for backup operations. Use this for preference backup and restore workflows."}, {"category": "server", "name": "server_information", "enabled": true, "description": "SERVER INFORMATION: Query server IP addresses, ports, and basic server information. Use this for getting fundamental server details like available IPs, current port, and server configuration."}, {"category": "server", "name": "server_connectivity", "enabled": true, "description": "SERVER CONNECTIVITY: Test server connectivity, check network status, and get network interface information. Use this for diagnosing connection issues and monitoring server network health."}, {"category": "broadcast", "name": "broadcast_log_management", "enabled": true, "description": "BROADCAST LOG MANAGEMENT: Get, filter, and clear broadcast message logs. Use this for monitoring and managing broadcast message history, including filtering by message type and controlling log size."}, {"category": "broadcast", "name": "broadcast_listener_management", "enabled": true, "description": "BROADCAST LISTENER MANAGEMENT: Start, stop, and manage broadcast message listeners. Use this for controlling which broadcast messages to monitor and tracking active listeners."}, {"category": "sceneView", "name": "scene_view_gizmo_management", "enabled": true, "description": "GIZMO MANAGEMENT: Change Gizmo tool type, pivot point, coordinate system, and view mode. Use this for controlling the scene manipulation tools."}, {"category": "sceneView", "name": "scene_view_mode_control", "enabled": true, "description": "VIEW MODE CONTROL: Switch between 2D/3D view modes and manage grid visibility. Use this for changing the scene view perspective and display settings."}, {"category": "sceneView", "name": "scene_view_icon_gizmo", "enabled": true, "description": "ICON GIZMO CONTROL: Set IconGizmo to 3D/2D mode and adjust size. Use this for managing the visual representation of nodes in the scene."}, {"category": "sceneView", "name": "scene_view_camera_control", "enabled": true, "description": "CAMERA CONTROL: Focus camera on nodes, align camera with view, and align view with selected node. Use this for camera positioning and scene navigation."}, {"category": "sceneView", "name": "scene_view_status_management", "enabled": true, "description": "STATUS MANAGEMENT: Get comprehensive scene view status and reset view to default settings. Use this for monitoring and restoring scene view state."}, {"category": "referenceImage", "name": "reference_image_management", "enabled": true, "description": "REFERENCE IMAGE MANAGEMENT: Add, remove, switch, and clear reference images. Use this for basic reference image operations like adding images to scene, removing them, switching between images, and clearing all images."}, {"category": "referenceImage", "name": "reference_image_query", "enabled": true, "description": "REFERENCE IMAGE QUERY: Get reference image configuration, current image data, and list all available images. Use this to inspect reference image settings and current state."}, {"category": "referenceImage", "name": "reference_image_transform", "enabled": true, "description": "REFERENCE IMAGE TRANSFORM: Set reference image position, scale, opacity, and other transform properties. Use this for adjusting how reference images are displayed in the scene."}, {"category": "referenceImage", "name": "reference_image_display", "enabled": true, "description": "REFERENCE IMAGE DISPLAY: Refresh reference image display and manage image visibility. Use this for updating the display and managing reference image refresh operations."}, {"category": "assetAdvanced", "name": "asset_manage", "enabled": true, "description": "ASSET MANAGEMENT: Import, delete, save metadata, or generate URLs for assets. Use this for all asset creation/deletion/modification operations."}, {"category": "assetAdvanced", "name": "asset_analyze", "enabled": true, "description": "ASSET ANALYSIS: Get dependencies or export manifests. Use this to understand asset relationships. NOTE: validate_refs and unused detection are commented out due to API limitations."}, {"category": "assetAdvanced", "name": "asset_system", "enabled": true, "description": "ASSET SYSTEM: Check asset database status, refresh assets, or open assets with external programs. Use this for system-level asset operations."}, {"category": "assetAdvanced", "name": "asset_query", "enabled": true, "description": "ASSET QUERY: Search, get information, and find assets by various criteria. Use this for asset discovery and detailed information retrieval."}, {"category": "assetAdvanced", "name": "asset_operations", "enabled": true, "description": "ASSET OPERATIONS: Create, copy, move, delete, save, and import assets. Use this for all asset file operations and modifications."}, {"category": "validation", "name": "validate_json_params", "enabled": true, "description": "Validate and fix JSON parameters before sending to other tools"}, {"category": "validation", "name": "safe_string_value", "enabled": true, "description": "Create a safe string value that won't cause JSON parsing issues"}, {"category": "validation", "name": "format_mcp_request", "enabled": true, "description": "Format a complete MCP request with proper JSON escaping"}], "createdAt": "2025-07-30T12:57:59.771Z", "updatedAt": "2025-08-09T04:28:03.533Z"}], "currentConfigId": "b6acf1b9-49fc-464b-98f6-24460d9af7b2", "maxConfigSlots": 5}