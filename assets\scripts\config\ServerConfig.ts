// Copyright (c) 2025 "放置". All rights reserved.
/**
 * 服务器配置管理
 * 统一管理所有服务器地址和配置
 */

export interface IServerConfig {
    name: string;
    baseUrl: string;
    priority: number;
    timeout: number;
    enabled: boolean;
}

/**
 * 服务器配置类
 */
export class ServerConfig {
    /**
     * 服务器配置列表
     * 按优先级排序，优先级越高越先尝试
     */
    private static readonly SERVER_CONFIGS: IServerConfig[] = [
        {
            name: '阿里云生产服务器',
            baseUrl: 'https://your-aliyun-server.com',  // 请替换为实际的阿里云服务器地址
            priority: 100,
            timeout: 8000,
            enabled: false  // 默认禁用，部署后启用
        },
        {
            name: '阿里云测试服务器',
            baseUrl: 'http://test.your-aliyun-server.com',  // 可选的测试服务器
            priority: 90,
            timeout: 5000,
            enabled: false  // 默认禁用，需要时启用
        },
        {
            name: '本地开发服务器',
            baseUrl: 'http://localhost:3001',
            priority: 10,
            timeout: 3000,
            enabled: true   // 默认启用本地开发
        }
    ];



    /**
     * 获取启用的服务器列表
     * 按优先级从高到低排序
     */
    public static getEnabledServers(): IServerConfig[] {
        return this.SERVER_CONFIGS
            .filter(config => config.enabled)
            .sort((a, b) => b.priority - a.priority);
    }

    /**
     * 获取主服务器配置
     */
    public static getPrimaryServer(): IServerConfig | null {
        const servers = this.getEnabledServers();
        return servers.length > 0 ? servers[0] : null;
    }

    /**
     * 获取备用服务器列表
     */
    public static getFallbackServers(): IServerConfig[] {
        const servers = this.getEnabledServers();
        return servers.slice(1);  // 除了主服务器之外的所有服务器
    }

    /**
     * 根据名称获取服务器配置
     */
    public static getServerByName(name: string): IServerConfig | null {
        return this.SERVER_CONFIGS.find(config => config.name === name) || null;
    }



    /**
     * 动态启用/禁用服务器
     */
    public static setServerEnabled(name: string, enabled: boolean): boolean {
        const config = this.SERVER_CONFIGS.find(c => c.name === name);
        if (config) {
            config.enabled = enabled;
            console.log(`🔧 服务器 ${name} ${enabled ? '已启用' : '已禁用'}`);
            return true;
        }
        return false;
    }

    /**
     * 动态设置服务器地址
     */
    public static setServerUrl(name: string, baseUrl: string): boolean {
        const config = this.SERVER_CONFIGS.find(c => c.name === name);
        if (config) {
            config.baseUrl = baseUrl;
            console.log(`🔧 服务器 ${name} 地址已更新为: ${baseUrl}`);
            return true;
        }
        return false;
    }



    /**
     * 获取当前配置摘要
     */
    public static getConfigSummary(): {
        enabledServers: number;
        primaryServer: string | null;
        totalServers: number;
    } {
        const enabledServers = this.getEnabledServers();
        const primaryServer = this.getPrimaryServer();

        return {
            enabledServers: enabledServers.length,
            primaryServer: primaryServer ? primaryServer.name : null,
            totalServers: this.SERVER_CONFIGS.length
        };
    }

    /**
     * 打印配置信息
     */
    public static printConfig(): void {
        console.log('🌐 ========== 服务器配置信息 ==========');
        
        const summary = this.getConfigSummary();
        console.log(`📊 总服务器数: ${summary.totalServers}`);
        console.log(`✅ 启用服务器数: ${summary.enabledServers}`);
        console.log(`🎯 主服务器: ${summary.primaryServer || '无'}`);

        console.log('\n📋 服务器列表:');
        this.SERVER_CONFIGS.forEach(config => {
            const status = config.enabled ? '✅' : '❌';
            console.log(`  ${status} ${config.name} (优先级: ${config.priority})`);
            console.log(`     地址: ${config.baseUrl}`);
            console.log(`     超时: ${config.timeout}ms`);
        });
        
        console.log('🌐 =====================================');
    }

    /**
     * 验证配置有效性
     */
    public static validateConfig(): {
        isValid: boolean;
        errors: string[];
        warnings: string[];
    } {
        const errors: string[] = [];
        const warnings: string[] = [];

        // 检查是否有启用的服务器
        const enabledServers = this.getEnabledServers();
        if (enabledServers.length === 0) {
            errors.push('没有启用的服务器');
        }

        // 检查服务器地址格式
        this.SERVER_CONFIGS.forEach(config => {
            if (!config.baseUrl.startsWith('http://') && !config.baseUrl.startsWith('https://')) {
                errors.push(`服务器 ${config.name} 的地址格式不正确: ${config.baseUrl}`);
            }
            
            if (config.baseUrl.includes('your-') || config.baseUrl.includes('example.com')) {
                warnings.push(`服务器 ${config.name} 使用了示例地址，请替换为实际地址`);
            }
        });



        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
}
