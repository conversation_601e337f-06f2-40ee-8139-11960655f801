/**
 * UI管理器
 * 负责管理所有UI面板的创建、显示、隐藏和销毁
 */

import { _decorator, Node, Canvas, find, instantiate, Prefab, resources, director } from 'cc';
import { BaseManager } from './BaseManager';
import { 
    IUIManager, 
    IUIPanelConfig, 
    IUIPanelInstance, 
    IUIPanel,
    UIPanelType, 
    UIPanelState, 
    UILayer,
    UIEventType,
    IUIEventData
} from '../ui/types/UITypes';
import { EventManager } from './EventManager';

const { ccclass } = _decorator;

@ccclass('UIManager')
export class UIManager extends BaseManager implements IUIManager {
    private static _instance: UIManager;
    
    // UI根节点
    private _uiRoot: Node | null = null;
    private _canvas: Canvas | null = null;
    
    // 面板配置和实例
    private _panelConfigs: Map<UIPanelType, IUIPanelConfig> = new Map();
    private _panelInstances: Map<UIPanelType, IUIPanelInstance> = new Map();
    private _panelCache: Map<string, Prefab> = new Map();
    
    // 层级节点
    private _layerNodes: Map<UILayer, Node> = new Map();
    
    // 模态面板栈
    private _modalStack: UIPanelType[] = [];

    // 面板动画队列
    private _animationQueue: Map<UIPanelType, Promise<void>> = new Map();

    // 面板历史栈（用于返回功能）
    private _panelHistory: UIPanelType[] = [];

    // 面板组（用于批量操作）
    private _panelGroups: Map<string, UIPanelType[]> = new Map();

    // 预加载队列
    private _preloadQueue: Set<UIPanelType> = new Set();
    
    /**
     * 获取单例实例
     */
    public static getInstance(): UIManager {
        if (!UIManager._instance) {
            UIManager._instance = new UIManager();
        }
        return UIManager._instance;
    }

    /**
     * 初始化UI管理器
     */
    protected async initializeManager(): Promise<void> {
        console.log('🎨 UIManager: 开始初始化UI管理器');
        
        try {
            // 查找UI根节点
            this._canvas = find('Canvas')?.getComponent(Canvas) || null;
            if (!this._canvas) {
                throw new Error('未找到Canvas节点');
            }
            
            this._uiRoot = this._canvas.node;
            console.log('✅ 找到UI根节点:', this._uiRoot.name);
            
            // 创建层级节点
            this.createLayerNodes();
            
            // 注册默认面板配置
            this.registerDefaultPanels();
            
            // 注册事件监听
            this.registerEventListeners();
            
            console.log('✅ UIManager: 初始化完成');
            
        } catch (error) {
            console.error('❌ UIManager: 初始化失败', error);
            throw error;
        }
    }

    /**
     * 销毁UI管理器
     */
    public destroyManager(): void {
        // 隐藏所有面板
        this.hideAllPanels();
        
        // 清理缓存
        this.clearCache();
        
        // 清理数据
        this._panelConfigs.clear();
        this._panelInstances.clear();
        this._layerNodes.clear();
        this._modalStack.length = 0;
        this._animationQueue.clear();
        this._panelHistory.length = 0;
        this._panelGroups.clear();
        this._preloadQueue.clear();
        
        console.log('🗑️ UIManager: 已销毁');
    }

    /**
     * 创建层级节点
     */
    private createLayerNodes(): void {
        const layers = [
            UILayer.Background,
            UILayer.Normal,
            UILayer.Popup,
            UILayer.Dialog,
            UILayer.Loading,
            UILayer.System,
            UILayer.Debug
        ];
        
        for (const layer of layers) {
            const layerNode = new Node(`UILayer_${UILayer[layer]}`);
            layerNode.setSiblingIndex(layer);
            layerNode.setParent(this._uiRoot);
            
            this._layerNodes.set(layer, layerNode);
            console.log(`✅ 创建UI层级: ${UILayer[layer]} (${layer})`);
        }
    }

    /**
     * 注册默认面板配置
     */
    private registerDefaultPanels(): void {
        // 注册挂机手游的默认面板配置
        const defaultPanels = [
            {
                type: UIPanelType.Inventory,
                prefabPath: 'ui/panels/InventoryPanel',
                layer: UILayer.Normal,
                singleton: true,
                cache: true,
                escapeToClose: true
            },
            {
                type: UIPanelType.Skills,
                prefabPath: 'ui/panels/SkillPanel',
                layer: UILayer.Normal,
                singleton: true,
                cache: true,
                escapeToClose: true
            },
            {
                type: UIPanelType.GameHUD,
                prefabPath: 'ui/panels/GameHUD',
                layer: UILayer.Background,
                singleton: true,
                cache: true,
                escapeToClose: false
            }
        ];

        for (const config of defaultPanels) {
            this.registerPanel(config);
        }

        console.log(`📋 注册了 ${defaultPanels.length} 个默认面板配置`);
    }

    /**
     * 注册事件监听
     */
    private registerEventListeners(): void {
        // 监听场景切换事件
        director.on(director.EVENT_BEFORE_SCENE_LOADING, this.onBeforeSceneLoading, this);
        director.on(director.EVENT_AFTER_SCENE_LAUNCH, this.onAfterSceneLaunch, this);
    }

    /**
     * 场景切换前处理
     */
    private onBeforeSceneLoading(): void {
        console.log('🔄 场景切换前，隐藏所有UI面板');
        this.hideAllPanels();
    }

    /**
     * 场景切换后处理
     */
    private onAfterSceneLaunch(): void {
        console.log('🔄 场景切换后，重新初始化UI系统');
        // 重新查找UI根节点
        this._canvas = find('Canvas')?.getComponent(Canvas) || null;
        if (this._canvas) {
            this._uiRoot = this._canvas.node;
            this.createLayerNodes();
        }
    }

    // ==================== IUIManager接口实现 ====================

    /**
     * 注册面板配置
     */
    public registerPanel(config: IUIPanelConfig): void {
        this._panelConfigs.set(config.type, config);
        console.log(`📋 注册面板配置: ${config.type}`);
    }

    /**
     * 显示面板
     */
    public async showPanel(type: UIPanelType, data?: any): Promise<IUIPanelInstance> {
        console.log(`🎨 显示面板: ${type}`);
        
        try {
            // 检查配置
            const config = this._panelConfigs.get(type);
            if (!config) {
                throw new Error(`未找到面板配置: ${type}`);
            }
            
            // 检查是否已存在实例
            let instance = this._panelInstances.get(type);
            
            if (instance) {
                // 如果是单例且已显示，直接返回
                if (config.singleton && instance.state === UIPanelState.Visible) {
                    console.log(`⚠️ 面板已显示: ${type}`);
                    return instance;
                }
                
                // 如果不是单例，销毁旧实例
                if (!config.singleton) {
                    this.destroyPanel(type);
                    instance = null;
                }
            }
            
            // 创建新实例
            if (!instance) {
                instance = await this.createPanelInstance(config);
                this._panelInstances.set(type, instance);
            }
            
            // 显示面板
            await this.showPanelInstance(instance, data);
            
            // 处理模态面板
            if (config.modal) {
                this._modalStack.push(type);
            }
            
            // 发送事件
            this.emitUIEvent(UIEventType.PanelShow, type, data);
            
            return instance;
            
        } catch (error) {
            console.error(`❌ 显示面板失败: ${type}`, error);
            throw error;
        }
    }

    /**
     * 隐藏面板
     */
    public async hidePanel(type: UIPanelType): Promise<void> {
        console.log(`🎨 隐藏面板: ${type}`);
        
        const instance = this._panelInstances.get(type);
        if (!instance || instance.state === UIPanelState.Hidden) {
            console.log(`⚠️ 面板未显示或已隐藏: ${type}`);
            return;
        }
        
        try {
            // 隐藏面板
            await this.hidePanelInstance(instance);
            
            // 从模态栈中移除
            const modalIndex = this._modalStack.indexOf(type);
            if (modalIndex !== -1) {
                this._modalStack.splice(modalIndex, 1);
            }
            
            // 发送事件
            this.emitUIEvent(UIEventType.PanelHide, type);
            
        } catch (error) {
            console.error(`❌ 隐藏面板失败: ${type}`, error);
            throw error;
        }
    }

    /**
     * 切换面板
     */
    public async togglePanel(type: UIPanelType, data?: any): Promise<void> {
        if (this.isPanelVisible(type)) {
            await this.hidePanel(type);
        } else {
            await this.showPanel(type, data);
        }
    }

    /**
     * 获取面板实例
     */
    public getPanel(type: UIPanelType): IUIPanelInstance | null {
        return this._panelInstances.get(type) || null;
    }

    /**
     * 检查面板是否显示
     */
    public isPanelVisible(type: UIPanelType): boolean {
        const instance = this._panelInstances.get(type);
        return instance ? instance.state === UIPanelState.Visible : false;
    }

    /**
     * 隐藏所有面板
     */
    public async hideAllPanels(): Promise<void> {
        console.log('🎨 隐藏所有面板');
        
        const hidePromises: Promise<void>[] = [];
        
        for (const [type, instance] of this._panelInstances) {
            if (instance.state === UIPanelState.Visible) {
                hidePromises.push(this.hidePanel(type));
            }
        }
        
        await Promise.all(hidePromises);
    }

    /**
     * 销毁面板
     */
    public destroyPanel(type: UIPanelType): void {
        const instance = this._panelInstances.get(type);
        if (!instance) {
            return;
        }
        
        console.log(`🗑️ 销毁面板: ${type}`);
        
        // 销毁面板组件和节点
        if (instance.component) {
            instance.component.destroy();
        }
        
        if (instance.node && instance.node.isValid) {
            instance.node.destroy();
        }
        
        // 从实例映射中移除
        this._panelInstances.delete(type);
        
        // 从模态栈中移除
        const modalIndex = this._modalStack.indexOf(type);
        if (modalIndex !== -1) {
            this._modalStack.splice(modalIndex, 1);
        }
        
        // 发送事件
        this.emitUIEvent(UIEventType.PanelDestroy, type);
    }

    /**
     * 清理缓存
     */
    public clearCache(): void {
        this._panelCache.clear();
        console.log('🧹 UI缓存已清理');
    }

    // ==================== 私有方法 ====================

    /**
     * 创建面板实例
     */
    private async createPanelInstance(config: IUIPanelConfig): Promise<IUIPanelInstance> {
        console.log(`🏗️ 创建面板实例: ${config.type}`);
        
        // 加载预制体
        const prefab = await this.loadPanelPrefab(config.prefabPath);
        
        // 实例化节点
        const node = instantiate(prefab);
        node.name = `Panel_${config.type}`;
        
        // 获取面板组件
        const component = node.getComponent('BaseUIPanel') as IUIPanel;
        if (!component) {
            throw new Error(`面板预制体缺少UIPanel组件: ${config.type}`);
        }
        
        // 设置父节点
        const layerNode = this._layerNodes.get(config.layer);
        if (layerNode) {
            node.setParent(layerNode);
        } else {
            node.setParent(this._uiRoot);
        }
        
        // 创建实例对象
        const instance: IUIPanelInstance = {
            config,
            node,
            component,
            state: UIPanelState.Hidden,
            createTime: Date.now()
        };
        
        // 初始化面板
        await component.initialize(config.initData);
        
        return instance;
    }

    /**
     * 加载面板预制体
     */
    private async loadPanelPrefab(prefabPath: string): Promise<Prefab> {
        // 检查缓存
        if (this._panelCache.has(prefabPath)) {
            return this._panelCache.get(prefabPath)!;
        }
        
        // 加载预制体
        return new Promise((resolve, reject) => {
            resources.load(prefabPath, Prefab, (error, prefab) => {
                if (error) {
                    reject(new Error(`加载面板预制体失败: ${prefabPath} - ${error.message}`));
                    return;
                }
                
                // 缓存预制体
                this._panelCache.set(prefabPath, prefab);
                resolve(prefab);
            });
        });
    }

    /**
     * 显示面板实例
     */
    private async showPanelInstance(instance: IUIPanelInstance, data?: any): Promise<void> {
        instance.state = UIPanelState.Showing;
        instance.showTime = Date.now();
        
        // 显示节点
        instance.node.active = true;
        
        // 调用面板显示方法
        await instance.component.show(data);
        
        instance.state = UIPanelState.Visible;
    }

    /**
     * 隐藏面板实例
     */
    private async hidePanelInstance(instance: IUIPanelInstance): Promise<void> {
        instance.state = UIPanelState.Hiding;
        instance.hideTime = Date.now();
        
        // 调用面板隐藏方法
        await instance.component.hide();
        
        // 隐藏节点
        instance.node.active = false;
        
        instance.state = UIPanelState.Hidden;
    }

    /**
     * 发送UI事件
     */
    private emitUIEvent(type: UIEventType, panelType?: UIPanelType, data?: any): void {
        const eventData: IUIEventData = {
            type,
            panelType,
            data,
            timestamp: Date.now()
        };
        
        EventManager.getInstance().emit(`ui_${type}`, eventData);
    }

    // ==================== 工具方法 ====================

    /**
     * 获取UI统计信息
     */
    public getUIStats(): any {
        return {
            totalPanels: this._panelConfigs.size,
            activePanels: this._panelInstances.size,
            visiblePanels: Array.from(this._panelInstances.values())
                .filter(instance => instance.state === UIPanelState.Visible).length,
            cachedPrefabs: this._panelCache.size,
            modalStack: [...this._modalStack],
            layers: Array.from(this._layerNodes.keys())
        };
    }

    /**
     * 处理ESC键
     */
    public handleEscapeKey(): boolean {
        // 从模态栈顶部开始处理
        for (let i = this._modalStack.length - 1; i >= 0; i--) {
            const panelType = this._modalStack[i];
            const instance = this._panelInstances.get(panelType);
            
            if (instance && instance.component.onEscapePressed) {
                if (instance.component.onEscapePressed()) {
                    return true; // 事件被处理
                }
            }
        }
        
        return false; // 事件未被处理
    }

    /**
     * 处理返回键
     */
    public handleBackKey(): boolean {
        // 从模态栈顶部开始处理
        for (let i = this._modalStack.length - 1; i >= 0; i--) {
            const panelType = this._modalStack[i];
            const instance = this._panelInstances.get(panelType);
            
            if (instance && instance.component.onBackPressed) {
                if (instance.component.onBackPressed()) {
                    return true; // 事件被处理
                }
            }
        }
        
        return false; // 事件未被处理
    }

    // ==================== 增强的面板管理方法 ====================

    /**
     * 预加载面板
     */
    public async preloadPanel(panelType: UIPanelType): Promise<void> {
        if (this._preloadQueue.has(panelType)) {
            console.log(`🔄 面板 ${panelType} 已在预加载队列中`);
            return;
        }

        this._preloadQueue.add(panelType);

        try {
            const config = this._panelConfigs.get(panelType);
            if (!config) {
                throw new Error(`未找到面板配置: ${panelType}`);
            }

            // 预加载预制体
            await this.loadPrefab(config.prefabPath);
            console.log(`✅ 面板 ${panelType} 预加载完成`);

        } catch (error) {
            console.error(`❌ 面板 ${panelType} 预加载失败:`, error);
        } finally {
            this._preloadQueue.delete(panelType);
        }
    }

    /**
     * 批量预加载面板
     */
    public async preloadPanels(panelTypes: UIPanelType[]): Promise<void> {
        console.log(`🔄 开始批量预加载 ${panelTypes.length} 个面板`);

        const promises = panelTypes.map(type => this.preloadPanel(type));
        await Promise.allSettled(promises);

        console.log(`✅ 批量预加载完成`);
    }

    /**
     * 注册面板组
     */
    public registerPanelGroup(groupName: string, panelTypes: UIPanelType[]): void {
        this._panelGroups.set(groupName, panelTypes);
        console.log(`📋 注册面板组 ${groupName}: [${panelTypes.join(', ')}]`);
    }

    /**
     * 显示面板组
     */
    public async showPanelGroup(groupName: string): Promise<void> {
        const panelTypes = this._panelGroups.get(groupName);
        if (!panelTypes) {
            throw new Error(`未找到面板组: ${groupName}`);
        }

        console.log(`📋 显示面板组 ${groupName}`);

        const promises = panelTypes.map(type => this.showPanel(type));
        await Promise.allSettled(promises);
    }

    /**
     * 隐藏面板组
     */
    public async hidePanelGroup(groupName: string): Promise<void> {
        const panelTypes = this._panelGroups.get(groupName);
        if (!panelTypes) {
            throw new Error(`未找到面板组: ${groupName}`);
        }

        console.log(`📋 隐藏面板组 ${groupName}`);

        const promises = panelTypes.map(type => this.hidePanel(type));
        await Promise.allSettled(promises);
    }

    /**
     * 推入面板历史
     */
    public pushPanelHistory(panelType: UIPanelType): void {
        // 避免重复推入相同面板
        if (this._panelHistory[this._panelHistory.length - 1] !== panelType) {
            this._panelHistory.push(panelType);

            // 限制历史栈大小
            if (this._panelHistory.length > 10) {
                this._panelHistory.shift();
            }
        }
    }

    /**
     * 返回上一个面板
     */
    public async goBackPanel(): Promise<boolean> {
        if (this._panelHistory.length < 2) {
            return false;
        }

        // 移除当前面板
        this._panelHistory.pop();

        // 获取上一个面板
        const previousPanel = this._panelHistory[this._panelHistory.length - 1];

        try {
            await this.showPanel(previousPanel);
            console.log(`🔙 返回到面板: ${previousPanel}`);
            return true;
        } catch (error) {
            console.error(`❌ 返回面板失败:`, error);
            return false;
        }
    }

    /**
     * 清空面板历史
     */
    public clearPanelHistory(): void {
        this._panelHistory.length = 0;
        console.log(`🧹 面板历史已清空`);
    }

    /**
     * 等待面板动画完成
     */
    public async waitForPanelAnimation(panelType: UIPanelType): Promise<void> {
        const animation = this._animationQueue.get(panelType);
        if (animation) {
            await animation;
        }
    }

    /**
     * 获取面板层级节点
     */
    public getLayerNode(layer: UILayer): Node | null {
        return this._layerNodes.get(layer) || null;
    }

    /**
     * 获取面板实例
     */
    public getPanelInstance(panelType: UIPanelType): any {
        const instance = this._panelInstances.get(panelType);
        return instance ? instance.component : null;
    }

    /**
     * 获取所有可见面板
     */
    public getVisiblePanels(): UIPanelType[] {
        const visiblePanels: UIPanelType[] = [];

        for (const [panelType, instance] of this._panelInstances) {
            if (instance.node.active) {
                visiblePanels.push(panelType);
            }
        }

        return visiblePanels;
    }

    /**
     * 获取面板历史
     */
    public getPanelHistory(): UIPanelType[] {
        return [...this._panelHistory];
    }

    /**
     * 获取面板组列表
     */
    public getPanelGroups(): Map<string, UIPanelType[]> {
        return new Map(this._panelGroups);
    }

    /**
     * 检查面板是否在动画中
     */
    public isPanelAnimating(panelType: UIPanelType): boolean {
        return this._animationQueue.has(panelType);
    }

    /**
     * 获取增强的UI统计信息
     */
    public getEnhancedUIStats(): any {
        const basicStats = this.getUIStats();

        return {
            ...basicStats,
            panelHistory: this._panelHistory.length,
            panelGroups: this._panelGroups.size,
            preloadQueue: this._preloadQueue.size,
            animatingPanels: this._animationQueue.size,
            visiblePanels: this.getVisiblePanels(),
            panelGroupsList: Array.from(this._panelGroups.keys())
        };
    }
}
