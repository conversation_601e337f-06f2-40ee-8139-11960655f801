/**
 * 技能计算服务
 * 负责技能相关的数值计算
 */

import { ISkillData, IPlayerSkill, SkillDamageType } from '../../config/interfaces/ISkillData';

/**
 * 技能计算服务
 */
export class SkillCalculationService {
    
    /**
     * 计算技能伤害
     */
    public static calculateSkillDamage(skillData: ISkillData, playerSkill: IPlayerSkill, casterStats?: any): number {
        const baseMultiplier = skillData.baseDamageMultiplier;
        const level = playerSkill.level;
        
        // 基础伤害计算
        let baseDamage = baseMultiplier * 100;
        
        // 等级加成
        const levelBonus = (level - 1) * 10;
        baseDamage += levelBonus;
        
        // 属性加成（如果有施法者属性）
        if (casterStats) {
            switch (skillData.damageType) {
                case SkillDamageType.Physical:
                    baseDamage += casterStats.attack || 0;
                    break;
                case SkillDamageType.Magical:
                    baseDamage += casterStats.magicPower || 0;
                    break;
            }
        }
        
        return Math.floor(baseDamage);
    }
    
    /**
     * 计算技能治疗量
     */
    public static calculateSkillHealing(skillData: ISkillData, playerSkill: IPlayerSkill, casterStats?: any): number {
        if (skillData.damageType !== SkillDamageType.Healing) {
            return 0;
        }
        
        const baseMultiplier = skillData.baseDamageMultiplier;
        const level = playerSkill.level;
        
        let baseHealing = baseMultiplier * 100;
        const levelBonus = (level - 1) * 10;
        baseHealing += levelBonus;
        
        // 治疗强度加成
        if (casterStats && casterStats.healingPower) {
            baseHealing += casterStats.healingPower;
        }
        
        return Math.floor(baseHealing);
    }
    
    /**
     * 计算技能法力消耗
     */
    public static calculateManaCost(skillData: ISkillData, playerSkill: IPlayerSkill, casterStats?: any): number {
        let manaCost = skillData.manaCost;
        
        // 等级减少法力消耗
        const levelReduction = Math.floor((playerSkill.level - 1) * 0.05 * manaCost);
        manaCost = Math.max(1, manaCost - levelReduction);
        
        // 法力效率加成
        if (casterStats && casterStats.manaEfficiency) {
            const reduction = Math.floor(manaCost * casterStats.manaEfficiency);
            manaCost = Math.max(1, manaCost - reduction);
        }
        
        return manaCost;
    }
    
    /**
     * 计算技能冷却时间
     */
    public static calculateCooldown(skillData: ISkillData, playerSkill: IPlayerSkill, casterStats?: any): number {
        let cooldown = skillData.cooldown;
        
        // 等级减少冷却时间
        const levelReduction = (playerSkill.level - 1) * 0.02 * cooldown;
        cooldown = Math.max(0.5, cooldown - levelReduction);
        
        // 冷却缩减加成
        if (casterStats && casterStats.cooldownReduction) {
            const reduction = cooldown * casterStats.cooldownReduction;
            cooldown = Math.max(0.5, cooldown - reduction);
        }
        
        return cooldown;
    }
    
    /**
     * 计算技能升级所需经验
     */
    public static calculateExpRequired(level: number): number {
        // 指数增长的经验需求
        return Math.floor(100 * Math.pow(1.5, level - 1));
    }
    
    /**
     * 计算暴击率
     */
    public static calculateCriticalChance(skillData: ISkillData, playerSkill: IPlayerSkill, casterStats?: any): number {
        let critChance = 0.05; // 基础5%暴击率
        
        // 等级提升暴击率
        critChance += (playerSkill.level - 1) * 0.005;
        
        // 属性加成
        if (casterStats && casterStats.criticalChance) {
            critChance += casterStats.criticalChance;
        }
        
        return Math.min(0.95, critChance); // 最大95%暴击率
    }
    
    /**
     * 计算暴击伤害倍率
     */
    public static calculateCriticalMultiplier(skillData: ISkillData, playerSkill: IPlayerSkill, casterStats?: any): number {
        let critMultiplier = 1.5; // 基础150%暴击伤害
        
        // 等级提升暴击伤害
        critMultiplier += (playerSkill.level - 1) * 0.05;
        
        // 属性加成
        if (casterStats && casterStats.criticalDamage) {
            critMultiplier += casterStats.criticalDamage;
        }
        
        return critMultiplier;
    }
    
    /**
     * 检查技能是否暴击
     */
    public static rollCritical(skillData: ISkillData, playerSkill: IPlayerSkill, casterStats?: any): boolean {
        const critChance = this.calculateCriticalChance(skillData, playerSkill, casterStats);
        return Math.random() < critChance;
    }
}
