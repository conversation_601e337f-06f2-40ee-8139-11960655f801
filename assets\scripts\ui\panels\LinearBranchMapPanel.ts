/**
 * 线性分支地图面板组件
 * 专门用于实现原型图中的中间交互区域
 * 支持线性分支的行为节点和拖拽导航
 */

import { _decorator, Component, Node, Vec3, Sprite, SpriteFrame, resources, UITransform, Color } from 'cc';
import { EventManager } from '../../managers/EventManager';
import { DraggableMapContainer } from '../components/DraggableMapContainer';
import { InteractionNode } from '../components/InteractionNode';
import { BehaviorType, IBehaviorData } from './BehaviorPanel';

const { ccclass, property } = _decorator;

/**
 * 分支节点数据接口
 */
export interface IBranchNodeData {
    /** 节点ID */
    id: string;
    
    /** 节点名称 */
    name: string;
    
    /** 节点描述 */
    description: string;
    
    /** 行为类型 */
    behaviorType: BehaviorType;
    
    /** 世界坐标位置 */
    position: Vec3;
    
    /** 是否已解锁 */
    unlocked: boolean;
    
    /** 前置节点ID列表 */
    prerequisites: string[];
    
    /** 解锁的后续节点ID列表 */
    unlocks: string[];
    
    /** 节点等级（用于分层显示） */
    level: number;
    
    /** 行为数据 */
    behaviorData?: IBehaviorData;
    
    /** 图标路径 */
    iconPath?: string;
    
    /** 自定义数据 */
    customData?: any;
}

/**
 * 地图分支配置
 */
export interface IBranchMapConfig {
    /** 地图背景图片路径 */
    backgroundImagePath: string;
    
    /** 地图大小 */
    mapSize: Vec3;
    
    /** 节点间距 */
    nodeSpacing: Vec3;
    
    /** 起始位置 */
    startPosition: Vec3;
    
    /** 分支数量 */
    branchCount: number;
    
    /** 每个分支的节点数量 */
    nodesPerBranch: number;
}

@ccclass('LinearBranchMapPanel')
export class LinearBranchMapPanel extends Component {
    
    @property({ type: Node, tooltip: '地图容器节点' })
    public mapContainer: Node | null = null;
    
    @property({ type: Node, tooltip: '背景节点' })
    public backgroundNode: Node | null = null;
    
    @property({ type: Node, tooltip: '节点容器' })
    public nodeContainer: Node | null = null;
    
    @property({ type: Node, tooltip: '连接线容器' })
    public connectionContainer: Node | null = null;
    
    @property({ tooltip: '背景图片路径' })
    public backgroundImagePath: string = 'ui/scene/BattleScene1';
    
    @property({ tooltip: '节点间距X' })
    public nodeSpacingX: number = 200;
    
    @property({ tooltip: '节点间距Y' })
    public nodeSpacingY: number = 150;
    
    @property({ tooltip: '分支数量' })
    public branchCount: number = 3;
    
    @property({ tooltip: '每分支节点数' })
    public nodesPerBranch: number = 5;
    
    // 私有属性
    private _draggableContainer: DraggableMapContainer | null = null;
    private _branchNodes: Map<string, InteractionNode> = new Map();
    private _nodeData: Map<string, IBranchNodeData> = new Map();
    private _mapConfig: IBranchMapConfig = {
        backgroundImagePath: 'ui/scene/BattleScene1',
        mapSize: new Vec3(1600, 1200, 0),
        nodeSpacing: new Vec3(200, 150, 0),
        startPosition: new Vec3(-600, 0, 0),
        branchCount: 3,
        nodesPerBranch: 5
    };

    protected onLoad(): void {
        console.log('🗺️ LinearBranchMapPanel: 线性分支地图面板加载');
        this.initializeComponents();
        this.setupDraggableContainer();
        this.initializeMapData();
    }

    protected start(): void {
        this.loadBackgroundImage();
        this.createBranchNodes();
        this.setupInitialView();
    }

    /**
     * 初始化组件
     */
    private initializeComponents(): void {
        // 自动查找或创建地图容器
        if (!this.mapContainer) {
            this.mapContainer = this.node.getChildByName('MapContainer');
            if (!this.mapContainer) {
                this.mapContainer = new Node('MapContainer');
                this.mapContainer.setParent(this.node);
                this.mapContainer.addComponent(UITransform);
            }
        }
        
        // 自动查找或创建背景节点
        if (!this.backgroundNode) {
            this.backgroundNode = this.mapContainer.getChildByName('Background');
            if (!this.backgroundNode) {
                this.backgroundNode = new Node('Background');
                this.backgroundNode.setParent(this.mapContainer);
                this.backgroundNode.addComponent(UITransform);
                this.backgroundNode.addComponent(Sprite);
            }
        }
        
        // 自动查找或创建节点容器
        if (!this.nodeContainer) {
            this.nodeContainer = this.mapContainer.getChildByName('NodeContainer');
            if (!this.nodeContainer) {
                this.nodeContainer = new Node('NodeContainer');
                this.nodeContainer.setParent(this.mapContainer);
                this.nodeContainer.addComponent(UITransform);
            }
        }
        
        // 自动查找或创建连接线容器
        if (!this.connectionContainer) {
            this.connectionContainer = this.mapContainer.getChildByName('ConnectionContainer');
            if (!this.connectionContainer) {
                this.connectionContainer = new Node('ConnectionContainer');
                this.connectionContainer.setParent(this.mapContainer);
                this.connectionContainer.addComponent(UITransform);
            }
        }
        
        console.log('🗺️ LinearBranchMapPanel: 组件初始化完成');
    }

    /**
     * 设置可拖拽容器
     */
    private setupDraggableContainer(): void {
        if (!this.mapContainer) return;
        
        this._draggableContainer = this.mapContainer.getComponent(DraggableMapContainer);
        if (!this._draggableContainer) {
            this._draggableContainer = this.mapContainer.addComponent(DraggableMapContainer);
        }
        
        // 配置拖拽参数
        this._draggableContainer.setBounds(-800, 800, 400, -400);
        this._draggableContainer.setConfig({
            enableDrag: true,
            enableZoom: true,
            dragDamping: 0.8,
            zoomSensitivity: 0.1,
            minScale: 0.5,
            maxScale: 1.5,
            bounds: { left: -800, right: 800, top: 400, bottom: -400 },
            enableInertia: true,
            inertiaDamping: 0.95
        });
    }

    /**
     * 初始化地图数据
     */
    private initializeMapData(): void {
        this._mapConfig.branchCount = this.branchCount;
        this._mapConfig.nodesPerBranch = this.nodesPerBranch;
        this._mapConfig.nodeSpacing = new Vec3(this.nodeSpacingX, this.nodeSpacingY, 0);
        this._mapConfig.backgroundImagePath = this.backgroundImagePath;
        
        // 生成分支节点数据
        this.generateBranchNodeData();
    }

    /**
     * 生成分支节点数据
     */
    private generateBranchNodeData(): void {
        const startX = this._mapConfig.startPosition.x;
        const startY = this._mapConfig.startPosition.y;
        const spacingX = this._mapConfig.nodeSpacing.x;
        const spacingY = this._mapConfig.nodeSpacing.y;
        
        // 创建起始节点
        const startNode: IBranchNodeData = {
            id: 'start',
            name: '起点',
            description: '冒险的开始',
            behaviorType: BehaviorType.Idle,
            position: new Vec3(startX, startY, 0),
            unlocked: true,
            prerequisites: [],
            unlocks: [],
            level: 0
        };
        
        this._nodeData.set(startNode.id, startNode);
        
        // 为每个分支生成节点
        for (let branch = 0; branch < this._mapConfig.branchCount; branch++) {
            const branchY = startY + (branch - Math.floor(this._mapConfig.branchCount / 2)) * spacingY;
            
            for (let level = 1; level <= this._mapConfig.nodesPerBranch; level++) {
                const nodeId = `branch_${branch}_level_${level}`;
                const nodeX = startX + level * spacingX;
                
                const nodeData: IBranchNodeData = {
                    id: nodeId,
                    name: `${this.getBranchName(branch)} - 第${level}层`,
                    description: `分支${branch + 1}的第${level}个节点`,
                    behaviorType: this.getBehaviorTypeByLevel(level),
                    position: new Vec3(nodeX, branchY, 0),
                    unlocked: level === 1, // 第一层默认解锁
                    prerequisites: level === 1 ? ['start'] : [`branch_${branch}_level_${level - 1}`],
                    unlocks: level < this._mapConfig.nodesPerBranch ? [`branch_${branch}_level_${level + 1}`] : [],
                    level: level
                };
                
                this._nodeData.set(nodeId, nodeData);
                
                // 更新前置节点的解锁列表
                if (level === 1) {
                    startNode.unlocks.push(nodeId);
                }
            }
        }
        
        console.log('🗺️ LinearBranchMapPanel: 生成节点数据完成', this._nodeData.size);
    }

    /**
     * 根据分支索引获取分支名称
     */
    private getBranchName(branchIndex: number): string {
        const branchNames = ['战斗路线', '探索路线', '技能路线', '商业路线', '社交路线'];
        return branchNames[branchIndex] || `分支${branchIndex + 1}`;
    }

    /**
     * 根据等级获取行为类型
     */
    private getBehaviorTypeByLevel(level: number): BehaviorType {
        const behaviorTypes = [BehaviorType.Attack, BehaviorType.Defend, BehaviorType.Skill, BehaviorType.Item, BehaviorType.Move];
        return behaviorTypes[(level - 1) % behaviorTypes.length];
    }

    /**
     * 加载背景图片
     */
    private loadBackgroundImage(): void {
        if (!this.backgroundNode) return;
        
        const sprite = this.backgroundNode.getComponent(Sprite);
        if (!sprite) return;
        
        resources.load(this._mapConfig.backgroundImagePath + '/spriteFrame', SpriteFrame, (err, spriteFrame) => {
            if (err) {
                console.error('🗺️ LinearBranchMapPanel: 加载背景图片失败', err);
                return;
            }
            
            sprite.spriteFrame = spriteFrame;
            
            // 设置背景大小
            const uiTransform = this.backgroundNode!.getComponent(UITransform);
            if (uiTransform) {
                uiTransform.setContentSize(this._mapConfig.mapSize.x, this._mapConfig.mapSize.y);
            }
            
            console.log('🗺️ LinearBranchMapPanel: 背景图片加载完成');
        });
    }

    /**
     * 创建分支节点
     */
    private createBranchNodes(): void {
        if (!this.nodeContainer) return;
        
        this._nodeData.forEach((nodeData, nodeId) => {
            this.createSingleNode(nodeData);
        });
        
        console.log('🗺️ LinearBranchMapPanel: 分支节点创建完成', this._branchNodes.size);
    }

    /**
     * 创建单个节点
     */
    private createSingleNode(nodeData: IBranchNodeData): void {
        if (!this.nodeContainer) return;
        
        // 创建节点对象
        const nodeObj = new Node(nodeData.id);
        nodeObj.setParent(this.nodeContainer);
        nodeObj.setPosition(nodeData.position);
        
        // 添加UITransform
        const uiTransform = nodeObj.addComponent(UITransform);
        uiTransform.setContentSize(80, 80);
        
        // 添加Sprite组件
        const sprite = nodeObj.addComponent(Sprite);
        sprite.color = this.getNodeColorByType(nodeData.behaviorType);
        
        // 添加InteractionNode组件
        const interactionNode = nodeObj.addComponent(InteractionNode);
        
        // 转换为InteractionNode需要的数据格式
        const interactionData = {
            id: nodeData.id,
            name: nodeData.name,
            description: nodeData.description,
            type: nodeData.behaviorType,
            worldPosition: nodeData.position,
            unlocked: nodeData.unlocked,
            prerequisites: nodeData.prerequisites,
            unlocks: nodeData.unlocks,
            behaviorData: nodeData.behaviorData,
            iconPath: nodeData.iconPath,
            customData: nodeData.customData
        };
        
        interactionNode.initialize(interactionData);
        
        // 设置点击回调
        interactionNode.setOnClickCallback((clickedData) => {
            this.onNodeClicked(nodeData);
        });
        
        // 存储节点引用
        this._branchNodes.set(nodeData.id, interactionNode);
        
        console.log('🗺️ LinearBranchMapPanel: 创建节点', nodeData.name);
    }

    /**
     * 根据行为类型获取节点颜色
     */
    private getNodeColorByType(behaviorType: BehaviorType): Color {
        switch (behaviorType) {
            case BehaviorType.Attack:
                return new Color(255, 100, 100, 255); // 红色
            case BehaviorType.Defend:
                return new Color(100, 100, 255, 255); // 蓝色
            case BehaviorType.Skill:
                return new Color(255, 255, 100, 255); // 黄色
            case BehaviorType.Item:
                return new Color(100, 255, 100, 255); // 绿色
            case BehaviorType.Move:
                return new Color(255, 100, 255, 255); // 紫色
            default:
                return new Color(200, 200, 200, 255); // 灰色
        }
    }

    /**
     * 节点点击处理
     */
    private onNodeClicked(nodeData: IBranchNodeData): void {
        console.log('🗺️ LinearBranchMapPanel: 节点被点击', nodeData.name);
        
        if (!nodeData.unlocked) {
            console.warn('🗺️ LinearBranchMapPanel: 节点未解锁', nodeData.name);
            this.showNodeLockedMessage(nodeData);
            return;
        }
        
        // 触发行为
        this.triggerNodeBehavior(nodeData);
        
        // 检查并解锁后续节点
        this.checkAndUnlockNodes(nodeData);
        
        // 发送事件
        EventManager.getInstance().emit('branch_node_clicked', {
            nodeData: nodeData,
            behaviorType: nodeData.behaviorType
        });
    }

    /**
     * 触发节点行为
     */
    private triggerNodeBehavior(nodeData: IBranchNodeData): void {
        // 发送行为触发事件，让BehaviorPanel处理
        EventManager.getInstance().emit('trigger_behavior', {
            behaviorType: nodeData.behaviorType,
            nodeData: nodeData
        });
    }

    /**
     * 检查并解锁后续节点
     */
    private checkAndUnlockNodes(triggeredNode: IBranchNodeData): void {
        triggeredNode.unlocks.forEach(unlockNodeId => {
            const nodeData = this._nodeData.get(unlockNodeId);
            const interactionNode = this._branchNodes.get(unlockNodeId);
            
            if (nodeData && interactionNode && !nodeData.unlocked) {
                // 检查前置条件
                const prerequisitesMet = nodeData.prerequisites.every(prereqId => {
                    const prereqNode = this._nodeData.get(prereqId);
                    return prereqNode && prereqNode.unlocked;
                });
                
                if (prerequisitesMet) {
                    nodeData.unlocked = true;
                    interactionNode.setUnlocked(true);
                    
                    console.log('🔓 LinearBranchMapPanel: 节点已解锁', nodeData.name);
                    
                    // 发送解锁事件
                    EventManager.getInstance().emit('branch_node_unlocked', {
                        nodeId: unlockNodeId,
                        nodeData: nodeData
                    });
                }
            }
        });
    }

    /**
     * 显示节点锁定消息
     */
    private showNodeLockedMessage(nodeData: IBranchNodeData): void {
        console.log('🔒 LinearBranchMapPanel: 节点被锁定', nodeData.name);
        
        EventManager.getInstance().emit('show_node_locked_message', {
            nodeData: nodeData,
            prerequisites: nodeData.prerequisites
        });
    }

    /**
     * 设置初始视图
     */
    private setupInitialView(): void {
        if (!this.mapContainer) return;
        
        // 设置初始缩放
        this.mapContainer.setScale(1.0, 1.0, 1.0);
        
        // 聚焦到起始节点
        this.focusOnNode('start');
    }

    // ==================== 公共API ====================

    /**
     * 聚焦到指定节点
     */
    public focusOnNode(nodeId: string): void {
        const nodeData = this._nodeData.get(nodeId);
        if (!nodeData || !this._draggableContainer) return;
        
        const targetPosition = new Vec3(-nodeData.position.x, -nodeData.position.y, 0);
        this._draggableContainer.moveTo(targetPosition, 0.5);
        
        console.log('🗺️ LinearBranchMapPanel: 聚焦到节点', nodeData.name);
    }

    /**
     * 解锁指定节点
     */
    public unlockNode(nodeId: string): void {
        const nodeData = this._nodeData.get(nodeId);
        const interactionNode = this._branchNodes.get(nodeId);
        
        if (nodeData && interactionNode) {
            nodeData.unlocked = true;
            interactionNode.setUnlocked(true);
            
            console.log('🔓 LinearBranchMapPanel: 手动解锁节点', nodeData.name);
        }
    }

    /**
     * 获取节点数据
     */
    public getNodeData(nodeId: string): IBranchNodeData | undefined {
        return this._nodeData.get(nodeId);
    }

    /**
     * 显示面板
     */
    public showPanel(): void {
        this.node.active = true;
        console.log('🗺️ LinearBranchMapPanel: 显示面板');
    }

    /**
     * 隐藏面板
     */
    public hidePanel(): void {
        this.node.active = false;
        console.log('🗺️ LinearBranchMapPanel: 隐藏面板');
    }
}
