# Copyright (c) 2025 "放置". All rights reserved.

# 游戏体验场景协议架构设计

## 🎯 概念定义

### 游戏体验场景 (Game Experience Scene)
- **定义**: 玩家在游戏中体验的功能区域（鱼塘、农场、森林等）
- **特点**: 包含玩法逻辑、奖励机制、解锁条件等
- **与Cocos Scene的区别**: 这是游戏内容层面，不是引擎技术层面

### Cocos Creator场景文件 (Cocos Scene File)
- **定义**: 引擎层面的场景文件（Launch.scene、Game.scene等）
- **特点**: 包含节点层级、组件配置、预制体等
- **职责**: 技术实现和渲染层面

## 🏗️ 协议分层架构

### 第一层：UI事件协议 (UI Event Protocol)
**切换位置**: MapPanel → MapPanelController
```typescript
interface UIEventProtocol {
  type: "AREA_BUTTON_CLICKED" | "AREA_INFO_REQUESTED";
  payload: {
    areaId: string;
    buttonType: "enter" | "info" | "unlock";
    playerData?: PlayerBasicInfo;
  };
  metadata: {
    componentId: string;
    timestamp: number;
  };
}
```

### 第二层：业务逻辑协议 (Business Logic Protocol)
**切换位置**: MapPanelController → SceneManager/LocationManager
```typescript
interface BusinessProtocol {
  type: "AREA_SWITCH_REQUEST" | "AREA_DATA_REQUEST" | "AREA_UNLOCK_REQUEST";
  payload: {
    fromAreaId?: string;
    toAreaId: string;
    switchType: "normal" | "fast" | "preload";
    validateConditions: boolean;
  };
  context: {
    playerId: string;
    sessionId: string;
    currentState: PlayerState;
  };
}
```

### 第三层：数据管理协议 (Data Management Protocol)
**切换位置**: SceneManager → ConfigManager/CacheManager
```typescript
interface DataProtocol {
  type: "CONFIG_REQUEST" | "CACHE_REQUEST" | "STORAGE_REQUEST";
  payload: {
    resourceType: "area_config" | "player_progress" | "unlock_status";
    resourceId: string;
    cachePolicy: CachePolicy;
  };
  options: {
    includeMetadata: boolean;
    timeout: number;
  };
}
```

### 第四层：网络通信协议 (Network Protocol)
**切换位置**: NetworkManager → Backend API
```typescript
interface NetworkProtocol {
  type: "HTTP_REQUEST" | "WEBSOCKET_MESSAGE";
  payload: {
    endpoint: string;
    method: "GET" | "POST" | "PUT" | "DELETE";
    data: any;
    headers: Record<string, string>;
  };
  config: {
    timeout: number;
    retryCount: number;
    priority: "high" | "normal" | "low";
  };
}
```

## 🔄 协议流转示例

### 完整的区域切换流程
```
1. [UI层] 玩家点击"农场"按钮
   MapPanel → UIEventProtocol → MapPanelController

2. [业务层] 处理区域切换逻辑
   MapPanelController → BusinessProtocol → SceneManager

3. [数据层] 获取区域配置和玩家数据
   SceneManager → DataProtocol → ConfigManager
   SceneManager → DataProtocol → PlayerDataManager

4. [网络层] 请求服务器数据
   SceneManager → NetworkProtocol → ApiClient → Backend

5. [响应链] 数据返回和处理
   Backend → NetworkProtocol → ApiClient
   ApiClient → DataProtocol → SceneManager
   SceneManager → BusinessProtocol → MapPanelController
   MapPanelController → UIEventProtocol → MapPanel

6. [渲染层] 更新UI显示
   MapPanel → 更新区域显示和玩家状态
```

## 🧩 协议解耦设计

### 模块间通信矩阵
```
┌─────────────┬─────────┬─────────┬─────────┬─────────┐
│   模块      │ UI层    │ 业务层  │ 数据层  │ 网络层  │
├─────────────┼─────────┼─────────┼─────────┼─────────┤
│ UI层        │   -     │ UI协议  │   ❌    │   ❌    │
│ 业务层      │ UI协议  │   -     │ 数据协议│ 网络协议│
│ 数据层      │   ❌    │ 数据协议│   -     │   ❌    │
│ 网络层      │   ❌    │ 网络协议│   ❌    │   -     │
└─────────────┴─────────┴─────────┴─────────┴─────────┘
```

### 协议边界规则
1. **严格分层**: 只能与相邻层通信
2. **单向依赖**: 上层依赖下层，下层不依赖上层
3. **协议隔离**: 每层使用独立的协议格式
4. **错误传播**: 错误信息向上层传播

## 🔧 协议路由系统

### 协议路由器设计
```typescript
class ProtocolRouter {
  private handlers = new Map<string, ProtocolHandler>();
  private middleware: ProtocolMiddleware[] = [];
  
  // 注册协议处理器
  register(protocol: string, handler: ProtocolHandler): void;
  
  // 路由协议消息
  route(message: ProtocolMessage): Promise<ProtocolResponse>;
  
  // 添加中间件
  use(middleware: ProtocolMiddleware): void;
}
```

### 协议适配器
```typescript
// UI适配器：UI事件 → 业务协议
class UIProtocolAdapter {
  adaptButtonClick(event: ButtonClickEvent): BusinessProtocol;
  adaptUIState(state: UIState): BusinessProtocol;
}

// 网络适配器：业务协议 → 网络请求
class NetworkProtocolAdapter {
  adaptToHTTP(protocol: BusinessProtocol): HTTPRequest;
  adaptToWebSocket(protocol: BusinessProtocol): WebSocketMessage;
}

// 数据适配器：业务协议 → 数据操作
class DataProtocolAdapter {
  adaptToQuery(protocol: BusinessProtocol): DataQuery;
  adaptToCache(protocol: BusinessProtocol): CacheOperation;
}
```

## 🛡️ 协议中间件系统

### 中间件类型
```typescript
// 日志中间件
class LoggingMiddleware implements ProtocolMiddleware {
  async process(message: ProtocolMessage, next: NextFunction): Promise<ProtocolResponse> {
    console.log(`[${message.type}] ${message.payload}`);
    const response = await next(message);
    console.log(`[${message.type}] Response: ${response.status}`);
    return response;
  }
}

// 验证中间件
class ValidationMiddleware implements ProtocolMiddleware {
  async process(message: ProtocolMessage, next: NextFunction): Promise<ProtocolResponse> {
    if (!this.validate(message)) {
      throw new ProtocolError("INVALID_MESSAGE", "消息格式无效");
    }
    return next(message);
  }
}

// 缓存中间件
class CacheMiddleware implements ProtocolMiddleware {
  async process(message: ProtocolMessage, next: NextFunction): Promise<ProtocolResponse> {
    const cached = await this.getCache(message);
    if (cached) return cached;
    
    const response = await next(message);
    await this.setCache(message, response);
    return response;
  }
}
```

## 📊 协议性能优化

### 批量处理
```typescript
// 批量协议处理器
class BatchProtocolProcessor {
  private queue: ProtocolMessage[] = [];
  private timer: NodeJS.Timeout | null = null;
  
  // 添加到批量队列
  enqueue(message: ProtocolMessage): void {
    this.queue.push(message);
    this.scheduleBatch();
  }
  
  // 批量处理
  private async processBatch(): Promise<void> {
    const batch = this.queue.splice(0);
    const responses = await this.processBatchMessages(batch);
    this.notifyResponses(responses);
  }
}
```

### 协议压缩
```typescript
// 协议压缩器
class ProtocolCompressor {
  // 压缩协议消息
  compress(message: ProtocolMessage): CompressedMessage;
  
  // 解压协议消息
  decompress(compressed: CompressedMessage): ProtocolMessage;
  
  // 差异化更新
  createDelta(oldMessage: ProtocolMessage, newMessage: ProtocolMessage): DeltaMessage;
}
```

## 🧪 协议测试框架

### 协议模拟器
```typescript
class ProtocolSimulator {
  // 模拟UI事件
  simulateUIEvent(eventType: string, payload: any): UIEventProtocol;
  
  // 模拟网络响应
  simulateNetworkResponse(request: NetworkProtocol): NetworkResponse;
  
  // 模拟错误场景
  simulateError(errorType: string): ProtocolError;
}
```

### 协议验证器
```typescript
class ProtocolValidator {
  // 验证协议格式
  validateFormat(message: ProtocolMessage): ValidationResult;
  
  // 验证协议流程
  validateFlow(messages: ProtocolMessage[]): FlowValidationResult;
  
  // 性能基准测试
  benchmark(protocolType: string): PerformanceMetrics;
}
```

## 🎯 实际应用场景

### 场景1：玩家点击进入农场
```
UI事件: "农场按钮点击"
↓ [UI协议]
业务逻辑: "处理农场进入请求"
↓ [数据协议] + [网络协议]
数据获取: "农场配置" + "玩家进度" + "服务器状态"
↓ [响应链]
UI更新: "显示农场界面和玩家状态"
```

### 场景2：解锁新区域
```
UI事件: "解锁森林区域"
↓ [UI协议]
业务逻辑: "验证解锁条件"
↓ [数据协议] + [网络协议]
条件检查: "玩家等级" + "完成任务" + "消耗物品"
↓ [网络协议]
服务器更新: "更新玩家进度" + "解锁区域"
↓ [响应链]
UI更新: "显示解锁动画" + "更新区域列表"
```

这个协议架构确保了每个模块的职责清晰，避免了manager脚本之间的强耦合，实现了真正的模块化和可扩展性。
