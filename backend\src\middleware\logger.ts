import { Request, Response, NextFunction } from 'express';
import { Logger, LogLevel } from '../utils/logger';

/**
 * 请求日志配置接口
 */
export interface RequestLoggerConfig {
  enabled: boolean;
  logLevel: LogLevel;
  logBody: boolean;
  logHeaders: boolean;
  logQuery: boolean;
  logParams: boolean;
  maxBodySize: number;
  sensitiveFields: string[];
  skipPaths: string[];
  skipMethods: string[];
  includeResponseBody: boolean;
  maxResponseBodySize: number;
}

/**
 * 错误日志配置接口
 */
export interface ErrorLoggerConfig {
  enabled: boolean;
  logLevel: LogLevel;
  logStack: boolean;
  logRequest: boolean;
  sensitiveFields: string[];
}

/**
 * 扩展Request接口
 */
declare global {
  namespace Express {
    interface Request {
      requestId?: string;
      startTime?: number;
      logger?: typeof Logger;
    }
  }
}

/**
 * 获取默认请求日志配置
 */
const getDefaultRequestLoggerConfig = (): RequestLoggerConfig => ({
  enabled: process.env['REQUEST_LOGGER_ENABLED'] !== 'false',
  logLevel: (process.env['REQUEST_LOGGER_LEVEL'] as LogLevel) || LogLevel.INFO,
  logBody: process.env['REQUEST_LOGGER_BODY'] === 'true',
  logHeaders: process.env['REQUEST_LOGGER_HEADERS'] === 'true',
  logQuery: process.env['REQUEST_LOGGER_QUERY'] !== 'false',
  logParams: process.env['REQUEST_LOGGER_PARAMS'] !== 'false',
  maxBodySize: parseInt(process.env['REQUEST_LOGGER_MAX_BODY_SIZE'] || '1024'),
  sensitiveFields: (process.env['REQUEST_LOGGER_SENSITIVE_FIELDS'] || 'password,token,secret,key,auth,authorization').split(','),
  skipPaths: (process.env['REQUEST_LOGGER_SKIP_PATHS'] || '/health,/metrics,/favicon.ico').split(','),
  skipMethods: (process.env['REQUEST_LOGGER_SKIP_METHODS'] || '').split(',').filter(Boolean),
  includeResponseBody: process.env['REQUEST_LOGGER_RESPONSE_BODY'] === 'true',
  maxResponseBodySize: parseInt(process.env['REQUEST_LOGGER_MAX_RESPONSE_BODY_SIZE'] || '1024'),
});

/**
 * 获取默认错误日志配置
 */
const getDefaultErrorLoggerConfig = (): ErrorLoggerConfig => ({
  enabled: process.env['ERROR_LOGGER_ENABLED'] !== 'false',
  logLevel: (process.env['ERROR_LOGGER_LEVEL'] as LogLevel) || LogLevel.ERROR,
  logStack: process.env['ERROR_LOGGER_STACK'] !== 'false',
  logRequest: process.env['ERROR_LOGGER_REQUEST'] !== 'false',
  sensitiveFields: (process.env['ERROR_LOGGER_SENSITIVE_FIELDS'] || 'password,token,secret,key,auth,authorization').split(','),
});

/**
 * 清理敏感数据
 */
const sanitizeData = (data: any, sensitiveFields: string[]): any => {
  if (!data || typeof data !== 'object') {
    return data;
  }

  if (Array.isArray(data)) {
    return data.map(item => sanitizeData(item, sensitiveFields));
  }

  const sanitized = { ...data };
  
  for (const field of sensitiveFields) {
    const lowerField = field.toLowerCase();
    for (const key in sanitized) {
      if (key.toLowerCase().includes(lowerField)) {
        sanitized[key] = '[REDACTED]';
      }
    }
  }

  return sanitized;
};

/**
 * 截断数据
 */
const truncateData = (data: any, maxSize: number): any => {
  const str = typeof data === 'string' ? data : JSON.stringify(data);
  if (str.length <= maxSize) {
    return data;
  }
  
  return str.substring(0, maxSize) + '... [TRUNCATED]';
};

/**
 * 获取客户端IP
 */
const getClientIP = (req: Request): string => {
  return (
    req.headers['x-forwarded-for'] as string ||
    req.headers['x-real-ip'] as string ||
    req.connection?.remoteAddress ||
    req.socket?.remoteAddress ||
    'unknown'
  );
};

/**
 * 请求日志中间件
 */
export const requestLogger = (config?: Partial<RequestLoggerConfig>) => {
  const finalConfig = { ...getDefaultRequestLoggerConfig(), ...config };

  return (req: Request, res: Response, next: NextFunction): void => {
    if (!finalConfig.enabled) {
      return next();
    }

    // 跳过指定路径
    if (finalConfig.skipPaths.some(path => req.path.startsWith(path))) {
      return next();
    }

    // 跳过指定方法
    if (finalConfig.skipMethods.includes(req.method)) {
      return next();
    }

    // 生成请求ID
    req.requestId = Logger.generateRequestId();
    req.startTime = Date.now();

    // 设置日志上下文
    Logger.setContext({
      requestId: req.requestId,
      userId: (req as any).user?.id,
      sessionId: (req as any).session?.id,
    });

    // 准备请求日志数据
    const requestData: any = {
      requestId: req.requestId,
      method: req.method,
      url: req.url,
      path: req.path,
      ip: getClientIP(req),
      userAgent: req.headers['user-agent'],
    };

    // 添加查询参数
    if (finalConfig.logQuery && Object.keys(req.query).length > 0) {
      requestData.query = sanitizeData(req.query, finalConfig.sensitiveFields);
    }

    // 添加路由参数
    if (finalConfig.logParams && Object.keys(req.params).length > 0) {
      requestData.params = sanitizeData(req.params, finalConfig.sensitiveFields);
    }

    // 添加请求头
    if (finalConfig.logHeaders) {
      requestData.headers = sanitizeData(req.headers, finalConfig.sensitiveFields);
    }

    // 添加请求体
    if (finalConfig.logBody && req.body) {
      const sanitizedBody = sanitizeData(req.body, finalConfig.sensitiveFields);
      requestData.body = truncateData(sanitizedBody, finalConfig.maxBodySize);
    }

    // 记录请求开始日志
    Logger.structured(finalConfig.logLevel, `请求开始: ${req.method} ${req.path}`, requestData);

    // 保存原始的res.end方法
    const originalEnd = res.end;
    const originalSend = res.send;

    let responseBody: any;

    // 拦截响应体
    if (finalConfig.includeResponseBody) {
      res.send = function(body: any) {
        responseBody = body;
        return originalSend.call(this, body);
      };
    }

    // 拦截res.end方法来记录响应日志
    res.end = function(chunk?: any, encoding?: any) {
      const duration = Date.now() - (req.startTime || Date.now());
      
      // 准备响应日志数据
      const responseData: any = {
        requestId: req.requestId,
        method: req.method,
        url: req.url,
        path: req.path,
        statusCode: res.statusCode,
        duration,
        contentLength: res.get('content-length'),
      };

      // 添加响应体
      if (finalConfig.includeResponseBody && responseBody) {
        const sanitizedBody = sanitizeData(responseBody, finalConfig.sensitiveFields);
        responseData.responseBody = truncateData(sanitizedBody, finalConfig.maxResponseBodySize);
      }

      // 确定日志级别
      let logLevel = finalConfig.logLevel;
      if (res.statusCode >= 500) {
        logLevel = LogLevel.ERROR;
      } else if (res.statusCode >= 400) {
        logLevel = LogLevel.WARN;
      } else if (duration > 1000) {
        logLevel = LogLevel.WARN;
      }

      // 记录响应日志
      Logger.structured(logLevel, `请求完成: ${req.method} ${req.path}`, responseData);

      // 清除日志上下文
      Logger.clearContext();

      // 调用原始的end方法
      return originalEnd.call(this, chunk, encoding);
    };

    next();
  };
};

/**
 * 错误日志中间件
 */
export const errorLogger = (config?: Partial<ErrorLoggerConfig>) => {
  const finalConfig = { ...getDefaultErrorLoggerConfig(), ...config };

  return (err: Error, req: Request, res: Response, next: NextFunction): void => {
    if (!finalConfig.enabled) {
      return next(err);
    }

    // 准备错误日志数据
    const errorData: any = {
      requestId: req.requestId,
      error: {
        name: err.name,
        message: err.message,
      },
    };

    // 添加错误堆栈
    if (finalConfig.logStack) {
      errorData.error.stack = err.stack;
    }

    // 添加请求信息
    if (finalConfig.logRequest) {
      errorData.request = {
        method: req.method,
        url: req.url,
        path: req.path,
        ip: getClientIP(req),
        userAgent: req.headers['user-agent'],
        headers: sanitizeData(req.headers, finalConfig.sensitiveFields),
        query: sanitizeData(req.query, finalConfig.sensitiveFields),
        params: sanitizeData(req.params, finalConfig.sensitiveFields),
        body: sanitizeData(req.body, finalConfig.sensitiveFields),
      };
    }

    // 添加用户信息
    if ((req as any).user) {
      errorData.user = {
        id: (req as any).user.id,
        username: (req as any).user.username,
      };
    }

    // 记录错误日志
    Logger.structured(finalConfig.logLevel, `请求错误: ${err.message}`, errorData);

    next(err);
  };
};

/**
 * 性能监控中间件
 */
export const performanceLogger = (threshold: number = 1000) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const startTime = Date.now();
    const startMemory = process.memoryUsage();

    // 保存原始的res.end方法
    const originalEnd = res.end;

    res.end = function(chunk?: any, encoding?: any) {
      const duration = Date.now() - startTime;
      const endMemory = process.memoryUsage();

      // 只记录超过阈值的请求
      if (duration > threshold) {
        Logger.performance(`${req.method} ${req.path}`, duration, {
          requestId: req.requestId,
          statusCode: res.statusCode,
          memoryDelta: {
            rss: endMemory.rss - startMemory.rss,
            heapUsed: endMemory.heapUsed - startMemory.heapUsed,
          },
          threshold,
        });
      }

      return originalEnd.call(this, chunk, encoding);
    };

    next();
  };
};

/**
 * 安全事件日志中间件
 */
export const securityLogger = () => {
  return (req: Request, res: Response, next: NextFunction): void => {
    // 检测可疑活动
    const suspiciousPatterns = [
      /\.\.\//,  // 路径遍历
      /<script/i, // XSS
      /union.*select/i, // SQL注入
      /javascript:/i, // JavaScript协议
    ];

    const checkSuspicious = (value: string): boolean => {
      return suspiciousPatterns.some(pattern => pattern.test(value));
    };

    // 检查URL
    if (checkSuspicious(req.url)) {
      Logger.securityEvent('suspicious_url', {
        requestId: req.requestId,
        url: req.url,
        ip: getClientIP(req),
        userAgent: req.headers['user-agent'],
      });
    }

    // 检查请求体
    if (req.body && typeof req.body === 'object') {
      const bodyStr = JSON.stringify(req.body);
      if (checkSuspicious(bodyStr)) {
        Logger.securityEvent('suspicious_body', {
          requestId: req.requestId,
          ip: getClientIP(req),
          userAgent: req.headers['user-agent'],
        });
      }
    }

    // 检查异常的请求头
    const suspiciousHeaders = ['x-forwarded-host', 'x-original-url', 'x-rewrite-url'];
    for (const header of suspiciousHeaders) {
      if (req.headers[header]) {
        Logger.securityEvent('suspicious_header', {
          requestId: req.requestId,
          header,
          value: req.headers[header],
          ip: getClientIP(req),
        });
      }
    }

    next();
  };
};
