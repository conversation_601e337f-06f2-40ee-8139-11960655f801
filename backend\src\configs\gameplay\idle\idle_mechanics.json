{"// Copyright (c) 2025 \"放置\". All rights reserved.": "", "version": "1.0.0", "config_type": "idle_mechanics", "last_updated": "2025-01-10T00:00:00Z", "checksum": "ghi789jkl012", "idle_settings": {"max_offline_duration": 86400, "offline_efficiency": 0.8, "auto_save_interval": 30, "progress_calculation_interval": 1, "max_stored_progress": 1000}, "auto_battle": {"enabled": true, "battle_interval": 5, "auto_loot": true, "auto_sell_common": false, "auto_use_potions": true, "retreat_on_low_health": true, "health_retreat_threshold": 0.2, "target_selection": "weakest_first"}, "idle_bonuses": {"consecutive_days": {"day_1": {"gold_multiplier": 1.0, "exp_multiplier": 1.0}, "day_2": {"gold_multiplier": 1.1, "exp_multiplier": 1.1}, "day_3": {"gold_multiplier": 1.2, "exp_multiplier": 1.2}, "day_7": {"gold_multiplier": 1.5, "exp_multiplier": 1.5}, "day_14": {"gold_multiplier": 2.0, "exp_multiplier": 2.0}, "day_30": {"gold_multiplier": 3.0, "exp_multiplier": 3.0}}, "vip_levels": {"vip_0": {"offline_efficiency": 0.8, "idle_speed": 1.0}, "vip_1": {"offline_efficiency": 0.85, "idle_speed": 1.1}, "vip_2": {"offline_efficiency": 0.9, "idle_speed": 1.2}, "vip_3": {"offline_efficiency": 0.95, "idle_speed": 1.3}, "vip_4": {"offline_efficiency": 1.0, "idle_speed": 1.5}}}, "progress_tracking": {"activities": {"combat": {"enabled": true, "exp_rate": 1.0, "gold_rate": 1.0, "item_drop_rate": 1.0}, "training": {"enabled": true, "exp_rate": 0.5, "gold_rate": 0.2, "skill_exp_rate": 2.0}, "meditation": {"enabled": true, "mana_regen_rate": 3.0, "skill_cooldown_reduction": 0.1}}}, "offline_calculation": {"max_iterations": 1000, "time_step": 60, "events": {"level_up": {"probability": 0.1, "bonus_multiplier": 1.5}, "rare_drop": {"probability": 0.05, "item_quality_bonus": 1}, "boss_encounter": {"probability": 0.02, "reward_multiplier": 3.0}}}, "formulas": {"offline_income": "base_income * offline_hours * offline_efficiency * vip_multiplier", "idle_exp_gain": "base_exp_rate * time_multiplier * activity_multiplier", "auto_battle_success": "character_power / (character_power + enemy_power)", "consecutive_bonus": "base_value * (1 + consecutive_days * 0.1)"}, "limits": {"max_offline_claim": 24, "max_auto_battles_per_hour": 720, "max_idle_income_multiplier": 10.0, "min_battle_interval": 1}}