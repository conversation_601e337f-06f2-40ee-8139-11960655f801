import swaggerJSDoc = require('swagger-jsdoc');
import swaggerUi = require('swagger-ui-express');
import express = require('express');
import { Logger } from '../utils/logger';

/**
 * Swagger API文档配置
 */

const swaggerDefinition = {
  openapi: '3.0.0',
  info: {
    title: '放置游戏 API',
    version: '1.0.0',
    description: '放置游戏后端API文档',
    contact: {
      name: 'IdleGame Team',
      email: '<EMAIL>',
    },
    license: {
      name: 'MIT',
      url: 'https://opensource.org/licenses/MIT',
    },
  },
  servers: [
    {
      url: 'http://localhost:3000',
      description: '开发环境',
    },
    {
      url: 'https://api.idlegame.com',
      description: '生产环境',
    },
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'JWT认证令牌',
      },
    },
    schemas: {
      Error: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            example: false,
          },
          message: {
            type: 'string',
            example: '错误信息',
          },
          error: {
            type: 'string',
            example: 'ERROR_CODE',
          },
          errorCode: {
            type: 'string',
            example: 'ERROR_CODE',
          },
          timestamp: {
            type: 'string',
            format: 'date-time',
            example: '2023-01-01T00:00:00.000Z',
          },
        },
      },
      Success: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            example: true,
          },
          message: {
            type: 'string',
            example: '操作成功',
          },
          data: {
            type: 'object',
            description: '响应数据',
          },
        },
      },
      User: {
        type: 'object',
        properties: {
          userId: {
            type: 'string',
            example: 'user_123456',
          },
          username: {
            type: 'string',
            example: 'player001',
          },
          email: {
            type: 'string',
            format: 'email',
            example: '<EMAIL>',
          },
          level: {
            type: 'integer',
            example: 15,
          },
          experience: {
            type: 'integer',
            example: 2500,
          },
          coins: {
            type: 'integer',
            example: 10000,
          },
          gems: {
            type: 'integer',
            example: 250,
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
          },
          lastLoginAt: {
            type: 'string',
            format: 'date-time',
          },
        },
      },
      Character: {
        type: 'object',
        properties: {
          characterId: {
            type: 'string',
            example: 'char_123456',
          },
          name: {
            type: 'string',
            example: '无名剑客',
          },
          level: {
            type: 'integer',
            example: 10,
          },
          experience: {
            type: 'integer',
            example: 1500,
          },
          attributes: {
            type: 'object',
            properties: {
              strength: { type: 'integer', example: 15 },
              agility: { type: 'integer', example: 12 },
              intelligence: { type: 'integer', example: 10 },
              vitality: { type: 'integer', example: 18 },
              spirit: { type: 'integer', example: 8 },
            },
          },
          stats: {
            type: 'object',
            properties: {
              health: { type: 'integer', example: 180 },
              mana: { type: 'integer', example: 80 },
              attack: { type: 'integer', example: 25 },
              defense: { type: 'integer', example: 15 },
              speed: { type: 'integer', example: 20 },
            },
          },
          sect: {
            type: 'string',
            nullable: true,
            example: 'wudang',
          },
          cultivation: {
            type: 'object',
            properties: {
              realm: { type: 'string', example: '练气期' },
              stage: { type: 'integer', example: 3 },
              progress: { type: 'integer', example: 750 },
            },
          },
        },
      },
      Battle: {
        type: 'object',
        properties: {
          battleId: {
            type: 'string',
            example: 'battle_123456',
          },
          type: {
            type: 'string',
            enum: ['pve', 'pvp'],
            example: 'pve',
          },
          enemy: {
            type: 'object',
            properties: {
              name: { type: 'string', example: '野狼' },
              level: { type: 'integer', example: 5 },
              health: { type: 'integer', example: 120 },
              attack: { type: 'integer', example: 18 },
              defense: { type: 'integer', example: 8 },
            },
          },
          player: {
            type: 'object',
            properties: {
              health: { type: 'integer', example: 180 },
              mana: { type: 'integer', example: 80 },
              attack: { type: 'integer', example: 25 },
              defense: { type: 'integer', example: 15 },
            },
          },
          startedAt: {
            type: 'string',
            format: 'date-time',
          },
        },
      },
    },
  },
  tags: [
    {
      name: 'Authentication',
      description: '认证相关接口',
    },
    {
      name: 'Users',
      description: '用户管理接口',
    },
    {
      name: 'Game',
      description: '游戏逻辑接口',
    },
    {
      name: 'Character',
      description: '角色系统接口',
    },
    {
      name: 'Battle',
      description: '战斗系统接口',
    },
    {
      name: 'Social',
      description: '社交功能接口',
    },
  ],
};

const options = {
  definition: swaggerDefinition,
  apis: [
    './src/routes/*.ts',
    './src/controllers/*.ts',
    './src/models/*.ts',
  ],
};

const swaggerSpec = swaggerJSDoc(options);

/**
 * Swagger UI配置选项
 */
const swaggerUiOptions: swaggerUi.SwaggerUiOptions = {
  explorer: true,
  customCss: `
    .swagger-ui .topbar { display: none; }
    .swagger-ui .info .title { color: #3b82f6; }
    .swagger-ui .scheme-container { background: #f8fafc; }
  `,
  customSiteTitle: '江湖风放置游戏 API 文档',
  customfavIcon: '/favicon.ico',
  swaggerOptions: {
    persistAuthorization: true,
    displayRequestDuration: true,
    filter: true,
    showExtensions: true,
    showCommonExtensions: true,
    docExpansion: 'list',
    defaultModelsExpandDepth: 2,
    defaultModelExpandDepth: 2,
  },
};

/**
 * 设置Swagger文档路由
 */
export function setupSwagger(app: express.Application): void {
  try {
    // 验证Swagger规范
    if (!validateSwaggerSpec()) {
      Logger.warn('Swagger规范验证失败，但仍将继续设置文档');
    }

    // Swagger UI
    app.use('/api/docs', swaggerUi.serve);
    app.get('/api/docs', swaggerUi.setup(swaggerSpec, swaggerUiOptions));

    // JSON格式的API文档
    app.get('/api/docs.json', (req, res) => {
      res.setHeader('Content-Type', 'application/json');
      res.send(swaggerSpec);
    });

    // YAML格式的API文档
    app.get('/api/docs.yaml', (req, res) => {
      res.setHeader('Content-Type', 'text/yaml');
      res.send(convertToYaml(swaggerSpec));
    });

    // 重定向根路径的docs到API docs
    app.get('/docs', (req, res) => {
      res.redirect('/api/docs');
    });

    // API文档状态检查
    app.get('/api/docs/status', (req, res) => {
      res.json({
        success: true,
        message: 'API文档服务正常',
        spec: {
          openapi: (swaggerSpec as any).openapi,
          title: (swaggerSpec as any).info?.title,
          version: (swaggerSpec as any).info?.version,
          pathCount: Object.keys((swaggerSpec as any).paths || {}).length,
          schemaCount: Object.keys((swaggerSpec as any).components?.schemas || {}).length,
        },
        urls: {
          ui: '/api/docs',
          json: '/api/docs.json',
          yaml: '/api/docs.yaml',
        },
        timestamp: new Date().toISOString(),
      });
    });

    Logger.info('Swagger文档已配置', {
      docsUrl: '/api/docs',
      jsonUrl: '/api/docs.json',
      yamlUrl: '/api/docs.yaml',
      statusUrl: '/api/docs/status',
      environment: process.env['NODE_ENV'],
    });
  } catch (error) {
    Logger.error('Swagger配置失败', error);
  }
}

/**
 * 验证Swagger规范
 */
function validateSwaggerSpec(): boolean {
  try {
    // 基本验证
    if (!(swaggerSpec as any).openapi) {
      throw new Error('缺少OpenAPI版本');
    }

    if (!(swaggerSpec as any).info) {
      throw new Error('缺少API信息');
    }

    if (!(swaggerSpec as any).paths) {
      Logger.warn('Swagger规范中没有找到路径定义');
    }

    Logger.debug('Swagger规范验证通过');
    return true;
  } catch (error) {
    Logger.error('Swagger规范验证失败', error);
    return false;
  }
}

/**
 * 转换为YAML格式（简单实现）
 */
function convertToYaml(obj: any): string {
  // 这里可以使用js-yaml库，但为了简化依赖，使用简单的JSON转换
  return JSON.stringify(obj, null, 2);
}

export { swaggerSpec };
