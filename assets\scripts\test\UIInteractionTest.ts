/**
 * UI交互测试组件
 * 专门用于测试UI交互功能，可以独立挂载到任何节点
 */

import { _decorator, Component, input, Input, EventKeyboard, KeyCode } from 'cc';
import { UIManager } from '../managers/UIManager';
import { UIPanelType } from '../ui/types/UITypes';
import { EventManager } from '../managers/EventManager';

const { ccclass, property } = _decorator;

@ccclass('UIInteractionTest')
export class UIInteractionTest extends Component {
    
    @property({ tooltip: '是否启用调试模式' })
    public debugMode: boolean = true;
    
    @property({ tooltip: '是否显示详细日志' })
    public verboseLogging: boolean = false;
    
    // 私有属性
    private _uiManager: UIManager | null = null;
    private _testStartTime: number = 0;

    protected onLoad(): void {
        if (this.debugMode) {
            console.log('🧪 UI交互测试组件加载');
            this._testStartTime = Date.now();
            
            // 初始化UI管理器引用
            this.initializeUIManager();
            
            // 绑定键盘事件（仅调试模式）
            this.bindDebugKeys();
            
            // 设置事件监听
            this.setupEventListeners();
            
            // 显示测试说明
            this.showTestInstructions();
        }
    }

    protected onDestroy(): void {
        if (this.debugMode) {
            this.unbindDebugKeys();
            this.removeEventListeners();
            console.log('🧪 UI交互测试组件销毁');
        }
    }

    /**
     * 初始化UI管理器
     */
    private initializeUIManager(): void {
        try {
            this._uiManager = UIManager.getInstance();
            if (this._uiManager) {
                console.log('✅ UI管理器连接成功');
            } else {
                console.warn('⚠️ UI管理器未初始化');
            }
        } catch (error) {
            console.error('❌ UI管理器连接失败:', error);
        }
    }

    /**
     * 绑定调试按键
     */
    private bindDebugKeys(): void {
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        if (this.verboseLogging) {
            console.log('⌨️ 调试按键已绑定');
        }
    }

    /**
     * 解绑调试按键
     */
    private unbindDebugKeys(): void {
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        if (this.verboseLogging) {
            console.log('⌨️ 调试按键已解绑');
        }
    }

    /**
     * 设置事件监听
     */
    private setupEventListeners(): void {
        const eventManager = EventManager.getInstance();
        
        // 监听UI事件
        eventManager.on('ui_panel_show', this.onPanelShow, this);
        eventManager.on('ui_panel_hide', this.onPanelHide, this);
        eventManager.on('mobile_touch_gesture', this.onTouchGesture, this);
        
        if (this.verboseLogging) {
            console.log('📡 事件监听已设置');
        }
    }

    /**
     * 移除事件监听
     */
    private removeEventListeners(): void {
        const eventManager = EventManager.getInstance();
        
        eventManager.off('ui_panel_show', this.onPanelShow, this);
        eventManager.off('ui_panel_hide', this.onPanelHide, this);
        eventManager.off('mobile_touch_gesture', this.onTouchGesture, this);
        
        if (this.verboseLogging) {
            console.log('📡 事件监听已移除');
        }
    }

    /**
     * 键盘事件处理
     */
    private onKeyDown(event: EventKeyboard): void {
        if (!this.debugMode) return;
        
        switch (event.keyCode) {
            case KeyCode.KEY_I:
                this.testInventoryPanel();
                break;
            case KeyCode.KEY_K:
                this.testSkillPanel();
                break;
            case KeyCode.KEY_M:
                this.testMainMenu();
                break;
            case KeyCode.ESCAPE:
                this.testCloseAllPanels();
                break;
            case KeyCode.KEY_H:
                this.showTestInstructions();
                break;
            case KeyCode.KEY_S:
                this.showUIStatus();
                break;
            case KeyCode.KEY_T:
                this.runQuickTest();
                break;
            case KeyCode.KEY_V:
                this.toggleVerboseLogging();
                break;
            case KeyCode.KEY_C:
                console.clear();
                this.showTestInstructions();
                break;
        }
    }

    /**
     * 面板显示事件
     */
    private onPanelShow(eventData: any): void {
        if (this.verboseLogging) {
            console.log('📋 面板显示:', eventData);
        }
    }

    /**
     * 面板隐藏事件
     */
    private onPanelHide(eventData: any): void {
        if (this.verboseLogging) {
            console.log('📋 面板隐藏:', eventData);
        }
    }

    /**
     * 触摸手势事件
     */
    private onTouchGesture(eventData: any): void {
        if (this.verboseLogging) {
            console.log('👆 触摸手势:', eventData.type, 'at', eventData.position);
        }
    }

    /**
     * 显示测试说明
     */
    private showTestInstructions(): void {
        console.log('\n🧪 ========== UI交互测试 ==========');
        console.log('🎯 专门用于测试UI交互功能');
        console.log('');
        console.log('⌨️ 调试快捷键:');
        console.log('   I键 - 测试背包面板');
        console.log('   K键 - 测试技能面板');
        console.log('   M键 - 测试主菜单');
        console.log('   ESC键 - 关闭所有面板');
        console.log('   H键 - 显示此帮助');
        console.log('   S键 - 显示UI状态');
        console.log('   T键 - 运行快速测试');
        console.log('   V键 - 切换详细日志');
        console.log('   C键 - 清空控制台');
        console.log('');
        console.log('📱 触摸操作:');
        console.log('   在屏幕上进行各种触摸操作来测试手势识别');
        console.log('🧪 ===================================');
    }

    /**
     * 测试背包面板
     */
    private testInventoryPanel(): void {
        console.log('📦 测试背包面板...');

        if (this._uiManager) {
            try {
                this._uiManager.togglePanel(UIPanelType.Inventory);
                console.log('✅ 背包面板配置测试成功');
            } catch (error) {
                if (error.message.includes('未找到面板配置')) {
                    console.warn('⚠️ 背包面板配置未注册，这是正常的（预制体尚未创建）');
                } else {
                    console.error('❌ 背包面板测试失败:', error);
                }
            }
        } else {
            console.warn('⚠️ UI管理器未可用');
        }
    }

    /**
     * 测试技能面板
     */
    private testSkillPanel(): void {
        console.log('⚔️ 测试技能面板...');
        
        if (this._uiManager) {
            try {
                this._uiManager.togglePanel(UIPanelType.Skills);
                console.log('✅ 技能面板切换成功');
            } catch (error) {
                console.error('❌ 技能面板测试失败:', error);
            }
        } else {
            console.warn('⚠️ UI管理器未可用');
        }
    }

    /**
     * 测试主菜单
     */
    private testMainMenu(): void {
        console.log('🏠 测试主菜单...');

        if (this._uiManager) {
            try {
                this._uiManager.togglePanel(UIPanelType.MainMenu);
                console.log('✅ 主菜单配置测试成功');
            } catch (error) {
                if (error.message.includes('未找到面板配置')) {
                    console.warn('⚠️ 主菜单配置未注册，这是正常的（预制体尚未创建）');
                } else {
                    console.error('❌ 主菜单测试失败:', error);
                }
            }
        } else {
            console.warn('⚠️ UI管理器未可用');
        }
    }

    /**
     * 测试关闭所有面板
     */
    private testCloseAllPanels(): void {
        console.log('❌ 关闭所有面板...');
        
        if (this._uiManager) {
            try {
                this._uiManager.hideAllPanels();
                console.log('✅ 所有面板已关闭');
            } catch (error) {
                console.error('❌ 关闭面板失败:', error);
            }
        } else {
            console.warn('⚠️ UI管理器未可用');
        }
    }

    /**
     * 显示UI状态
     */
    private showUIStatus(): void {
        console.log('\n📊 ========== UI系统状态 ==========');
        
        if (this._uiManager) {
            const stats = this._uiManager.getUIStats();
            console.log('🎨 UI管理器状态:');
            console.log(`   总面板数: ${stats.totalPanels}`);
            console.log(`   活跃面板: ${stats.activePanels}`);
            console.log(`   可见面板: ${stats.visiblePanels}`);
            console.log(`   缓存预制体: ${stats.cachedPrefabs}`);
            console.log(`   模态栈: [${stats.modalStack.join(', ')}]`);
            console.log(`   UI层级: [${stats.layers.join(', ')}]`);
        } else {
            console.log('❌ UI管理器未可用');
        }
        
        // 显示运行时间
        const runTime = Date.now() - this._testStartTime;
        console.log(`⏱️ 测试运行时间: ${runTime}ms`);
        
        console.log('📊 ==============================');
    }

    /**
     * 运行快速测试
     */
    private runQuickTest(): void {
        console.log('\n⚡ ========== 快速测试 ==========');
        
        let testsPassed = 0;
        let totalTests = 0;
        
        // 测试1: UI管理器可用性
        totalTests++;
        if (this._uiManager) {
            console.log('✅ UI管理器: 可用');
            testsPassed++;
        } else {
            console.log('❌ UI管理器: 不可用');
        }
        
        // 测试2: 事件管理器可用性
        totalTests++;
        try {
            const eventManager = EventManager.getInstance();
            if (eventManager) {
                console.log('✅ 事件管理器: 可用');
                testsPassed++;
            } else {
                console.log('❌ 事件管理器: 不可用');
            }
        } catch (error) {
            console.log('❌ 事件管理器: 异常');
        }
        
        // 测试3: 面板类型定义
        totalTests++;
        if (UIPanelType && Object.keys(UIPanelType).length > 0) {
            console.log('✅ 面板类型: 已定义');
            testsPassed++;
        } else {
            console.log('❌ 面板类型: 未定义');
        }
        
        // 测试4: 组件状态
        totalTests++;
        if (this.node && this.node.isValid) {
            console.log('✅ 测试组件: 正常');
            testsPassed++;
        } else {
            console.log('❌ 测试组件: 异常');
        }
        
        // 显示测试结果
        const passRate = (testsPassed / totalTests * 100).toFixed(1);
        console.log(`📈 测试结果: ${testsPassed}/${totalTests} 通过 (${passRate}%)`);
        
        if (testsPassed === totalTests) {
            console.log('🎉 所有测试通过！UI系统运行正常');
        } else {
            console.log('⚠️ 部分测试失败，请检查系统状态');
        }
        
        console.log('⚡ ============================');
    }

    /**
     * 切换详细日志
     */
    private toggleVerboseLogging(): void {
        this.verboseLogging = !this.verboseLogging;
        console.log(`📝 详细日志: ${this.verboseLogging ? '启用' : '禁用'}`);
    }

    // ==================== 公共API ====================

    /**
     * 启用/禁用调试模式
     */
    public setDebugMode(enabled: boolean): void {
        const wasEnabled = this.debugMode;
        this.debugMode = enabled;
        
        if (enabled && !wasEnabled) {
            this.bindDebugKeys();
            this.setupEventListeners();
            console.log('🐛 UI交互测试: 调试模式已启用');
        } else if (!enabled && wasEnabled) {
            this.unbindDebugKeys();
            this.removeEventListeners();
            console.log('📱 UI交互测试: 调试模式已禁用');
        }
    }

    /**
     * 手动触发面板测试
     */
    public testPanel(panelType: UIPanelType): void {
        if (this._uiManager) {
            console.log(`🧪 手动测试面板: ${panelType}`);
            this._uiManager.togglePanel(panelType);
        }
    }

    /**
     * 获取测试统计
     */
    public getTestStats(): any {
        return {
            debugMode: this.debugMode,
            verboseLogging: this.verboseLogging,
            runTime: Date.now() - this._testStartTime,
            uiManagerAvailable: this._uiManager !== null,
            componentValid: this.node && this.node.isValid
        };
    }
}
