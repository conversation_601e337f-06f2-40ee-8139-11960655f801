import express = require('express');
import jwt = require('jsonwebtoken');
import { Logger } from '../utils/logger';

/**
 * 认证中间件
 */

export interface AuthenticatedRequest extends express.Request {
  user?: {
    userId: string;
    username: string;
    email: string;
    role: string;
  };
}

/**
 * JWT认证中间件
 */
export function authenticateToken(req: AuthenticatedRequest, res: express.Response, next: express.NextFunction): void {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    Logger.warn('访问受保护路由但未提供令牌', {
      url: req.originalUrl,
      method: req.method,
      ip: req.ip,
    });

    res.status(401).json({
      success: false,
      message: '访问令牌是必需的',
      error: 'MISSING_TOKEN',
      timestamp: new Date().toISOString(),
    });
    return;
  }

  const jwtSecret = process.env['JWT_SECRET'];
  if (!jwtSecret) {
    Logger.error('JWT_SECRET环境变量未设置');
    res.status(500).json({
      success: false,
      message: '服务器配置错误',
      error: 'SERVER_CONFIG_ERROR',
      timestamp: new Date().toISOString(),
    });
    return;
  }

  jwt.verify(token, jwtSecret, (err: any, decoded: any) => {
    if (err) {
      Logger.warn('无效的访问令牌', {
        url: req.originalUrl,
        method: req.method,
        ip: req.ip,
        error: err.message,
      });

      res.status(403).json({
        success: false,
        message: '无效的访问令牌',
        error: 'INVALID_TOKEN',
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // 将用户信息添加到请求对象
    req.user = {
      userId: decoded.userId,
      username: decoded.username,
      email: decoded.email,
      role: decoded.role || 'user',
    };

    Logger.debug('用户认证成功', {
      userId: req.user.userId,
      username: req.user.username,
      url: req.originalUrl,
      method: req.method,
    });

    next();
  });
}

/**
 * 可选认证中间件（不强制要求令牌）
 */
export function optionalAuth(req: AuthenticatedRequest, _res: express.Response, next: express.NextFunction): void {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return next(); // 没有令牌，继续执行
  }

  const jwtSecret = process.env['JWT_SECRET'];
  if (!jwtSecret) {
    return next(); // 配置错误，继续执行
  }

  jwt.verify(token, jwtSecret, (err: any, decoded: any) => {
    if (!err && decoded) {
      req.user = {
        userId: decoded.userId,
        username: decoded.username,
        email: decoded.email,
        role: decoded.role || 'user',
      };
    }
    next();
  });
}

/**
 * 角色权限检查中间件
 */
export function requireRole(roles: string | string[]) {
  const allowedRoles = Array.isArray(roles) ? roles : [roles];

  return (req: AuthenticatedRequest, res: express.Response, next: express.NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        message: '需要认证',
        error: 'AUTHENTICATION_REQUIRED',
        timestamp: new Date().toISOString(),
      });
      return;
    }

    if (!allowedRoles.includes(req.user.role)) {
      Logger.warn('用户权限不足', {
        userId: req.user.userId,
        userRole: req.user.role,
        requiredRoles: allowedRoles,
        url: req.originalUrl,
        method: req.method,
      });

      res.status(403).json({
        success: false,
        message: '权限不足',
        error: 'INSUFFICIENT_PERMISSIONS',
        timestamp: new Date().toISOString(),
      });
      return;
    }

    next();
  };
}

/**
 * 管理员权限检查
 */
export const requireAdmin = requireRole(['admin', 'super_admin']);

/**
 * 版主权限检查
 */
export const requireModerator = requireRole(['moderator', 'admin', 'super_admin']);

/**
 * 速率限制中间件
 */
export function rateLimit(options: {
  windowMs: number;
  maxRequests: number;
  message?: string;
}) {
  const requests = new Map<string, { count: number; resetTime: number }>();

  return (req: express.Request, res: express.Response, next: express.NextFunction) => {
    const key = req.ip || 'unknown';
    const now = Date.now();
    const windowMs = options.windowMs;
    const maxRequests = options.maxRequests;

    // 清理过期的记录
    const resetTime = now + windowMs;
    const current = requests.get(key);

    if (!current || now > current.resetTime) {
      requests.set(key, { count: 1, resetTime });
      return next();
    }

    if (current.count >= maxRequests) {
      Logger.warn('速率限制触发', {
        ip: key,
        url: req.originalUrl,
        method: req.method,
        count: current.count,
        maxRequests,
      });

      return res.status(429).json({
        success: false,
        message: options.message || '请求过于频繁，请稍后再试',
        error: 'RATE_LIMIT_EXCEEDED',
        retryAfter: Math.ceil((current.resetTime - now) / 1000),
        timestamp: new Date().toISOString(),
      });
    }

    current.count++;
    next();
  };
}

/**
 * 游戏状态检查中间件
 */
export function requireGameAccess(req: AuthenticatedRequest, res: express.Response, next: express.NextFunction): void {
  // TODO: 检查游戏维护状态、用户封禁状态等
  
  const maintenanceMode = process.env['MAINTENANCE_MODE'] === 'true';
  
  if (maintenanceMode && req.user?.role !== 'admin') {
    res.status(503).json({
      success: false,
      message: '游戏正在维护中，请稍后再试',
      error: 'MAINTENANCE_MODE',
      timestamp: new Date().toISOString(),
    });
    return;
  }

  // TODO: 检查用户是否被封禁
  // TODO: 检查用户游戏状态

  next();
}

/**
 * 创建JWT令牌
 */
export function createToken(payload: any, expiresIn: string = '7d'): string {
  const jwtSecret = process.env['JWT_SECRET'];
  if (!jwtSecret) {
    throw new Error('JWT_SECRET环境变量未设置');
  }

  return jwt.sign(payload, jwtSecret, { expiresIn } as jwt.SignOptions);
}

/**
 * 验证JWT令牌
 */
export function verifyToken(token: string): any {
  const jwtSecret = process.env['JWT_SECRET'];
  if (!jwtSecret) {
    throw new Error('JWT_SECRET环境变量未设置');
  }

  return jwt.verify(token, jwtSecret);
}

// 导出auth别名以保持向后兼容
export const auth = {
  required: authenticateToken
};
