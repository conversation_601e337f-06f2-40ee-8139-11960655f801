# Copyright (c) 2025 "放置". All rights reserved.

# 配置表管理工具

## 功能概述
配置表管理工具是一个用于管理游戏配置表的命令行工具，支持版本控制、数据同步、校验等功能。

## 主要功能
1. **版本管理**：配置表版本控制和更新
2. **数据校验**：配置数据完整性和格式校验
3. **同步机制**：前后端配置同步
4. **热更新**：支持配置热更新
5. **备份恢复**：配置数据备份和恢复

## 目录结构
```
tools/config-manager/
├── src/                # 源代码
│   ├── commands/       # 命令行命令
│   ├── validators/     # 数据校验器
│   ├── sync/           # 同步机制
│   ├── utils/          # 工具函数
│   └── types/          # 类型定义
├── templates/          # 配置模板
├── scripts/            # 脚本文件
└── config/             # 工具配置
```

## 使用方法

### 安装依赖
```bash
npm install
```

### 基本命令
```bash
# 校验所有配置
npm run validate

# 生成版本清单
npm run generate-manifest

# 同步配置到服务器
npm run sync

# 创建配置备份
npm run backup

# 从备份恢复配置
npm run restore
```

### 高级功能
```bash
# 校验特定配置文件
node dist/cli.js validate --file assets/configs/ui/layouts/main_scene_layout.json

# 生成配置差异报告
node dist/cli.js diff --from v1.0.0 --to v1.1.0

# 热更新配置
node dist/cli.js hot-update --config character_stats

# 批量更新版本号
node dist/cli.js bump-version --type patch
```

## 配置文件规范
- 所有配置文件必须包含版本号
- 配置文件必须通过JSON Schema校验
- 敏感配置不得包含在前端配置中
- 配置更新必须更新对应的校验和

## 开发指南
1. 添加新的配置类型需要更新校验器
2. 修改配置结构需要考虑向后兼容性
3. 重要配置变更需要编写迁移脚本
4. 所有配置操作都应该有日志记录
