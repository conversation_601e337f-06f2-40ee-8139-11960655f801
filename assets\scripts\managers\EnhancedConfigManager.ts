/**
 * Copyright (c) 2025 "放置". All rights reserved.
 * 增强配置管理器 - 支持shared类型定义和分包策略
 */

import { _decorator, resources, AssetManager } from 'cc';
import { BaseManager } from './BaseManager';
import { ConfigSecurityLevel, ConfigType, ClientDisplayConfig } from '../../../shared/configs/types/config-types';
import { PlayerInfo, GameLocation, ApiResponse } from '../../../shared/configs/types/game-types';
import { LoginRequest, ApiEndpoint } from '../../../shared/configs/types/api-types';

const { ccclass } = _decorator;

/**
 * Bundle加载状态
 */
interface BundleLoadState {
  name: string;
  loaded: boolean;
  loading: boolean;
  priority: number;
  size?: number;
  loadTime?: number;
}

/**
 * 配置加载选项
 */
interface ConfigLoadOptions {
  bundle?: string;           // 指定bundle
  forceReload?: boolean;     // 强制重新加载
  timeout?: number;          // 超时时间
  fallback?: boolean;        // 是否使用降级方案
}

/**
 * 配置缓存项
 */
interface ConfigCacheItem<T> {
  data: T;
  timestamp: number;
  version: string;
  securityLevel: ConfigSecurityLevel;
  expiry: number;
}

@ccclass('EnhancedConfigManager')
export class EnhancedConfigManager extends BaseManager {
  private static _instance: EnhancedConfigManager;
  
  // Bundle管理
  private _bundleStates: Map<string, BundleLoadState> = new Map();
  private _loadedBundles: Map<string, AssetManager.Bundle> = new Map();
  
  // 配置缓存
  private _configCache: Map<string, ConfigCacheItem<any>> = new Map();
  private _loadingPromises: Map<string, Promise<any>> = new Map();
  
  // 分包策略配置
  private readonly BUNDLE_PRIORITY = {
    'core': 1,
    'ui-basic': 2,
    'features': 2,
    'locations': 1,
    'ui-features': 2,
    'ui-advanced': 3,
    'audio': 4,
    'configs': 5,
    'remote-resources': 3
  };
  
  // 缓存配置
  private readonly CACHE_DURATION = {
    [ConfigType.STATIC]: 60 * 60 * 1000,      // 1小时
    [ConfigType.DYNAMIC]: 30 * 60 * 1000,     // 30分钟
    [ConfigType.USER]: 10 * 60 * 1000,        // 10分钟
    [ConfigType.REALTIME]: 5 * 60 * 1000      // 5分钟
  };

  /**
   * 获取单例实例
   */
  public static getInstance(): EnhancedConfigManager {
    if (!EnhancedConfigManager._instance) {
      EnhancedConfigManager._instance = new EnhancedConfigManager();
    }
    return EnhancedConfigManager._instance;
  }

  /**
   * 初始化管理器
   */
  protected async initializeManager(): Promise<void> {
    console.log('🔧 EnhancedConfigManager: 开始初始化');
    
    try {
      // 初始化Bundle状态
      this.initializeBundleStates();
      
      // 预加载核心Bundle
      await this.preloadCoreBundles();
      
      // 加载基础配置
      await this.loadBasicConfigs();
      
      console.log('✅ EnhancedConfigManager: 初始化完成');
      
    } catch (error) {
      console.error('❌ EnhancedConfigManager: 初始化失败', error);
      throw error;
    }
  }

  /**
   * 销毁管理器
   */
  public destroyManager(): void {
    this._configCache.clear();
    this._loadingPromises.clear();
    this._bundleStates.clear();
    this._loadedBundles.clear();
    console.log('🗑️ EnhancedConfigManager: 已销毁');
  }

  // ==================== Bundle管理 ====================

  /**
   * 初始化Bundle状态
   */
  private initializeBundleStates(): void {
    Object.entries(this.BUNDLE_PRIORITY).forEach(([name, priority]) => {
      this._bundleStates.set(name, {
        name,
        loaded: false,
        loading: false,
        priority
      });
    });
  }

  /**
   * 预加载核心Bundle
   */
  private async preloadCoreBundles(): Promise<void> {
    const coreBundles = ['core', 'ui-basic'];
    const loadPromises = coreBundles.map(bundleName => this.loadBundle(bundleName));
    
    try {
      await Promise.all(loadPromises);
      console.log('✅ 核心Bundle预加载完成');
    } catch (error) {
      console.error('❌ 核心Bundle预加载失败', error);
      throw error;
    }
  }

  /**
   * 加载Bundle
   */
  public async loadBundle(bundleName: string): Promise<AssetManager.Bundle> {
    const state = this._bundleStates.get(bundleName);
    if (!state) {
      throw new Error(`未知的Bundle: ${bundleName}`);
    }

    // 如果已加载，直接返回
    if (state.loaded && this._loadedBundles.has(bundleName)) {
      return this._loadedBundles.get(bundleName)!;
    }

    // 如果正在加载，等待完成
    if (state.loading) {
      return new Promise((resolve, reject) => {
        const checkLoaded = () => {
          if (state.loaded && this._loadedBundles.has(bundleName)) {
            resolve(this._loadedBundles.get(bundleName)!);
          } else if (!state.loading) {
            reject(new Error(`Bundle加载失败: ${bundleName}`));
          } else {
            setTimeout(checkLoaded, 100);
          }
        };
        checkLoaded();
      });
    }

    // 开始加载
    state.loading = true;
    const startTime = Date.now();

    try {
      console.log(`📦 开始加载Bundle: ${bundleName}`);
      
      const bundle = await new Promise<AssetManager.Bundle>((resolve, reject) => {
        AssetManager.loadBundle(bundleName, (error, bundle) => {
          if (error) {
            reject(error);
          } else {
            resolve(bundle);
          }
        });
      });

      // 更新状态
      state.loaded = true;
      state.loading = false;
      state.loadTime = Date.now() - startTime;
      
      this._loadedBundles.set(bundleName, bundle);
      
      console.log(`✅ Bundle加载完成: ${bundleName} (${state.loadTime}ms)`);
      return bundle;
      
    } catch (error) {
      state.loading = false;
      console.error(`❌ Bundle加载失败: ${bundleName}`, error);
      throw error;
    }
  }

  /**
   * 按需加载Bundle
   */
  public async loadBundleOnDemand(bundleName: string): Promise<AssetManager.Bundle> {
    console.log(`📦 按需加载Bundle: ${bundleName}`);
    return this.loadBundle(bundleName);
  }

  /**
   * 获取Bundle加载状态
   */
  public getBundleStates(): Map<string, BundleLoadState> {
    return new Map(this._bundleStates);
  }

  // ==================== 配置加载 ====================

  /**
   * 加载基础配置
   */
  private async loadBasicConfigs(): Promise<void> {
    try {
      // 从configs bundle加载远程配置
      await this.loadConfigFromBundle<ClientDisplayConfig[]>('configs', 'display/locations', {
        securityLevel: ConfigSecurityLevel.PUBLIC,
        type: ConfigType.STATIC
      });
      
      console.log('✅ 基础配置加载完成');
    } catch (error) {
      console.warn('⚠️ 基础配置加载失败，使用降级方案', error);
      await this.loadFallbackConfigs();
    }
  }

  /**
   * 从Bundle加载配置
   */
  public async loadConfigFromBundle<T>(
    bundleName: string,
    configPath: string,
    options: {
      securityLevel: ConfigSecurityLevel;
      type: ConfigType;
      forceReload?: boolean;
    }
  ): Promise<T> {
    const cacheKey = `${bundleName}:${configPath}`;
    
    // 检查缓存
    if (!options.forceReload) {
      const cached = this.getFromCache<T>(cacheKey);
      if (cached) {
        return cached;
      }
    }

    // 检查是否正在加载
    if (this._loadingPromises.has(cacheKey)) {
      return this._loadingPromises.get(cacheKey);
    }

    // 开始加载
    const loadPromise = this.doLoadConfigFromBundle<T>(bundleName, configPath, options);
    this._loadingPromises.set(cacheKey, loadPromise);

    try {
      const config = await loadPromise;
      
      // 缓存配置
      this.setToCache(cacheKey, config, options.securityLevel, options.type);
      
      return config;
    } finally {
      this._loadingPromises.delete(cacheKey);
    }
  }

  /**
   * 实际执行Bundle配置加载
   */
  private async doLoadConfigFromBundle<T>(
    bundleName: string,
    configPath: string,
    options: {
      securityLevel: ConfigSecurityLevel;
      type: ConfigType;
    }
  ): Promise<T> {
    // 确保Bundle已加载
    const bundle = await this.loadBundle(bundleName);
    
    return new Promise((resolve, reject) => {
      bundle.load(configPath, (error, asset) => {
        if (error) {
          reject(new Error(`配置加载失败: ${bundleName}/${configPath} - ${error.message}`));
          return;
        }

        try {
          let config: T;
          
          if (typeof asset === 'string') {
            config = JSON.parse(asset);
          } else if (asset.json) {
            config = asset.json;
          } else {
            config = asset;
          }
          
          resolve(config);
        } catch (parseError) {
          reject(new Error(`配置解析失败: ${bundleName}/${configPath} - ${parseError.message}`));
        }
      });
    });
  }

  // ==================== 缓存管理 ====================

  /**
   * 从缓存获取配置
   */
  private getFromCache<T>(key: string): T | null {
    const cached = this._configCache.get(key);
    if (!cached) {
      return null;
    }

    // 检查是否过期
    if (Date.now() > cached.expiry) {
      this._configCache.delete(key);
      return null;
    }

    return cached.data as T;
  }

  /**
   * 设置缓存
   */
  private setToCache<T>(
    key: string,
    data: T,
    securityLevel: ConfigSecurityLevel,
    type: ConfigType,
    version: string = '1.0.0'
  ): void {
    const duration = this.CACHE_DURATION[type] || this.CACHE_DURATION[ConfigType.STATIC];
    
    this._configCache.set(key, {
      data,
      timestamp: Date.now(),
      version,
      securityLevel,
      expiry: Date.now() + duration
    });
  }

  /**
   * 清除缓存
   */
  public clearCache(pattern?: string): void {
    if (pattern) {
      const keysToDelete = Array.from(this._configCache.keys()).filter(key => key.includes(pattern));
      keysToDelete.forEach(key => this._configCache.delete(key));
      console.log(`🗑️ 已清除匹配 "${pattern}" 的缓存 (${keysToDelete.length} 项)`);
    } else {
      this._configCache.clear();
      console.log('🗑️ 已清除所有配置缓存');
    }
  }

  // ==================== 降级方案 ====================

  /**
   * 加载降级配置
   */
  private async loadFallbackConfigs(): Promise<void> {
    try {
      // 从本地resources加载基础配置
      const fallbackConfig = await this.loadFromResources<any>('configs/fallback');
      console.log('✅ 降级配置加载完成');
    } catch (error) {
      console.error('❌ 降级配置加载失败', error);
      // 使用硬编码的最小配置
      this.useMinimalConfig();
    }
  }

  /**
   * 从resources加载配置
   */
  private async loadFromResources<T>(path: string): Promise<T> {
    return new Promise((resolve, reject) => {
      resources.load(path, (error, asset) => {
        if (error) {
          reject(error);
          return;
        }

        try {
          let config: T;
          if (typeof asset === 'string') {
            config = JSON.parse(asset);
          } else if (asset.json) {
            config = asset.json;
          } else {
            config = asset;
          }
          resolve(config);
        } catch (parseError) {
          reject(parseError);
        }
      });
    });
  }

  /**
   * 使用最小配置
   */
  private useMinimalConfig(): void {
    console.log('⚠️ 使用最小配置');
    // 设置最基本的配置数据
    const minimalConfig = {
      version: '1.0.0',
      locations: [],
      features: {}
    };
    
    this.setToCache('minimal:config', minimalConfig, ConfigSecurityLevel.PUBLIC, ConfigType.STATIC);
  }

  // ==================== 工具方法 ====================

  /**
   * 获取管理器状态
   */
  public getManagerStatus(): {
    bundleStates: Map<string, BundleLoadState>;
    cacheSize: number;
    loadingCount: number;
  } {
    return {
      bundleStates: this.getBundleStates(),
      cacheSize: this._configCache.size,
      loadingCount: this._loadingPromises.size
    };
  }

  /**
   * 预热缓存
   */
  public async warmupCache(): Promise<void> {
    console.log('🔥 开始预热缓存');
    
    const warmupTasks = [
      this.loadBundleOnDemand('features'),
      this.loadBundleOnDemand('locations')
    ];
    
    try {
      await Promise.allSettled(warmupTasks);
      console.log('✅ 缓存预热完成');
    } catch (error) {
      console.warn('⚠️ 缓存预热部分失败', error);
    }
  }
}
