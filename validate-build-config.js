/**
 * Copyright (c) 2025 "放置". All rights reserved.
 * 构建配置验证脚本
 */

const fs = require('fs');
const path = require('path');

class BuildConfigValidator {
  constructor() {
    this.results = [];
    this.errors = [];
  }

  /**
   * 运行所有验证
   */
  async runValidation() {
    console.log('🔧 开始构建配置验证...\n');
    
    this.validateTsConfig();
    this.validateBundleConfigs();
    this.validateSharedTypes();
    this.validateAssetStructure();
    this.validateBuildConfig();
    
    this.printResults();
  }

  /**
   * 验证TypeScript配置
   */
  validateTsConfig() {
    console.log('📋 验证TypeScript配置...');
    
    try {
      const tsconfigPath = path.join(process.cwd(), 'tsconfig.json');
      const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'));
      
      // 检查include配置
      const includes = tsconfig.include || [];
      const hasShared = includes.some(pattern => pattern.includes('shared'));
      
      if (hasShared) {
        this.addResult('✅ tsconfig.json包含shared文件夹');
      } else {
        this.addError('❌ tsconfig.json未包含shared文件夹');
      }
      
      // 检查路径映射
      const paths = tsconfig.compilerOptions?.paths || {};
      const hasSharedPaths = Object.keys(paths).some(key => key.includes('@shared'));
      
      if (hasSharedPaths) {
        this.addResult('✅ tsconfig.json包含shared路径映射');
      } else {
        this.addError('❌ tsconfig.json缺少shared路径映射');
      }
      
    } catch (error) {
      this.addError(`❌ 读取tsconfig.json失败: ${error.message}`);
    }
  }

  /**
   * 验证Bundle配置
   */
  validateBundleConfigs() {
    console.log('📋 验证Bundle配置...');
    
    const bundleDir = path.join(process.cwd(), 'assets', 'bundles');
    
    if (!fs.existsSync(bundleDir)) {
      this.addError('❌ assets/bundles目录不存在');
      return;
    }
    
    const expectedBundles = [
      'core', 'ui-basic', 'ui-advanced', 'features', 
      'locations', 'ui-features', 'audio', 'configs', 'remote-resources'
    ];
    
    const existingBundles = [];
    
    expectedBundles.forEach(bundleName => {
      const metaPath = path.join(bundleDir, `${bundleName}.meta`);
      
      if (fs.existsSync(metaPath)) {
        try {
          const meta = JSON.parse(fs.readFileSync(metaPath, 'utf8'));
          const bundleConfig = meta.userData?.bundle;
          
          if (bundleConfig) {
            existingBundles.push(bundleName);
            this.addResult(`✅ Bundle配置正常: ${bundleName} (优先级: ${bundleConfig.priority})`);
          } else {
            this.addError(`❌ Bundle配置缺失: ${bundleName}`);
          }
        } catch (error) {
          this.addError(`❌ Bundle配置解析失败: ${bundleName} - ${error.message}`);
        }
      } else {
        this.addError(`❌ Bundle元数据文件不存在: ${bundleName}.meta`);
      }
    });
    
    this.addResult(`📊 Bundle配置统计: ${existingBundles.length}/${expectedBundles.length}个`);
  }

  /**
   * 验证Shared类型文件
   */
  validateSharedTypes() {
    console.log('📋 验证Shared类型文件...');
    
    const sharedTypesDir = path.join(process.cwd(), 'shared', 'configs', 'types');
    
    if (!fs.existsSync(sharedTypesDir)) {
      this.addError('❌ shared/configs/types目录不存在');
      return;
    }
    
    const expectedTypes = ['config-types.ts', 'game-types.ts', 'api-types.ts'];
    
    expectedTypes.forEach(fileName => {
      const filePath = path.join(sharedTypesDir, fileName);
      
      if (fs.existsSync(filePath)) {
        try {
          const content = fs.readFileSync(filePath, 'utf8');
          
          // 检查文件内容
          if (content.includes('export')) {
            this.addResult(`✅ 类型文件正常: ${fileName}`);
          } else {
            this.addError(`❌ 类型文件缺少导出: ${fileName}`);
          }
        } catch (error) {
          this.addError(`❌ 读取类型文件失败: ${fileName} - ${error.message}`);
        }
      } else {
        this.addError(`❌ 类型文件不存在: ${fileName}`);
      }
    });
  }

  /**
   * 验证资源结构
   */
  validateAssetStructure() {
    console.log('📋 验证资源结构...');
    
    const assetsDir = path.join(process.cwd(), 'assets');
    
    // 检查关键目录
    const keyDirectories = [
      'configs',
      'bundles',
      'scripts',
      'scenes',
      'prefabs'
    ];
    
    keyDirectories.forEach(dirName => {
      const dirPath = path.join(assetsDir, dirName);
      
      if (fs.existsSync(dirPath)) {
        this.addResult(`✅ 关键目录存在: assets/${dirName}`);
      } else {
        this.addError(`❌ 关键目录缺失: assets/${dirName}`);
      }
    });
    
    // 检查配置文件
    const configsDir = path.join(assetsDir, 'configs');
    if (fs.existsSync(configsDir)) {
      const configSubDirs = ['display', 'audio', 'localization', 'cache', 'system'];
      
      configSubDirs.forEach(subDir => {
        const subDirPath = path.join(configsDir, subDir);
        if (fs.existsSync(subDirPath)) {
          this.addResult(`✅ 配置子目录存在: configs/${subDir}`);
        } else {
          this.addError(`❌ 配置子目录缺失: configs/${subDir}`);
        }
      });
    }
  }

  /**
   * 验证构建配置
   */
  validateBuildConfig() {
    console.log('📋 验证构建配置...');
    
    const buildConfigPath = path.join(process.cwd(), 'build-config.json');
    
    if (fs.existsSync(buildConfigPath)) {
      try {
        const buildConfig = JSON.parse(fs.readFileSync(buildConfigPath, 'utf8'));
        
        // 检查平台配置
        const platforms = ['wechatgame', 'bytedance-mini-game'];
        
        platforms.forEach(platform => {
          if (buildConfig.buildConfigs && buildConfig.buildConfigs[platform]) {
            this.addResult(`✅ 构建配置存在: ${platform}`);
            
            const config = buildConfig.buildConfigs[platform];
            if (config.optimization) {
              this.addResult(`  ✅ 优化配置: ${Object.keys(config.optimization).join(', ')}`);
            }
          } else {
            this.addError(`❌ 构建配置缺失: ${platform}`);
          }
        });
        
      } catch (error) {
        this.addError(`❌ 构建配置解析失败: ${error.message}`);
      }
    } else {
      this.addError('❌ build-config.json文件不存在');
    }
  }

  /**
   * 添加成功结果
   */
  addResult(message) {
    this.results.push(message);
    console.log(message);
  }

  /**
   * 添加错误
   */
  addError(message) {
    this.errors.push(message);
    console.error(message);
  }

  /**
   * 打印验证结果
   */
  printResults() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 构建配置验证结果');
    console.log('='.repeat(60));
    
    console.log(`\n✅ 成功项目: ${this.results.length}`);
    console.log(`❌ 错误项目: ${this.errors.length}`);
    
    if (this.errors.length > 0) {
      console.log('\n🚨 需要修复的问题:');
      this.errors.forEach(error => console.log(`  ${error}`));
    }
    
    if (this.errors.length === 0) {
      console.log('\n🎉 所有验证通过！配置架构已正确设置。');
    } else {
      console.log('\n⚠️ 存在问题需要修复。');
    }
    
    console.log('\n' + '='.repeat(60));
  }
}

// 运行验证
const validator = new BuildConfigValidator();
validator.runValidation().catch(error => {
  console.error('验证过程出错:', error);
  process.exit(1);
});
