/**
 * 输入屏蔽测试脚本
 * 测试地图拖拽时按钮输入屏蔽功能
 */

import { _decorator, Component, Node, find, input, Input, EventKeyboard, KeyCode, Button } from 'cc';
import { DraggableMapContainer } from '../ui/components/DraggableMapContainer';

const { ccclass, property } = _decorator;

@ccclass('InputBlockingTest')
export class InputBlockingTest extends Component {
    
    @property({ tooltip: '是否启用测试' })
    public enableTest: boolean = true;
    
    @property({ tooltip: '是否自动添加输入屏蔽到按钮' })
    public autoAddInputBlocking: boolean = true;
    
    // 私有属性
    private _draggableContainer: DraggableMapContainer | null = null;
    private _testButtons: Node[] = [];

    protected onLoad(): void {
        if (!this.enableTest) return;
        
        console.log('🧪 InputBlockingTest: 输入屏蔽测试脚本加载');
        this.initializeComponents();
        this.setupKeyboardTest();
        
        if (this.autoAddInputBlocking) {
            this.scheduleOnce(() => {
                this.addInputBlockingToButtons();
            }, 1.0);
        }
    }

    protected onDestroy(): void {
        this.cleanupKeyboardTest();
    }

    /**
     * 初始化组件
     */
    private initializeComponents(): void {
        // 查找拖拽容器
        this._draggableContainer = find('Canvas/MainUI/BackPanel/LinearBranchMapContainer')?.getComponent(DraggableMapContainer);
        
        // 查找测试按钮
        this.findTestButtons();
        
        console.log('🧪 InputBlockingTest: 组件初始化完成', {
            draggableContainer: !!this._draggableContainer,
            buttonCount: this._testButtons.length
        });
    }

    /**
     * 查找测试按钮
     */
    private findTestButtons(): void {
        // 查找地图上的交互节点
        const mapContainer = find('Canvas/MainUI/BackPanel/LinearBranchMapContainer/MapContainer');
        if (mapContainer) {
            mapContainer.children.forEach(child => {
                const button = child.getComponent('cc.Button');
                if (button) {
                    this._testButtons.push(child);
                }
            });
        }
        
        // 查找其他UI按钮
        const uiButtons = [
            'Canvas/MainUI/TopPanel/SettingsButton',
            'Canvas/MainUI/BottomPanel/MenuButton',
            'Canvas/MainUI/RightPanel/InventoryButton'
        ];
        
        uiButtons.forEach(path => {
            const button = find(path);
            if (button && button.getComponent('cc.Button')) {
                this._testButtons.push(button);
            }
        });
        
        console.log('🧪 InputBlockingTest: 找到按钮', this._testButtons.length, '个');
    }

    /**
     * 为按钮添加输入屏蔽功能
     */
    private addInputBlockingToButtons(): void {
        // 简化版本：直接使用DraggableMapContainer的输入屏蔽功能
        console.log('🧪 InputBlockingTest: 输入屏蔽功能由DraggableMapContainer统一管理');
        console.log('🧪 InputBlockingTest: 找到', this._testButtons.length, '个按钮进行测试');
    }

    /**
     * 设置键盘测试
     */
    private setupKeyboardTest(): void {
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log('🧪 InputBlockingTest: 键盘测试设置完成');
    }

    /**
     * 清理键盘测试
     */
    private cleanupKeyboardTest(): void {
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
    }

    /**
     * 键盘按键处理
     */
    private onKeyDown(event: EventKeyboard): void {
        if (!this.enableTest) return;
        
        switch (event.keyCode) {
            case KeyCode.KEY_B:
                this.toggleInputBlocking();
                break;
            case KeyCode.KEY_N:
                this.testButtonClicks();
                break;
            case KeyCode.KEY_M:
                this.showInputStatus();
                break;
            case KeyCode.KEY_V:
                this.forceClickAllButtons();
                break;
            case KeyCode.KEY_X:
                this.clearAllBlocking();
                break;
        }
    }

    /**
     * 切换输入屏蔽状态
     */
    private toggleInputBlocking(): void {
        const currentState = DraggableMapContainer.isInputBlocked();
        DraggableMapContainer.setInputBlocking(!currentState);
        
        console.log('🧪 InputBlockingTest: 手动切换输入屏蔽', !currentState ? '开启' : '关闭');
    }

    /**
     * 测试按钮点击
     */
    private testButtonClicks(): void {
        console.log('🧪 InputBlockingTest: 开始测试按钮点击');
        
        this._testButtons.forEach((button, index) => {
            this.scheduleOnce(() => {
                console.log('🧪 尝试点击按钮:', button.name);
                
                // 模拟点击事件
                const buttonComponent = button.getComponent(Button);
                if (buttonComponent && buttonComponent.interactable) {
                    console.log(`🧪 模拟点击按钮: ${button.name}`);
                    // 简单的点击模拟
                    buttonComponent.node.emit('click');
                } else {
                    console.log(`🧪 按钮不可交互: ${button.name}`);
                }
            }, index * 0.5);
        });
    }

    /**
     * 显示输入状态
     */
    private showInputStatus(): void {
        const isBlocked = DraggableMapContainer.isInputBlocked();
        
        console.log('🧪 ========== 输入屏蔽状态 ==========');
        console.log('🚫 全局输入屏蔽:', isBlocked ? '开启' : '关闭');
        console.log('🎮 拖拽容器:', this._draggableContainer ? '存在' : '不存在');
        console.log('🔘 测试按钮数量:', this._testButtons.length);
        
        // 检查每个按钮的状态
        this._testButtons.forEach(button => {
            const buttonComponent = button.getComponent(Button);
            if (buttonComponent) {
                console.log(`🔘 ${button.name}: 按钮可交互: ${buttonComponent.interactable}`);
            } else {
                console.log(`🔘 ${button.name}: 无Button组件`);
            }
        });
        
        console.log('🧪 ================================');
    }

    /**
     * 强制点击所有按钮（忽略屏蔽）
     */
    private forceClickAllButtons(): void {
        console.log('🧪 InputBlockingTest: 强制点击所有按钮');

        this._testButtons.forEach(button => {
            const buttonComponent = button.getComponent(Button);
            if (buttonComponent) {
                console.log(`🧪 强制点击: ${button.name}`);
                buttonComponent.node.emit('click');
            }
        });
    }

    /**
     * 清除所有屏蔽
     */
    private clearAllBlocking(): void {
        console.log('🧪 InputBlockingTest: 清除所有输入屏蔽');
        
        DraggableMapContainer.clearInputBlocking();
        
        // 重置拖拽状态
        if (this._draggableContainer) {
            this._draggableContainer.resetDragState();
        }
    }

    /**
     * 显示测试说明
     */
    private showTestInstructions(): void {
        console.log('🧪 ========== 输入屏蔽测试说明 ==========');
        console.log('🎯 测试地图拖拽时按钮输入屏蔽功能');
        console.log('⌨️ 测试快捷键:');
        console.log('   B键 - 手动切换输入屏蔽状态');
        console.log('   N键 - 测试所有按钮点击');
        console.log('   M键 - 显示当前输入状态');
        console.log('   V键 - 强制点击所有按钮（忽略屏蔽）');
        console.log('   X键 - 清除所有屏蔽状态');
        console.log('🖱️ 测试步骤:');
        console.log('   1. 拖拽地图，观察按钮是否被屏蔽');
        console.log('   2. 停止拖拽，按钮应该恢复响应');
        console.log('   3. 使用快捷键进行各种测试');
        console.log('🧪 ====================================');
    }

    // ==================== 公共API ====================

    /**
     * 手动运行测试
     */
    public runTest(): void {
        this.showTestInstructions();
        this.showInputStatus();
    }

    /**
     * 添加按钮到测试列表
     */
    public addTestButton(button: Node): void {
        if (button && this._testButtons.indexOf(button) === -1) {
            this._testButtons.push(button);
            
            if (this.autoAddInputBlocking) {
                // 输入屏蔽由DraggableMapContainer统一管理
                console.log('🧪 InputBlockingTest: 输入屏蔽功能已集成到DraggableMapContainer');
            }
            
            console.log('🧪 InputBlockingTest: 添加测试按钮', button.name);
        }
    }

    /**
     * 获取测试结果
     */
    public getTestResults(): any {
        return {
            draggableContainer: !!this._draggableContainer,
            buttonCount: this._testButtons.length,
            inputBlocked: DraggableMapContainer.isInputBlocked(),
            timestamp: Date.now()
        };
    }
}
