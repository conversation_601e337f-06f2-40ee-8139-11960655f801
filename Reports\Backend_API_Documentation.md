# 后端API接口文档

## 📋 API概述

**基础URL**: 
- 本地环境: `http://localhost:3000`
- 远程环境: `https://your-aliyun-server.com`

**API版本**: v1  
**数据格式**: JSON  
**字符编码**: UTF-8  

## 🗺️ 地点配置API

### 1. 获取单个地点配置

**接口**: `GET /api/v1/locations/config/:locationId`

**描述**: 获取指定地点的配置信息

**请求参数**:
- `locationId` (路径参数): 地点ID，支持 Forest、Desert、Mountain
- `version` (查询参数): 配置版本，默认为 latest

**请求示例**:
```bash
GET /api/v1/locations/config/Forest?version=latest
```

**响应格式**:
```json
{
  "success": true,
  "message": "获取地点配置成功",
  "data": {
    "locationId": "Forest",
    "version": "v1.0.0",
    "config": {
      "locationId": "Forest",
      "name": "神秘森林",
      "description": "充满生机的森林地带，蕴藏着丰富的资源和神秘的生物",
      "mapConfig": {
        "backgroundImagePath": "bundles/locations/textures/forest_background",
        "nodeSpacing": { "x": 200, "y": 150 },
        "branchCount": 3,
        "nodesPerBranch": 5
      },
      "environment": {
        "ambientColor": "#2d5a3d",
        "fogColor": "#1a3d2e",
        "lightIntensity": 0.8
      },
      "uiElements": {
        "titleColor": "#4a7c59",
        "buttonStyle": "forest_theme"
      },
      "nodeData": [
        {
          "id": "forest_node_1",
          "position": { "x": 0, "y": 0 },
          "type": "resource",
          "resources": ["wood", "herbs"]
        }
      ]
    },
    "lastUpdated": "2025-08-09T08:00:00.000Z",
    "cacheExpiry": "2025-08-09T08:30:00.000Z"
  }
}
```

**错误响应**:
```json
{
  "success": false,
  "message": "地点配置不存在",
  "error": "LOCATION_CONFIG_NOT_FOUND"
}
```

### 2. 批量获取地点配置

**接口**: `POST /api/v1/locations/config/batch`

**描述**: 批量获取多个地点的配置信息

**请求体**:
```json
{
  "locationIds": ["Forest", "Desert", "Mountain"],
  "version": "latest"
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "批量获取地点配置成功",
  "data": {
    "configs": {
      "Forest": {
        "locationId": "Forest",
        "version": "v1.0.0",
        "config": { /* 完整配置数据 */ },
        "lastUpdated": "2025-08-09T08:00:00.000Z"
      },
      "Desert": {
        "locationId": "Desert",
        "version": "v1.0.0", 
        "config": { /* 完整配置数据 */ },
        "lastUpdated": "2025-08-09T08:00:00.000Z"
      }
    },
    "notFound": ["Mountain"],
    "cacheExpiry": "2025-08-09T08:30:00.000Z"
  }
}
```

### 3. 获取地点列表

**接口**: `GET /api/v1/locations/list`

**描述**: 获取所有可用地点的列表信息

**响应格式**:
```json
{
  "success": true,
  "message": "获取地点列表成功",
  "data": {
    "locations": [
      {
        "locationId": "Forest",
        "name": "神秘森林",
        "description": "充满生机的森林地带",
        "level": 1,
        "type": "resource",
        "version": "v1.0.0",
        "lastUpdated": "2025-08-09T08:00:00.000Z"
      },
      {
        "locationId": "Desert",
        "name": "炽热沙漠", 
        "description": "干燥炎热的沙漠地区",
        "level": 2,
        "type": "challenge",
        "version": "v1.0.0",
        "lastUpdated": "2025-08-09T08:00:00.000Z"
      }
    ],
    "total": 2
  }
}
```

### 4. 刷新配置缓存

**接口**: `POST /api/v1/locations/cache/refresh`

**描述**: 刷新地点配置缓存，重新加载配置文件

**响应格式**:
```json
{
  "success": true,
  "message": "地点配置缓存刷新成功",
  "data": {
    "cachedLocations": 3,
    "refreshedAt": "2025-08-09T08:15:00.000Z"
  }
}
```

### 5. 获取缓存状态

**接口**: `GET /api/v1/locations/cache/status`

**描述**: 获取当前缓存状态信息

**响应格式**:
```json
{
  "success": true,
  "message": "获取缓存状态成功",
  "data": {
    "initialized": true,
    "cachedLocations": 3,
    "locations": ["Forest", "Desert", "Mountain"],
    "lastInitialized": "2025-08-09T08:00:00.000Z"
  }
}
```

## 🏥 系统API

### 1. 健康检查

**接口**: `GET /health`

**描述**: 检查服务器健康状态

**响应格式**:
```json
{
  "success": true,
  "message": "服务器运行正常",
  "data": {
    "status": "healthy",
    "timestamp": "2025-08-09T08:00:00.000Z",
    "uptime": 3600,
    "version": "1.0.0",
    "environment": "development",
    "database": {
      "status": "connected",
      "connections": 5
    },
    "cache": {
      "status": "active",
      "locations": 3
    }
  }
}
```

### 2. API信息

**接口**: `GET /api/info`

**描述**: 获取API文档和版本信息

**响应格式**:
```json
{
  "success": true,
  "data": {
    "name": "江湖风放置游戏API",
    "version": "1.0.0",
    "description": "多人在线放置挂机游戏后端API",
    "endpoints": {
      "locations": "/api/v1/locations/*",
      "health": "/health",
      "docs": "/api/info"
    },
    "environment": "development",
    "timestamp": "2025-08-09T08:00:00.000Z"
  }
}
```

## 🔧 错误代码

### 通用错误代码
- `INTERNAL_SERVER_ERROR`: 服务器内部错误
- `INVALID_REQUEST`: 请求参数无效
- `UNAUTHORIZED`: 未授权访问
- `FORBIDDEN`: 禁止访问
- `NOT_FOUND`: 资源不存在

### 地点配置相关错误
- `MISSING_LOCATION_ID`: 缺少地点ID
- `LOCATION_CONFIG_NOT_FOUND`: 地点配置不存在
- `MISSING_LOCATION_IDS`: 缺少地点ID列表
- `CACHE_REFRESH_FAILED`: 缓存刷新失败

## 📊 响应状态码

- `200`: 请求成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `500`: 服务器内部错误

## 🔒 安全说明

### CORS配置
- **本地环境**: 允许所有来源 (`*`)
- **生产环境**: 限制特定域名

### 请求限制
- **频率限制**: 每分钟最多100次请求
- **请求大小**: 最大10MB
- **超时时间**: 30秒

## 📈 性能指标

### 响应时间
- **地点配置获取**: < 50ms (缓存命中)
- **地点列表查询**: < 100ms
- **批量配置获取**: < 200ms
- **健康检查**: < 10ms

### 缓存策略
- **缓存时间**: 30分钟
- **缓存命中率**: > 95%
- **自动刷新**: 支持手动和定时刷新

## 🧪 测试示例

### 使用curl测试
```bash
# 健康检查
curl -X GET http://localhost:3000/health

# 获取地点列表
curl -X GET http://localhost:3000/api/v1/locations/list

# 获取森林配置
curl -X GET http://localhost:3000/api/v1/locations/config/Forest

# 批量获取配置
curl -X POST http://localhost:3000/api/v1/locations/config/batch \
  -H "Content-Type: application/json" \
  -d '{"locationIds": ["Forest", "Desert"]}'

# 刷新缓存
curl -X POST http://localhost:3000/api/v1/locations/cache/refresh

# 查看缓存状态
curl -X GET http://localhost:3000/api/v1/locations/cache/status
```

### 使用JavaScript测试
```javascript
// 获取地点配置
async function getLocationConfig(locationId) {
  try {
    const response = await fetch(`/api/v1/locations/config/${locationId}`);
    const data = await response.json();
    
    if (data.success) {
      console.log('配置获取成功:', data.data.config);
      return data.data.config;
    } else {
      console.error('配置获取失败:', data.message);
      return null;
    }
  } catch (error) {
    console.error('网络请求失败:', error);
    return null;
  }
}

// 批量获取配置
async function getLocationConfigsBatch(locationIds) {
  try {
    const response = await fetch('/api/v1/locations/config/batch', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ locationIds })
    });
    
    const data = await response.json();
    return data.success ? data.data.configs : null;
  } catch (error) {
    console.error('批量获取失败:', error);
    return null;
  }
}
```

## 📋 更新日志

### v1.0.0 (2025-08-09)
- ✅ 初始版本发布
- ✅ 地点配置API完整实现
- ✅ 缓存机制和性能优化
- ✅ 错误处理和日志记录
- ✅ 环境配置和部署支持

### 计划更新
- [ ] v1.1.0: 添加用户认证
- [ ] v1.2.0: 实现配置版本管理
- [ ] v1.3.0: 添加实时数据推送
- [ ] v2.0.0: 微服务架构重构

## 📞 技术支持

如有API使用问题，请检查：
1. 服务器是否正常运行 (`/health`)
2. 请求格式是否正确
3. 网络连接是否正常
4. 环境配置是否正确

**调试工具**:
- 环境状态: `npm run env:status`
- 服务器日志: `npm run dev` 或 `pm2 logs`
- 缓存状态: `GET /api/v1/locations/cache/status`
