/**
 * 行为面板组件
 * 管理角色行为选择和行动条显示
 */

import { _decorator, Component, Node, Button, find } from 'cc';
import { ActionBar, ActionBarState, IActionBarConfig } from '../components/ActionBar';
import { EventManager } from '../../managers/EventManager';

const { ccclass, property } = _decorator;

/**
 * 行为类型枚举
 */
export enum BehaviorType {
    Idle = 'idle',
    Attack = 'attack',
    Defend = 'defend',
    Skill = 'skill',
    Item = 'item',
    Move = 'move'
}

/**
 * 行为数据接口
 */
export interface IBehaviorData {
    /** 行为类型 */
    type: BehaviorType;
    
    /** 行为名称 */
    name: string;
    
    /** 行为描述 */
    description: string;
    
    /** 执行时间（秒） */
    duration: number;
    
    /** 是否可中断 */
    interruptible: boolean;
    
    /** 冷却时间（秒） */
    cooldown?: number;
    
    /** 额外数据 */
    extraData?: any;
}

@ccclass('BehaviorPanel')
export class BehaviorPanel extends Component {
    
    @property({ type: Node, tooltip: '行动条容器节点' })
    public actionBarContainer: Node | null = null;
    
    @property({ type: ActionBar, tooltip: '行动条组件' })
    public actionBar: ActionBar | null = null;
    
    @property({ type: Node, tooltip: '行为按钮容器' })
    public behaviorButtonContainer: Node | null = null;
    
    @property({ type: [Button], tooltip: '行为按钮列表' })
    public behaviorButtons: Button[] = [];
    
    @property({ tooltip: '是否自动显示面板' })
    public autoShow: boolean = false;
    
    @property({ tooltip: '默认行为持续时间' })
    public defaultBehaviorDuration: number = 3.0;
    
    // 私有属性
    private _currentBehavior: IBehaviorData | null = null;
    private _isExecutingBehavior: boolean = false;
    private _behaviorQueue: IBehaviorData[] = [];
    
    // 预定义行为数据
    private _behaviorData: Map<string, IBehaviorData> = new Map();

    protected onLoad(): void {
        console.log('🎭 BehaviorPanel: 行为面板加载');
        this.initializeComponents();
        this.initializeBehaviorData();
        this.bindEvents();
    }

    protected start(): void {
        if (this.autoShow) {
            this.showPanel();
        }
    }

    protected onDestroy(): void {
        this.unbindEvents();
        this.stopCurrentBehavior();
    }

    /**
     * 初始化组件
     */
    private initializeComponents(): void {
        // 自动查找行动条容器
        if (!this.actionBarContainer) {
            this.actionBarContainer = this.node.getChildByName('ActionBarContainer');
        }
        
        // 自动查找行动条组件
        if (!this.actionBar) {
            if (this.actionBarContainer) {
                this.actionBar = this.actionBarContainer.getComponent(ActionBar);
                if (!this.actionBar) {
                    this.actionBar = this.actionBarContainer.getComponentInChildren(ActionBar);
                }
            } else {
                this.actionBar = this.node.getComponent(ActionBar);
                if (!this.actionBar) {
                    this.actionBar = this.node.getComponentInChildren(ActionBar);
                }
            }
        }
        
        // 自动查找行为按钮容器
        if (!this.behaviorButtonContainer) {
            this.behaviorButtonContainer = this.node.getChildByName('BehaviorButtons') || 
                                         this.node.getChildByName('ButtonContainer');
        }
        
        // 自动查找行为按钮
        if (this.behaviorButtons.length === 0 && this.behaviorButtonContainer) {
            this.behaviorButtons = this.behaviorButtonContainer.getComponentsInChildren(Button);
        }
        
        console.log('🎭 BehaviorPanel: 组件初始化完成', {
            actionBarContainer: !!this.actionBarContainer,
            actionBar: !!this.actionBar,
            behaviorButtonContainer: !!this.behaviorButtonContainer,
            behaviorButtonsCount: this.behaviorButtons.length
        });
    }

    /**
     * 初始化行为数据
     */
    private initializeBehaviorData(): void {
        // 定义默认行为
        const defaultBehaviors: IBehaviorData[] = [
            {
                type: BehaviorType.Attack,
                name: '攻击',
                description: '对目标进行普通攻击',
                duration: 2.0,
                interruptible: true
            },
            {
                type: BehaviorType.Defend,
                name: '防御',
                description: '提高防御力，减少受到的伤害',
                duration: 1.5,
                interruptible: true
            },
            {
                type: BehaviorType.Skill,
                name: '技能',
                description: '释放特殊技能',
                duration: 3.0,
                interruptible: false,
                cooldown: 5.0
            },
            {
                type: BehaviorType.Item,
                name: '道具',
                description: '使用道具',
                duration: 1.0,
                interruptible: true
            }
        ];
        
        // 存储行为数据
        defaultBehaviors.forEach(behavior => {
            this._behaviorData.set(behavior.type, behavior);
        });
        
        console.log('🎭 BehaviorPanel: 行为数据初始化完成', this._behaviorData.size);
    }

    /**
     * 绑定事件
     */
    private bindEvents(): void {
        // 绑定行为按钮事件
        this.behaviorButtons.forEach((button, index) => {
            if (button && button.node) {
                button.node.on(Button.EventType.CLICK, () => {
                    this.onBehaviorButtonClick(button, index);
                }, this);
            }
        });
        
        // 绑定行动条事件
        if (this.actionBar) {
            this.actionBar.setOnCompleteCallback(() => {
                this.onBehaviorComplete();
            });
            
            this.actionBar.setOnProgressCallback((progress: number) => {
                this.onBehaviorProgress(progress);
            });
        }
        
        console.log('🎭 BehaviorPanel: 事件绑定完成');
    }

    /**
     * 解绑事件
     */
    private unbindEvents(): void {
        // 解绑行为按钮事件
        this.behaviorButtons.forEach(button => {
            if (button && button.node) {
                button.node.off(Button.EventType.CLICK);
            }
        });
        
        console.log('🎭 BehaviorPanel: 事件解绑完成');
    }

    /**
     * 行为按钮点击处理
     */
    private onBehaviorButtonClick(button: Button, index: number): void {
        console.log('🎭 BehaviorPanel: 行为按钮点击', { buttonName: button.node.name, index });
        
        // 根据按钮名称或索引确定行为类型
        let behaviorType: BehaviorType;
        const buttonName = button.node.name.toLowerCase();
        
        if (buttonName.includes('attack') || buttonName.includes('攻击')) {
            behaviorType = BehaviorType.Attack;
        } else if (buttonName.includes('defend') || buttonName.includes('防御')) {
            behaviorType = BehaviorType.Defend;
        } else if (buttonName.includes('skill') || buttonName.includes('技能')) {
            behaviorType = BehaviorType.Skill;
        } else if (buttonName.includes('item') || buttonName.includes('道具')) {
            behaviorType = BehaviorType.Item;
        } else {
            // 根据索引分配默认行为
            const behaviorTypes = [BehaviorType.Attack, BehaviorType.Defend, BehaviorType.Skill, BehaviorType.Item];
            behaviorType = behaviorTypes[index % behaviorTypes.length];
        }
        
        this.executeBehavior(behaviorType);
    }

    /**
     * 执行行为
     */
    public executeBehavior(behaviorType: BehaviorType, customData?: Partial<IBehaviorData>): void {
        if (this._isExecutingBehavior) {
            console.warn('🎭 BehaviorPanel: 正在执行其他行为，无法开始新行为');
            return;
        }
        
        const behaviorData = this._behaviorData.get(behaviorType);
        if (!behaviorData) {
            console.error('🎭 BehaviorPanel: 未找到行为数据', behaviorType);
            return;
        }
        
        // 合并自定义数据
        const finalBehaviorData: IBehaviorData = { ...behaviorData, ...customData };
        
        console.log('🎭 BehaviorPanel: 开始执行行为', finalBehaviorData);
        
        this._currentBehavior = finalBehaviorData;
        this._isExecutingBehavior = true;
        
        // 配置并启动行动条
        if (this.actionBar) {
            const actionBarConfig: IActionBarConfig = {
                duration: finalBehaviorData.duration,
                showText: true,
                textFormat: `${finalBehaviorData.name}: {current}s / {total}s`,
                enableAnimation: true,
                animationType: 'smooth'
            };
            
            this.actionBar.configure(actionBarConfig);
            this.actionBar.startProgress();
        }
        
        // 禁用行为按钮（如果行为不可中断）
        if (!finalBehaviorData.interruptible) {
            this.setButtonsEnabled(false);
        }
        
        // 发送行为开始事件
        EventManager.getInstance().emit('behavior_start', {
            behaviorType: behaviorType,
            behaviorData: finalBehaviorData
        });
    }

    /**
     * 停止当前行为
     */
    public stopCurrentBehavior(): void {
        if (!this._isExecutingBehavior) {
            return;
        }
        
        console.log('🎭 BehaviorPanel: 停止当前行为');
        
        if (this.actionBar) {
            this.actionBar.stopProgress();
        }
        
        this._isExecutingBehavior = false;
        this._currentBehavior = null;
        
        // 重新启用按钮
        this.setButtonsEnabled(true);
        
        // 发送行为停止事件
        EventManager.getInstance().emit('behavior_stop', {});
    }

    /**
     * 行为进度更新
     */
    private onBehaviorProgress(progress: number): void {
        // 发送进度更新事件
        EventManager.getInstance().emit('behavior_progress', {
            progress: progress,
            behaviorData: this._currentBehavior
        });
    }

    /**
     * 行为完成处理
     */
    private onBehaviorComplete(): void {
        console.log('🎭 BehaviorPanel: 行为执行完成', this._currentBehavior);
        
        const completedBehavior = this._currentBehavior;
        this._isExecutingBehavior = false;
        this._currentBehavior = null;
        
        // 重新启用按钮
        this.setButtonsEnabled(true);
        
        // 发送行为完成事件
        EventManager.getInstance().emit('behavior_complete', {
            behaviorData: completedBehavior
        });
        
        // 处理行为队列
        this.processNextBehavior();
    }

    /**
     * 处理下一个行为
     */
    private processNextBehavior(): void {
        if (this._behaviorQueue.length > 0) {
            const nextBehavior = this._behaviorQueue.shift();
            if (nextBehavior) {
                this.scheduleOnce(() => {
                    this.executeBehavior(nextBehavior.type, nextBehavior);
                }, 0.1);
            }
        }
    }

    /**
     * 设置按钮启用状态
     */
    private setButtonsEnabled(enabled: boolean): void {
        this.behaviorButtons.forEach(button => {
            if (button && button.node) {
                button.interactable = enabled;
            }
        });
    }

    // ==================== 公共API ====================

    /**
     * 显示面板
     */
    public showPanel(): void {
        console.log('🎭 BehaviorPanel: 显示面板');
        this.node.active = true;
        
        // 发送面板显示事件
        EventManager.getInstance().emit('behavior_panel_show', {});
    }

    /**
     * 隐藏面板
     */
    public hidePanel(): void {
        console.log('🎭 BehaviorPanel: 隐藏面板');
        this.node.active = false;
        
        // 停止当前行为
        this.stopCurrentBehavior();
        
        // 发送面板隐藏事件
        EventManager.getInstance().emit('behavior_panel_hide', {});
    }

    /**
     * 添加行为到队列
     */
    public queueBehavior(behaviorType: BehaviorType, customData?: Partial<IBehaviorData>): void {
        const behaviorData = this._behaviorData.get(behaviorType);
        if (!behaviorData) {
            console.error('🎭 BehaviorPanel: 未找到行为数据', behaviorType);
            return;
        }
        
        const finalBehaviorData: IBehaviorData = { ...behaviorData, ...customData };
        this._behaviorQueue.push(finalBehaviorData);
        
        console.log('🎭 BehaviorPanel: 行为已添加到队列', finalBehaviorData.name);
    }

    /**
     * 清空行为队列
     */
    public clearBehaviorQueue(): void {
        this._behaviorQueue = [];
        console.log('🎭 BehaviorPanel: 行为队列已清空');
    }

    /**
     * 获取当前行为
     */
    public getCurrentBehavior(): IBehaviorData | null {
        return this._currentBehavior;
    }

    /**
     * 是否正在执行行为
     */
    public isExecutingBehavior(): boolean {
        return this._isExecutingBehavior;
    }

    /**
     * 获取行为队列长度
     */
    public getQueueLength(): number {
        return this._behaviorQueue.length;
    }

    /**
     * 注册自定义行为
     */
    public registerBehavior(behaviorData: IBehaviorData): void {
        this._behaviorData.set(behaviorData.type, behaviorData);
        console.log('🎭 BehaviorPanel: 注册自定义行为', behaviorData.name);
    }
}
